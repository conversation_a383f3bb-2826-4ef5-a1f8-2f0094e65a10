<aura:component implements="lightning:actionOverride, lightning:hasPageReference, force:lightningQuickActionWithoutHeader, flexipage:availableForRecordHome, force:hasRecordId, force:hasSObjectName" 
                controller="OpportunityEditOverrideController">
    
    <aura:attribute name="recordId" type="String" />
    <lightning:navigation aura:id="navService"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    
</aura:component>
