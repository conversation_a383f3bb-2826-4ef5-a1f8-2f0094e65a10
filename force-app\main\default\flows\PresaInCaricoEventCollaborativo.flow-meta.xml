<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>Check_Current_Event</name>
        <label>Check Current Event</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Aggiornamento_non_eseguito</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Current Event NON Present</defaultConnectorLabel>
        <rules>
            <name>Current_Event_Present</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Current_Event</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Status</targetReference>
            </connector>
            <label>Current Event Present</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>PresaInCaricoEventCollaborativo {!$Flow.CurrentDateTime}</interviewLabel>
    <label>PresaInCaricoEventCollaborativo</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Current_Event</name>
        <label>Get Current Event</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Current_Event</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Event</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Status</name>
        <label>Update Status and OtherInformations</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Aggiornamento_con_successo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Group__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>ShowAs</field>
            <value>
                <stringValue>Busy</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Preso_in_carico</stringValue>
            </value>
        </inputAssignments>
        <object>Event</object>
    </recordUpdates>
    <screens>
        <name>Aggiornamento_con_successo</name>
        <label>Aggiornamento con successo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Output</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Aggionamento effettuato con successo !&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>OK</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Aggiornamento_non_eseguito</name>
        <label>Aggiornamento non eseguito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_Output</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;Non è possibile eseguire l&apos;aggiornamento.&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong&gt;L&apos;appuntamento potrebbe essere stato cancellato.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>OK</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Current_Event</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
