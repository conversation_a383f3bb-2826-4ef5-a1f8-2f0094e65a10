({
    doInit : function(component, event, helper) {
        var action = component.get("c.checkEditAccess");
        action.setParams({
            opportunityId: component.get("v.recordId")
        });
        console.log('####In doinit');
        action.setCallback(this, function(response) {
            if (response.getState() === "SUCCESS") {
                var result = response.getReturnValue();
                var navService = component.find("navService");
                console.log('@@@In action setcallback');
                
                if(result.isEditAllowed) {
                    // Usa l'URL diretto con nooverride per evitare il loop
                    // var baseUrl = window.location.origin;
                    // var editUrl = baseUrl + '/lightning/r/Opportunity/' + 
                    //             component.get("v.recordId") + '/edit?nooverride=1';
                    // window.location.href = editUrl;
                    console.log('@@@Inside IF before firing editRecordEvent');

                    // var editRecordEvent = $A.get("e.force:editRecord"); 
                    // editRecordEvent.setParams({ "recordId": component.get("v.recordId") }); 
                    // editRecordEvent.fire();
                    // console.log('Edit Record Event fired for recordId: ' + component.get("v.recordId"));

                    // var baseUrl = window.location.origin;
                    // var editUrl = baseUrl + '/lightning/r/Opportunity/' + 
                    //             component.get("v.recordId") + '/edit?nooverride=1';
                    // window.location.replace(editUrl);
                    console.log('Edit with navservice');
                    var navService = component.find("navService");
                    var pageReference = {
                        type: 'standard__recordPage',
                        attributes: {
                            recordId: component.get("v.recordId"),
                            objectApiName: 'Opportunity',
                            actionName: 'edit'
                        },
                        state: {
                            nooverride: '1'
                        }
                    };
                    navService.navigate(pageReference);
                    
                } else {
                    // Mostra messaggio di warning
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Attenzione!",
                        "type": "warning",
                        "message": result.message,
                        "duration": 5000
                    });
                    toastEvent.fire();
                    
                    // Redirect alla pagina di visualizzazione
                    navService.navigate({
                        type: 'standard__recordPage',
                        attributes: {
                            recordId: component.get("v.recordId"),
                            objectApiName: 'Opportunity',
                            actionName: 'view'
                        }
                    });
                }
            } else if (response.getState() === "ERROR") {
                var errors = response.getError();
                var message = 'Errore sconosciuto';
                if (errors && Array.isArray(errors) && errors.length > 0) {
                    message = errors[0].message;
                }
                
                // Mostra errore
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Errore",
                    "type": "error",
                    "message": message,
                    "duration": 5000
                });
                toastEvent.fire();
                
                // Torna alla visualizzazione
                var navService = component.find("navService");
                navService.navigate({
                    type: 'standard__recordPage',
                    attributes: {
                        recordId: component.get("v.recordId"),
                        objectApiName: 'Opportunity',
                        actionName: 'view'
                    }
                });
            }
        });
        
        $A.enqueueAction(action);
    }
})
