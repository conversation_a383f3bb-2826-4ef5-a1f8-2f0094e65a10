({
    doInit : function(component, event, helper) {
        var action = component.get("c.isNewAccountAllowed");
        action.setParams({
            accountId: component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            if (response.getState() === "SUCCESS") {
                var result = response.getReturnValue();
                if (!result || !result.isAllowed || !result.recordTypes || result.recordTypes.length === 0) {
                    // Nessun recordType "ur_" trovato: redirect alla pagina di dettaglio
                    var navEvt = $A.get("e.force:navigateToURL");
                    navEvt.setParams({
                        "url": "/lightning/r/Account/" + component.get("v.recordId") + "/view"
                    });
                    navEvt.fire();
                } else {
                    // Almeno un recordType "ur_" trovato: chiama la action di edit standard
                    // var editRecordEvent = $A.get("e.force:editRecord");
                    // editRecordEvent.setParams({ "recordId": component.get("v.recordId") });
                    // editRecordEvent.fire();
                     console.log('Edit with navservice');
                    try {
                        // Log: inizio navigazione
                        console.log('DEBUG: Inizio navigazione edit Account');
                        var navService = component.find("navService");
                        var pageReference = {
                            type: 'standard__recordPage',
                            attributes: {
                                recordId: component.get("v.recordId"),
                                objectApiName: 'Account',
                                actionName: 'edit'
                            },
                            state: {
                                nooverride: '1'
                            }
                        };
                        navService.navigate(pageReference);
                        console.log('DEBUG: Navigazione effettuata', pageReference);
                    } catch (err) {
                        console.error('ERROR: Navigazione edit fallita', err, err && err.stack ? err.stack : '[no stack]');
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Errore JS",
                            "message": "Errore durante la navigazione: " + err.message,
                            "type": "error"
                        });
                        toastEvent.fire();
                    }
                }
            } else {
                // In caso di errore, mostra solo un toast event
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Errore",
                    "message": "Impossibile modificare il record. Riprovare o contattare l'amministratore.",
                    "type": "error"
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
    }
})