<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <assignments>
        <name>Payload10</name>
        <label>Payload10</label>
        <locationX>3306</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadUNICACESSIONE</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload11</name>
        <label>Payload11</label>
        <locationX>3570</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadUNICAASSOCIAPAGAMENTO</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload12</name>
        <label>Payload12</label>
        <locationX>3834</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadUNICACONTATTITELEMATICA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload13</name>
        <label>Payload13</label>
        <locationX>4098</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadUNICAVARIAZIONETEMPORANEA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload14</name>
        <label>Payload14</label>
        <locationX>4362</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadUNICASTORNOCESSAZIONE</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_1</name>
        <label>Payload 1</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadVITAPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_2</name>
        <label>Payload 2</label>
        <locationX>1018</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadREVARIAZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_3</name>
        <label>Payload 3</label>
        <locationX>1282</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadRESOSTITUZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_4</name>
        <label>Payload 4</label>
        <locationX>1722</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadRAVARIAZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_5</name>
        <label>Payload 5</label>
        <locationX>1986</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadRASOSTITUZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_6</name>
        <label>Payload 6</label>
        <locationX>2426</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadNPACSOSTITUZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_7</name>
        <label>Payload 7</label>
        <locationX>2866</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadIPINTERROGAZIONEPOLIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_8</name>
        <label>Payload 8</label>
        <locationX>4802</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadSXCCINTERROGAZIONE</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Payload_9</name>
        <label>Payload 9</label>
        <locationX>5506</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadCONULTAZIONECONTRATTO</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadSXCCINSERIMENTO</name>
        <label>SetPayloadSXCCINSERIMENTO</label>
        <locationX>5066</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PayloadSXCCINSERIMENTO</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadVITALAVORA</name>
        <label>SetPayloadVITALAVORA</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadVITALAVORA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadVITAVISUALIZZA</name>
        <label>SetPayloadVITAVISUALIZZA</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadVITAVISUALIZZA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>CheckFEI</name>
        <label>CheckFEI</label>
        <locationX>5638</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CONSULTAZIONE_CONTRATTO</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>GetCurrentInsurancePolicy.PolicyName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CONSULTAZIONE_CONTRATTO</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CONSULTAZIONE_CONTRATTO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_9</targetReference>
            </connector>
            <label>CONSULTAZIONE_CONTRATTO</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_IP</name>
        <label>CheckFEI IP</label>
        <locationX>2998</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IP_INTERROGAZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>IP.INTERROGAZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_7</targetReference>
            </connector>
            <label>IP.INTERROGAZIONE.POLIZZA</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_NPAC</name>
        <label>CheckFEI NPAC</label>
        <locationX>2558</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NPAC_SOSTITUZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>NPAC.SOSTITUZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_6</targetReference>
            </connector>
            <label>NPAC.SOSTITUZIONE.POLIZZA</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_RA</name>
        <label>CheckFEI RA</label>
        <locationX>1986</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RA_VARIAZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RA.VARIAZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_4</targetReference>
            </connector>
            <label>RA.VARIAZIONE.POLIZZA</label>
        </rules>
        <rules>
            <name>RA_SOSTITUZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RA.SOSTITUZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_5</targetReference>
            </connector>
            <label>RA.SOSTITUZIONE.POLIZZA</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_RE</name>
        <label>CheckFEI RE</label>
        <locationX>1282</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RE_VARIAZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RE.VARIAZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_2</targetReference>
            </connector>
            <label>RE.VARIAZIONE.POLIZZA</label>
        </rules>
        <rules>
            <name>RE_SOSTITUZIONE_POLIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RE.SOSTITUZIONE.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_3</targetReference>
            </connector>
            <label>RE.SOSTITUZIONE.POLIZZA</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_SXCC</name>
        <label>CheckFEI SXCC</label>
        <locationX>5066</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SXCC_INTERROGAZIONE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SXCC.INTERROGAZIONE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_8</targetReference>
            </connector>
            <label>SXCC.INTERROGAZIONE</label>
        </rules>
        <rules>
            <name>SXCC_INSERIMENTO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>SXCC.INSERIMENTO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadSXCCINSERIMENTO</targetReference>
            </connector>
            <label>SXCC.INSERIMENTO</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_UNICA</name>
        <label>CheckFEI UNICA</label>
        <locationX>3966</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>UNICA_CESSIONE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.CESSIONE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload10</targetReference>
            </connector>
            <label>UNICA.CESSIONE</label>
        </rules>
        <rules>
            <name>UNICA_ASSOCIA_METODO_PAGAMENTO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.ASSOCIA.METODO.PAGAMENTO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload11</targetReference>
            </connector>
            <label>UNICA.ASSOCIA.METODO.PAGAMENTO</label>
        </rules>
        <rules>
            <name>UNICA_CONTATTI_TELEMATICA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.CONTATTI.TELEMATICA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload12</targetReference>
            </connector>
            <label>UNICA.CONTATTI.TELEMATICA</label>
        </rules>
        <rules>
            <name>UNICA_VARIAZIONE_TEMPORANEA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.VARIAZIONE.TEMPORANEA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload13</targetReference>
            </connector>
            <label>UNICA.VARIAZIONE.TEMPORANEA</label>
        </rules>
        <rules>
            <name>UNICA_STORNO_CESSAZIONE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.STORNO.CESSAZIONE</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload14</targetReference>
            </connector>
            <label>UNICA.STORNO.CESSAZIONE</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckFEI_VITA</name>
        <label>CheckFEI_VITA</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>VITA_POLIZZA</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>GetCurrentInsurancePolicy.PolicyName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Payload_1</targetReference>
            </connector>
            <label>VITA.POLIZZA</label>
        </rules>
        <rules>
            <name>VITA_VISUALIZZA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.VISUALIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadVITAVISUALIZZA</targetReference>
            </connector>
            <label>VITA.VISUALIZZA</label>
        </rules>
        <rules>
            <name>VITA_LAVORA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.LAVORA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadVITALAVORA</targetReference>
            </connector>
            <label>VITA.LAVORA</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckType</name>
        <label>CheckType</label>
        <locationX>3042</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>CheckFEI</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>VITA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>VITA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_VITA</targetReference>
            </connector>
            <label>VITA</label>
        </rules>
        <rules>
            <name>RE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>RE.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_RE</targetReference>
            </connector>
            <label>RE</label>
        </rules>
        <rules>
            <name>RA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>RA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_RA</targetReference>
            </connector>
            <label>RA</label>
        </rules>
        <rules>
            <name>NPAC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>NPAC.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_NPAC</targetReference>
            </connector>
            <label>NPAC</label>
        </rules>
        <rules>
            <name>IP</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>IP.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_IP</targetReference>
            </connector>
            <label>IP</label>
        </rules>
        <rules>
            <name>UNICA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>UNICA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_UNICA</targetReference>
            </connector>
            <label>UNICA</label>
        </rules>
        <rules>
            <name>SXCC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>SXCC.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckFEI_SXCC</targetReference>
            </connector>
            <label>SXCC</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>dataEffetto</name>
        <dataType>String</dataType>
        <expression>TEXT(YEAR(TODAY())) &amp; &apos;-&apos; &amp; IF(MONTH(TODAY())&gt;9,TEXT(MONTH(TODAY())),&apos;0&apos; &amp; TEXT(MONTH(TODAY()))) &amp; &apos;-&apos; &amp; IF(DAY(TODAY())&gt;9,TEXT(DAY(TODAY())),&apos;0&apos; &amp; TEXT(DAY(TODAY())))</expression>
    </formulas>
    <formulas>
        <name>environment</name>
        <dataType>String</dataType>
        <expression>{!$Setup.FEI_Environment__c.Environment__c}</expression>
    </formulas>
    <formulas>
        <name>PayloadCONULTAZIONECONTRATTO</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;contractid&quot;:&quot;&apos; &amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp; &apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadIPINTERROGAZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;agenzia&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;,&quot;compagnia&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;,&quot;polizza&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;,&quot;ramo&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadNPACSOSTITUZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{ &quot;agenzia&quot;: &quot;{$agenzia}&quot;, &quot;agenziaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;, &quot;codiceFiscale&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.NameInsured.CF__c}&amp;&apos;&quot;, &quot;compagnia&quot;: &quot;{$compagnia}&quot;, &quot;compagniaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;, &quot;polizzaSostituita&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;, &quot;prodotto&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.Product__c}&amp;&apos;&quot;, &quot;ramoSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;, &quot;tipoOperazione&quot;: &quot;SO&quot;, &quot;subAgenzia&quot;: &quot;&apos;&amp;RIGHT(&apos;00000&apos; + {!GetCurrentInsurancePolicy.CIP__c},5)&amp;&apos;&quot; }&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadRASOSTITUZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{ &quot;agenzia&quot;: &quot;{$agenzia}&quot;, &quot;agenziaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;, &quot;codiceFiscale&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.NameInsured.ExternalId__c}&amp;&apos;&quot;, &quot;compagnia&quot;: &quot;{$compagnia}&quot;, &quot;compagniaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;, &quot;polizzaSostituita&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;, &quot;prodotto&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.Product__c}&amp;&apos;&quot;, &quot;ramoSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;, &quot;tipoOperazione&quot;: &quot;SO&quot;, &quot;subAgenzia&quot;: &quot;&apos;&amp;RIGHT(&apos;00000&apos; + {!GetCurrentInsurancePolicy.CIP__c},5)&amp;&apos;&quot; }&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadRAVARIAZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{ &quot;agenzia&quot;: &quot;{$agenzia}&quot;, &quot;agenziaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;, &quot;codiceFiscale&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.NameInsured.ExternalId__c}&amp;&apos;&quot;, &quot;compagnia&quot;: &quot;{$compagnia}&quot;, &quot;compagniaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;, &quot;polizzaSostituita&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;, &quot;prodotto&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.Product__c}&amp;&apos;&quot;, &quot;ramoSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;, &quot;tipoOperazione&quot;: &quot;VA&quot;, &quot;subAgenzia&quot;: &quot;&apos;&amp;RIGHT(&apos;00000&apos; + {!GetCurrentInsurancePolicy.CIP__c},5)&amp;&apos;&quot; }&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadRESOSTITUZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{ &quot;agenzia&quot;: &quot;{$agenzia}&quot;, &quot;agenziaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;, &quot;codiceFiscale&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.NameInsured.ExternalId__c}&amp;&apos;&quot;, &quot;compagnia&quot;: &quot;{$compagnia}&quot;, &quot;compagniaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;, &quot;polizzaSostituita&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;, &quot;prodotto&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.Product__c}&amp;&apos;&quot;, &quot;ramoSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;, &quot;tipoOperazione&quot;: &quot;SO&quot;, &quot;subAgenzia&quot;: &quot;&apos;&amp;RIGHT(&apos;00000&apos; + {!GetCurrentInsurancePolicy.CIP__c},5)&amp;&apos;&quot; }&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadREVARIAZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{ &quot;agenzia&quot;: &quot;{$agenzia}&quot;, &quot;agenziaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.AgencyCode__c}&amp;&apos;&quot;, &quot;codiceFiscale&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.NameInsured.ExternalId__c}&amp;&apos;&quot;, &quot;compagnia&quot;: &quot;{$compagnia}&quot;, &quot;compagniaSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.CompanyCode__c}&amp;&apos;&quot;, &quot;polizzaSostituita&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;, &quot;prodotto&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.Product__c}&amp;&apos;&quot;, &quot;ramoSostituita&quot;: &quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;, &quot;tipoOperazione&quot;: &quot;VA&quot;, &quot;subAgenzia&quot;: &quot;&apos;&amp;RIGHT(&apos;00000&apos; + {!GetCurrentInsurancePolicy.CIP__c},5)&amp;&apos;&quot; }&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadSXCCINSERIMENTO</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;agenzia&quot;:&quot;{$agenzia}&quot;,&quot;compagnia&quot;:&quot;{$compagnia}&quot;,&quot;numPolizza&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp;&apos;&quot;,&quot;ramo&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;&apos;&quot;,&quot;tipoEvento&quot;:&quot;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadSXCCINTERROGAZIONE</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;id&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.Name}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadUNICAASSOCIAPAGAMENTO</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;folderId&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadUNICACESSIONE</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;folderId&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp;&apos;&quot;,&quot;position&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.Position__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadUNICACONTATTITELEMATICA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;folderId&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp;&apos;&quot;,&quot;position&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.Position__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadUNICASTORNOCESSAZIONE</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;folderId&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp;&apos;&quot;,&quot;position&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.Position__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadUNICAVARIAZIONETEMPORANEA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;folderId&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.FolderId__c}&amp;&apos;&quot;,&quot;position&quot;:&quot;&apos;&amp;{!GetCurrentInsurancePolicy.Position__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadVITALAVORA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;idPolizza&quot;:&quot;&apos; &amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadVITAPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;chiamante&quot;: &quot;NFEV&quot;,&quot;funzione&quot;: &quot;5&quot;,&quot;polizza&quot;: &quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;,&quot;compagnia&quot;: &quot;{$compagnia}&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PayloadVITASOSTITUZIONEPOLIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;idPolizza&quot;:&quot;&apos; &amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadVITAVISUALIZZA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;idPolizza&quot;:&quot;&apos; &amp;{!GetCurrentInsurancePolicy.PolicyBranchCode__c}&amp;{!GetCurrentInsurancePolicy.ReferencePolicyNumber}&amp; &apos;&quot;,&quot;compagnia&quot;:&quot;{$compagnia}&quot;,&quot;dataEffetto&quot;:&quot;&apos; &amp; {!dataEffetto} &amp; &apos;&quot;,&quot;chiamante&quot;:&quot;VITA&quot;}&apos;</expression>
    </formulas>
    <interviewLabel>FEIQuickAction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>FEIQuickAction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetCurrentFEISetting</name>
        <label>GetCurrentFEISetting</label>
        <locationX>3042</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckType</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Label</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>FEIID</elementReference>
            </value>
        </filters>
        <filters>
            <field>Environment__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>environment</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FEI_Settings__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCurrentInsurancePolicy</name>
        <label>GetCurrentInsurancePolicy</label>
        <locationX>3042</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetCurrentFEISetting</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>InsurancePolicy</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>FeiContainerScreen</name>
        <label>FeiContainerScreen</label>
        <locationX>3042</locationX>
        <locationY>842</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FeiContainer</name>
            <extensionName>c:feiContainer</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>feiRequestPayload</name>
                <value>
                    <elementReference>feiRequestPayload</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FEIID</name>
                <value>
                    <elementReference>FEIID</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FiscalCode</name>
                <value>
                    <elementReference>$User.FederationIdentifier</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>permissionSetName</name>
                <value>
                    <elementReference>GetCurrentFEISetting.UCA_Permission_Name__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>feiAddressEndpointPayload</name>
                <value>
                    <elementReference>feiAddressEndpointPayload</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>society</name>
                <value>
                    <elementReference>society</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>2916</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCurrentInsurancePolicy</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>feiAddressEndpointPayload</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>FEIID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>feiRequestPayload</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>society</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
