public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {
    public class NPCResponse {
        public Boolean isAllowed = false;
        public List<Map<String, String>> recordTypes;
    }
    public NewPermissionChecker() {

    }

    @AuraEnabled
    public static String isNewCaseAllowed(){
        NPCResponse resp = new NPCResponse();
        // System.debug('isNewCaseAllowed: ' + FeatureManagement.checkPermission('Allow_Case_Creation'));
        // resp.isAllowed = FeatureManagement.checkPermission('Allow_Case_Creation');
        System.debug('isNewCaseAllowed: ' +Schema.sObjectType.Case.isCreateable());
        resp.isAllowed =Schema.sObjectType.Case.isCreateable();
        if(resp.isAllowed) {
            resp.recordTypes = getCaseRTs();
        }
        System.debug('resp: ' + resp);
        return JSON.serialize(resp);
    }
    

    @AuraEnabled
    public static List<Map<String, String>> getCaseRTs(){
        System.debug('Inside getCaseRTs ');
        return getAvailableRecordTypeNamesForSObject(Case.SObjectType);
    }


    // Returns a List of the Names of all RecordTypes
    // available to the running user for a given SOBject type
    public static List<Map<String, String>> getAvailableRecordTypeNamesForSObject(Schema.SObjectType objType) {
        System.debug('Inside getAvailableRecordTypeNamesForSObject ');
        String resp = '';
        List<Map<String,String>> rtMap = new List<Map<String,String>>();
        List<RecordTypeInfo> infos = objType.getDescribe().getRecordTypeInfos();
        // If there are 2 or more RecordTypes...
        if (infos.size() > 1) {
            System.debug('Inside first IF ');
            for (RecordTypeInfo i : infos) {
                // Ignore the Master Record Type, whose Id always ends with 'AAA'.
                // We check the Id because Name can change depending on the user's language.
                System.debug('Inside iteration ' + i.getName());
                
                if (i.isAvailable() && !String.valueOf(i.getRecordTypeId()).endsWith('AAA')) {
                    rtMap.add(new Map<String,String>{ 'Name' => i.getName(), 'Id' => i.getRecordTypeId() });
                }
            }
        } 
        // Otherwise there's just the Master record type,
        // so add it in, since it MUST always be available
        else {
            System.debug('Inside ELSE');
            //rtMap.put(infos[0].getName(),infos[0].getRecordTypeId());
            rtMap.add(new Map<String,String>{ 'Name' => infos[0].getName(), 'Id' => infos[0].getRecordTypeId() });
        }
        return rtMap;
    }
}