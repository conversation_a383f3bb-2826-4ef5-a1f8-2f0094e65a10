import { LightningElement, api, track, wire } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { NavigationMixin } from "lightning/navigation";
import { getData<PERSON>and<PERSON> } from "omnistudio/utility";
import hasCustomPermission from "@salesforce/customPermission/X169_506000000";
import hasVariazionePermission1 from "@salesforce/customPermission/X172_201030200";
import hasVariazionePermission2 from "@salesforce/customPermission/X172_201030300";
import hasVariazionePermission3 from "@salesforce/customPermission/X172_201030400";
import hasVariazionePermission4 from "@salesforce/customPermission/X172_201030500";
import hasVariazionePermission5 from "@salesforce/customPermission/X172_201030600";
import hasVariazionePermission6 from "@salesforce/customPermission/X172_201030700";
import hasVariazionePermission7 from "@salesforce/customPermission/X172_201030800";
import hasVariazionePermission8 from "@salesforce/customPermission/X172_201030900";
import hasVariazionePermission9 from "@salesforce/customPermission/X172_201031000";
import hasVariazionePermission10 from "@salesforce/customPermission/X172_202020000";
import hasVariazionePermission11 from "@salesforce/customPermission/X172_202020100";
import hasVariazionePermission12 from "@salesforce/customPermission/X172_202020101";
import hasVariazionePermission13 from "@salesforce/customPermission/X172_202020102";
import hasVariazionePermission14 from "@salesforce/customPermission/X172_202020103";
import hasVariazionePermission15 from "@salesforce/customPermission/X172_202020104";
import hasVariazionePermission16 from "@salesforce/customPermission/X172_202020105";
import hasVariazionePermission17 from "@salesforce/customPermission/X172_202020500";
import hasVariazionePermission18 from "@salesforce/customPermission/X172_202020501";
import hasVariazionePermission19 from "@salesforce/customPermission/X172_202020502";
import hasVariazionePermission20 from "@salesforce/customPermission/X172_202020503";
import hasVariazionePermission21 from "@salesforce/customPermission/X172_202020504";
import hasVariazionePermission22 from "@salesforce/customPermission/X172_202150504";
import hasVariazionePermission23 from "@salesforce/customPermission/X172_202150600";
import hasVariazionePermission24 from "@salesforce/customPermission/X172_202020600";
import hasVariazionePermission25 from "@salesforce/customPermission/X172_202150000";
import hasVariazionePermission26 from "@salesforce/customPermission/X172_202150101";
import hasVariazionePermission27 from "@salesforce/customPermission/X172_202150102";
import hasVariazionePermission28 from "@salesforce/customPermission/X172_202150100";
import hasVariazionePermission29 from "@salesforce/customPermission/X172_202150103";
import hasVariazionePermission30 from "@salesforce/customPermission/X172_202150104";
import hasVariazionePermission31 from "@salesforce/customPermission/X172_202150105";
import hasVariazionePermission32 from "@salesforce/customPermission/X172_202150106";
import hasVariazionePermission33 from "@salesforce/customPermission/X172_202150107";
import hasVariazionePermission34 from "@salesforce/customPermission/X172_202150200";
import hasVariazionePermission35 from "@salesforce/customPermission/X172_202150300";
import hasVariazionePermission36 from "@salesforce/customPermission/X172_202150301";
import hasVariazionePermission37 from "@salesforce/customPermission/X172_202150302";
import hasVariazionePermission38 from "@salesforce/customPermission/X172_202150303";
import hasVariazionePermission39 from "@salesforce/customPermission/X172_202150304";
import hasVariazionePermission40 from "@salesforce/customPermission/X172_202150305";
import hasVariazionePermission41 from "@salesforce/customPermission/X172_202150400";
import hasVariazionePermission42 from "@salesforce/customPermission/X172_202150500";
import hasVariazionePermission43 from "@salesforce/customPermission/X172_202150501";
import hasVariazionePermission44 from "@salesforce/customPermission/X172_202150502";
import hasVariazionePermission45 from "@salesforce/customPermission/X172_202150503";
import hasVisualizzazionePermission1 from "@salesforce/customPermission/X174_201000000";
import hasVisualizzazionePermission2 from "@salesforce/customPermission/X174_202000000";
import hasSostituzionePermission1 from "@salesforce/customPermission/X182_201010000";
import hasSostituzionePermission2 from "@salesforce/customPermission/X182_201020000";
import hasSostituzionePermission3 from "@salesforce/customPermission/X182_202030000";
import hasSostituzionePermission4 from "@salesforce/customPermission/X182_202120000";
import hasSostituzionePermission5 from "@salesforce/customPermission/X182_202010000";
import hasSostituzionePermission6 from "@salesforce/customPermission/X182_202140000";
import hasSostituzionePermission7 from "@salesforce/customPermission/X182_202200100";
import hasSostituzionePermission8 from "@salesforce/customPermission/X182_202200200";
import hasSostituzionePermission9 from "@salesforce/customPermission/X182_202210100";
import hasSostituzionePermission10 from "@salesforce/customPermission/X182_202210200";
import hasSostituzionePermission11 from "@salesforce/customPermission/X182_206110100";
import hasSostituzionePermission12 from "@salesforce/customPermission/X182_206110200";
import { CurrentPageReference } from 'lightning/navigation';
import uniRuota from '@salesforce/resourceUrl/uniRuota';
import uniCasa from '@salesforce/resourceUrl/uniCasa';

import checkAbbinato from '@salesforce/apex/InsurancePolicyController.checkAbbinato';

export default class RecordAccordionTable extends NavigationMixin(LightningElement) {
  @api recordId;

  @track rows = [];
  @track otherPolicies = [];
  @track isLoading = false;
  @track expandedRows = {};
  @track rowIdSelected = false;
  @track agencyType = 'same';
  @track isAbbinato = false;
  hasAperturaSinistroPermission = hasCustomPermission;
  isFlowModalOpened = false;
  flowInputs = [];

  _selectedTab;
  _policies;

  @api
  set selectedTab(value) {
    this._selectedTab = value;
    this.tryLoadingData();
  }

  get selectedTab() {
    return this._selectedTab;
  }

  @api
  set policies(value) {
    this._policies = value;
    this.tryLoadingData();
  }

  get policies() {
    return this._policies;
  }

  get motorIcon() {
    return uniRuota;
  }

  get casaIcon() {
    return uniCasa;
  }

  tryLoadingData() {
    if (this._selectedTab != null) {
      checkAbbinato({
            accountId: this.recordId
          }
        ).then( (data) => {
          this.isAbbinato = data.isAbbinato;
          this.loadDataByTab();
        })
    }
  }

  loadDataByTab() {
    this.isLoading = true;
    this.expandedRows = {};

    //const isReadOnly = this._selectedTab === "tutti";
    const isReadOnly = !this.isAbbinato;

    setTimeout(() => {
      const normalizedPolicies = Array.isArray(this._policies)
        ? this._policies
        : this._policies
        ? [this._policies]
        : [];

      this.rows = normalizedPolicies.map((p, index) => {
        let premioFormatted = "-";
        if (p.Role !== "Beneficiario" && p.Role !== "Assicurato") {
          if (
            !isNaN(p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"]) &&
            typeof p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"] === "number"
          ) {
            premioFormatted = `${p["InsurancePolicy.NPI__r.GrossWrittenPremium__c"].toFixed(
              2
            )} €`;
          }
        }

        let scadenza = "-";
        if (
          p["InsurancePolicy.ExpirationDate"] &&
          typeof p["InsurancePolicy.ExpirationDate"] === "string"
        ) {
          const expirationDate = new Date(p["InsurancePolicy.ExpirationDate"]);
          const today = new Date();
          const maxDate = new Date();
          maxDate.setFullYear(today.getFullYear() + 200);

          if (expirationDate <= maxDate) {
            const day = String(expirationDate.getDate()).padStart(2, '0');
            const month = String(expirationDate.getMonth() + 1).padStart(2, '0');
            const year = expirationDate.getFullYear();
            scadenza = `${day}/${month}/${year}`;
          } else {
            scadenza = "-";
          }
        }
        let nomeProdotto = "-";
        if (p["InsurancePolicy.RecordType.DeveloperName"] === "ESSIG_VITA_INDIVIDUALE"){
          nomeProdotto = this.getTariffaFromCode(p["InsurancePolicy.Product__c"]);
        } else if (p["InsurancePolicy.RecordType.DeveloperName"] === "PU_FOLDER" || p["InsurancePolicy.RecordType.DeveloperName"] === "PU_POSITION"){
          nomeProdotto = "UNICA" + " - " + (p["InsurancePolicy.Product__c"] ?? "") + " - " + (p["InsurancePolicy.AreasOfNeed__c"] ?? "");
        } else if (p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_FOLDER" && p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_POSITION" && p["InsurancePolicy.RecordType.DeveloperName"] !== "ESSIG_VITA_INDIVIDUALE"){
          nomeProdotto = (p["InsurancePolicy.AreasOfNeed__c"] ?? "") + " - " + (p["InsurancePolicy.Product__c"] ?? "");
        }
        
        return {
          id: p.Id || `row-${index}`,
          nomeProdotto: nomeProdotto || "-",
          areasOfNeed: p["InsurancePolicy.AreasOfNeed__c"] || "-",
          idPolizza: (p["InsurancePolicy.PolicyBranchCode__c"] && p["InsurancePolicy.ReferencePolicyNumber"]) ? (p["InsurancePolicy.PolicyBranchCode__c"] + p["InsurancePolicy.ReferencePolicyNumber"]) : "-",
          idPolizzaRecordId: p["InsurancePolicy.Id"], //
          insuranceProduct: p["InsurancePolicy.Product__c"], //
          insuranceNamedInsuranceCF: p["InsurancePolicy.NameInsured.CF__c"], //
          ruoloCliente: p.Role || "-",
          premio: premioFormatted,
          scadenza: scadenza,
          iconName: isReadOnly ? "" : "utility:chevronright",
          isExpanded: false,
          showDropdown: false,
          isLoadingDetails: false,
          hasDetailsLoaded: false,
          rowClass: "accordion-row",
          isReadOnly: isReadOnly,
          ServiceProviderIdentifier: p["InsurancePolicy.ParentPolicy.ServiceProviderIdentifier__c"], //mark
          idTecPosition: p["InsurancePolicy.ServiceProviderIdentifier__c"], //mark]
          hasMotor: false,
          hasCasaEFamiglia: false,
          hasPersona: false,
          isUnica: p["InsurancePolicy.RecordType.DeveloperName"] === "PU_FOLDER" || p["InsurancePolicy.RecordType.DeveloperName"] === "PU_POSITION",
          isEssig: p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_FOLDER" && p["InsurancePolicy.RecordType.DeveloperName"] !== "PU_POSITION" && p["InsurancePolicy.RecordType.DeveloperName"] !== "ESSIG_VITA_INDIVIDUALE",
          isVita: p["InsurancePolicy.RecordType.DeveloperName"] === "ESSIG_VITA_INDIVIDUALE",
          fractionation: this.getFractionationFromCode(p["InsurancePolicy.NPI__r.PremiumFrequency"]) ?? '-',
          society: p["InsurancePolicy.Society__r.ExternalId__c"],
          policyType: p["InsurancePolicy.RecordType.DeveloperName"]
        };
      });
      this.isLoading = false;
    }, 300);
  }
  /* handlePremio(ruoloCliente, premioForomatted) {
  if(ruoloCliente === 'Beneficiario') {
    return '-';
  } else {
    return premioForomatted;
  }
} */
  navigateToRecord(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const row = this.rows.find((r) => r.id === rowId);
    if (!row || !row.idPolizzaRecordId) return;
    
    if (row.isUnica) {
      this.flowInputs = [
          { name: 'FEIID', type: 'String', value: 'CONSULTAZIONE_CONTRATTO'},
          { name: 'recordId', type: 'String', value: row.idPolizzaRecordId },
          { name: 'society', type: 'String', value: row.society }                   
      ];
      this.toggleFlowModal();
    } else {
      this[NavigationMixin.Navigate]({
        type: "standard__recordPage",
        attributes: {
          recordId: row.idPolizzaRecordId,
          actionName: "view",
        },
      });
    }
    
  }
  @track tempVar;
  toggleDetails(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const row = this.rows.find((r) => r.id === rowId);
    console.log("row  --> " + JSON.stringify(row.ServiceProviderIdentifier));
    this.tempVar = row.ServiceProviderIdentifier;
    console.log("TEMP  --> " + JSON.stringify(this.tempVar));
    if (row?.isReadOnly) return;

    const alreadyExpanded = this.expandedRows[rowId];

    // Espandere subito l'accordion (senza aspettare il caricamento dei dati)
    this.expandedRows = {
      ...this.expandedRows,
      [rowId]: !alreadyExpanded,
    };

    this.updateIcons();

    // Eseguiamo l'auto-scroll se necessario prima che i dati vengano caricati
    const btn = event.currentTarget; // Otteniamo il bottone direttamente dall'evento
    if (btn) {
      const rect = btn.getBoundingClientRect();
      const accordionHeight = 400; // altezza stimata dell'accordion aperto
      const spaceBelow = window.innerHeight - rect.bottom;

      // Se lo spazio sotto è insufficiente, eseguiamo lo scroll
      if (spaceBelow < accordionHeight) {
        setTimeout(() => {
          btn.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 50);
      }
    }

    // Se non ha ancora i dettagli, caricali
    if (!alreadyExpanded && !row.hasDetailsLoaded) {
      this.rows = this.rows.map((r) =>
        r.id === rowId ? { ...r, isLoadingDetails: true } : r
      );

      // Caricamento dei dettagli (simulato con un delay)
      this.loadDetailsForRow(rowId).then(() => {
        this.expandedRows = {
          ...this.expandedRows,
          [rowId]: true, // Dopo aver caricato i dati, l'accordion sarà espanso
        };
        this.updateIcons();
      });
    }
  }
  toggleSection(event) {
    const sectionId = event.currentTarget.dataset.id;
    const rowId = event.currentTarget.dataset.rowid;

    // Trova la riga e aggiorna la sezione
    this.rows = this.rows.map((row) => {
      if (row.id !== rowId) return row;
      return {
        ...row,
        sections: row.sections.map((section) => {
          if (section.id !== sectionId) return section;
          return {
            ...section,
            isOpen: !section.isOpen,
            iconName: section.isOpen
              ? "utility:chevronright"
              : "utility:chevrondown",
          };
        }),
      };
    });
  }

  updateIcons() {
    this.rows = this.rows.map((row) => ({
      ...row,
      iconName: row.isReadOnly
        ? ""
        : this.expandedRows[row.id]
        ? "utility:chevrondown"
        : "utility:chevronright",
      isExpanded: !!this.expandedRows[row.id],
    }));
  }

  loadDetailsForRow(rowId) {
    return new Promise((resolve) => {
      // Spinner attivo subito
      this.rows = this.rows.map((row) =>
        row.id === rowId ? { ...row, isLoadingDetails: true } : row
      );

      const simulateError = false;

      if (simulateError) {
        this.showErrorToast("Errore nel recupero dei dettagli.");
        this.rows = this.rows.map((row) =>
          row.id === rowId ? { ...row, isLoadingDetails: false } : row
        );
        resolve(false); // False = errore
        return;
      }
      console.log("quaaaaa" + this.tempVar);
      const row = this.rows.find((r) => r.id === rowId);

      //const values = this.getMockData(rowId);
      this.callIntegrationProcedure(row.idPolizzaRecordId, rowId);
      /**
  const sections = this.getSectionsByTab(this._selectedTab, values);

  this.rows = this.rows.map(row => row.id === rowId ? {
      ...row,
      sections: sections,
      isLoadingDetails: false,
      hasDetailsLoaded: true
  } : row);
   
   */
    });
  }

  getSectionsByTab(tab, values) {
    const tabsConfig = {
      motor: [
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente }, // si/no
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Data incasso", value: values.dataIncasso },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
          ],
        },
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Targa", value: values.targa },
            { label: "Modello Auto", value: values.modello },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            { label: "Descrizione CIP Polizza", value: values.descrizioneCip },
            { label: "Stato polizza/posizione", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "Presenza box", value: values.presenzaBox },
            { label: "Valore canone box", value: values.canoneBox },
            { label: "Nr. Unibox", value: values.nrUnibox },
            { label: "Esito Prevenitvass", value: values.esitoPreventivass },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Sospensione polizza", value: values.sospensione },
            { label: "CARE", value: values.care },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling }, // solo se valorizzato
          ],
        },
      ],
      casa: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      persona: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      attivita: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "ID Folder", value: values.policyNumber }, // solo Unica
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato }, // solo Unica
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // Mostrare solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
      vita: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Stato polizza e Causale",
              value: values.statoPolizzaCausale,
            },
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Agenzia", value: values.agenzia },
            { label: "CIP", value: values.cip },
          ],
        },
        {
          id: "premi",
          title: "PREMI E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Tipo Premio", value: values.tipoPremio },
            {
              label: "Data ultimo premio pagato",
              value: values.dataUltimoPremioPagato,
            },
            { label: "Ultimo premio pagato", value: values.ultimoPremioPagato },
            { label: "Data effetto", value: values.dataEffetto },
          ],
        },
        {
          id: "valori",
          title: "VALORI AD OGGI",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Prestazione Assicurata (€)",
              value: values.prestazioneAssicurata,
            },
            { label: "Valore riscatto (€)", value: values.valoreRiscatto },
            {
              label: "Cumulo premi attivi (€)",
              value: values.cumuloPremiAttivi,
            },
          ],
        },
        {
          id: "info",
          title: "ULTERIORI INFORMAZIONI",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            {
              label: "Fascicolo Informativo / Set Informativo",
              value: values.linkFascicolo, // esempio: '<a href="..." target="_blank">Scarica Documento</a>'
              isRichText: true, // se gestito lato front per render HTML
            },
            {
              label: "Motori Finanziari Gestione Separata - GEST 1 UNIPOL",
              value: values.linkMotoriFinanziari, // esempio: '<a href="..." target="_blank">Consulta in UEBA</a>'
              isRichText: true,
            },
          ],
        },
      ],
      salute: [
        {
          id: "prod",
          title: "CARATTERISTICHE PRODOTTO",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "CIP di Polizza", value: values.cip },
            { label: "Bene assicurato", value: values.beneAssicurato },
            { label: "Modulo", value: values.modulo }, // esempio: essential + myfamily
            { label: "PROD (Codice + Descrizione)", value: values.prod },
            {
              label: "Descrizione CIP Polizza / Soggetto abbinato",
              value: values.descrizioneCip,
            },
            { label: "Stato polizza/posizione", value: values.statoPolizza },
            { label: "Agenzia di riferimento", value: values.agenzia },
          ],
        },
        {
          id: "dates",
          title: "FRAZIONAMENTO E DATE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Data effetto", value: values.dataEffetto },
            { label: "Addebito ricorrente", value: values.addebitoRicorrente },
            {
              label: "Metodo di pagamento (associato)",
              value: values.metodoPagamento,
            },
            { label: "Ultimo titolo incassato", value: values.ultimoIncasso },
            { label: "Data incasso", value: values.dataIncasso }, // solo se valorizzato
          ],
        },
        {
          id: "info",
          title: "INFO AGGIUNTIVE",
          isOpen: true,
          iconName: "utility:chevrondown",
          fields: [
            { label: "Codice convenzione", value: values.codiceConvenzione },
            { label: "Nome convenzione", value: values.nomeConvenzione },
            { label: "FEA", value: values.fea },
            { label: "Canale di acquisto", value: values.canaleAcquisto },
            { label: "Voucher", value: values.voucher },
            { label: "Suggerimento di up-selling", value: values.upSelling },
          ],
        },
      ],
    };

    return tabsConfig[tab] || [];
  }
  // Da rimuovere
  

  handleDropdownToggle(event) {
    const rowId = event.currentTarget.dataset.id;
    console.log("row id --> " + rowId);
    const btn = event.currentTarget;
    const rect = btn.getBoundingClientRect();
    const menuHeight = 150;

    // Toggle menu aperto/chiuso
    this.rows = this.rows.map((row) => {
      const showDropdown = row.id === rowId ? !row.showDropdown : false;
      return {
        ...row,
        showDropdown,
        rowClass:
          row.id === rowId && showDropdown
            ? "accordion-row dropdown-open"
            : "accordion-row",
      };
    });

    const recordTable = this.template.querySelector(".record-table");
    const isDropdownOpening = this.rows.find(
      (r) => r.id === rowId && r.showDropdown
    );

    if (isDropdownOpening && !this.rowIdSelected) {
      recordTable.style.minHeight = `${
        recordTable.offsetHeight + menuHeight
      }px`;
      this.rowIdSelected = true;
    } else if (!isDropdownOpening && this.rowIdSelected) {
      recordTable.style.minHeight = `0px`;
      this.rowIdSelected = false;
    }

    const spaceRequired =
      rect.bottom + menuHeight > window.innerHeight
        ? rect.top - menuHeight
        : rect.bottom;

    if (isDropdownOpening) {
      // Auto-scroll solo se necessario
      const spaceBelow = window.innerHeight - rect.bottom;
      if (spaceBelow < menuHeight) {
        setTimeout(() => {
          btn.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 50);
      }
    }
  }

  handleMenuClick(event) {
    const action = event.currentTarget.dataset.action;
    const policyId = event.currentTarget.dataset.id;
    const row = this.rows.find((r) => r.idPolizzaRecordId === policyId);
    const policyType = row.policyType.toLowerCase();
    const society = row.society;
    // For now, pass empty string as second param (to be extended later)
    const feiId = this.getFeiIdFromAction(action, policyType);
    this.flowInputs = [
        { name: 'FEIID', type: 'String', value: feiId},
        { name: 'recordId', type: 'String', value: policyId },
        { name: 'society', type: 'String', value: society }             
    ];
    const toggleEvent = {
      currentTarget: {
        dataset: {
          id: policyId
        }
      }
    };
    this.toggleFlowModal();
    this.handleDropdownToggle(toggleEvent);
  }

  getFeiIdFromAction(action, policyType) {
    const feiIdMap = {
      //generica
      'apertura_sinistro': 'SXCC.INSERIMENTO',
      //unica
      'storno_cessazione': 'UNICA.STORNO.CESSAZIONE',
      'associa_pagamento': 'UNICA.ASSOCIA.METODO.PAGAMENTO',
      'cessione': 'UNICA.CESSIONE',
      'variazione_temporanea': 'UNICA.VARIAZIONE.TEMPORANEA',
      'gestisci_contatti': 'UNICA.CONTATTI.TELEMATICA',
      //auto/nananti
      'visualizzazione_essig_auto': 'IP.INTERROGAZIONE.POLIZZA',
      'variazione_essig_auto': 'RA.VARIAZIONE.POLIZZA',
      'sostituzione_essig_auto': 'NPAC.SOSTITUZIONE.POLIZZA',
      //essig_re 
      'visualizzazione_essig_re': 'IP.INTERROGAZIONE.POLIZZA',
      'variazione_essig_re': 'RE.VARIAZIONE.POLIZZA',
      'sostituzione_essig_re': 'RE.SOSTITUZIONE.POLIZZA',
      //vita_individuale
      'visualizza_essig_vita_individuale': 'VITA.VISUALIZZA',
      'lavora_essig_vita_individuale': 'VITA.LAVORA'
    };
    const feiId = (policyType !== 'pu_position' && action !== 'apertura_sinistro') ? feiIdMap[`${action}_${policyType}`] : feiIdMap[action];
    return  feiId;
  }

  toggleFlowModal(){
    this.isFlowModalOpened = !this.isFlowModalOpened;
  }

  handleFlowStatusChange(event) {
    if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
        this.toggleFlowModal();
  }

  showErrorToast(message) {
    const toast = new ShowToastEvent({
      title: "Errore",
      message,
      variant: "error",
    });
    this.dispatchEvent(toast);
  }

  callIntegrationProcedure(idPolizzaRecordId, rowId) {
    console.log("dentro la chiamata " + idPolizzaRecordId);
    this.isLoading = true;
    const inputMap = {
      policyId: idPolizzaRecordId
    };
    const config = JSON.stringify({
      type: "integrationprocedure",
      value: {
        ipMethod: "ProdottiAssicurativiDetails_GetDataRow",
        inputMap: JSON.stringify(inputMap),
      },
    });
    getDataHandler(config)
      .then((result) => {
        // Verifica se il risultato è una stringa JSON e parsalo
        let parsedResult;
        try {
          parsedResult =
            typeof result === "string" ? JSON.parse(result) : result;
          console.log("parsedResult --> " + JSON.stringify(parsedResult));
        } catch (e) {
          this.isLoading = false;
          console.error("Errore parsing JSON:", e);
          this.showErrorToast("Errore nel parsing della risposta JSON.");
          return;
        }

        const data = parsedResult?.IPResult;
        if (data) {
          //elaboro data
          let values = {};
          let otherPolicies = [];
          let area = '';
          
          const selectedRow = this.rows.find((row) => row.id === rowId);

          if (selectedRow.isUnica){ 
            values = this.filterUnicaValues(data);
            area = this.getUnicaAreaFromCode(values.code);
          }
          if (selectedRow.isEssig || selectedRow.isVita) { 
            values = this.filterEssigValues(data, selectedRow.areasOfNeed);
            area = this.getEssigAreaFromAreaOfNeed(selectedRow.areasOfNeed);
          }

          //filters out current policy and populates list for checks
          if (data.response.positions) {
            otherPolicies = this.getOtherPolicies(data.response.positions, values.code); 
          }
          const sections = this.getSectionsByTab(area, values);

          this.rows = this.rows.map((row) =>
            row.id === rowId
              ? {
                  ...row,
                  sections: sections,
                  isLoadingDetails: false,
                  hasDetailsLoaded: true,
                  hasMotor: otherPolicies.some(p => p.code === "PUAUTO" || p.code === "PUMOBILITA"),
                  hasCasaEFamiglia: otherPolicies.some(
                    p => p.code === "PUPET" ||
                    p.code === "PUCASA" ||
                    p.code === "PUFAM" ||
                    p.code === "PUVIAGGIT"
                  ),
                  hasPersona: otherPolicies.some(p => p.code === "PUINF" || p.code === "PUSAL"),
                }
              : row
          );

          // Seleziona la sezione corretta: same_user_agency o other_agency
          /*     this.activeAgencyData = data.same_user_agency || data.other_agency || {};
          this.updateTabsWithCounts(this.activeAgencyData); */
        } else {
          console.warn("Struttura inattesa nella risposta:", parsedResult);
          this.isLoading = false;
          this.showErrorToast(
            "Struttura inattesa nella risposta della Integration Procedure."
          );
        }
      })
      .catch((error) => {
        console.error("Errore nella chiamata alla IP:", error);
        this.showErrorToast(
          "Errore durante la chiamata alla Integration Procedure."
        );
      }).finally(() => {
        this.isLoading = false;
      });
  }

  filterUnicaValues(IPResult) {
    const data = IPResult.response;
    const agencyCode = IPResult.agencyCode;
    const filteredPosition = data.positions.find(item => item.idTecPosition === IPResult.idTecPosition);
      
    if (!filteredPosition) {
      console.warn(`No position found with idTecPosition: ${data.idTecPosition}`);
      return null;
    }

    // Create base formatted object
    const formattedResult = {
      code: filteredPosition.code,
      policyNumber: data.policyNumber ? data.policyNumber : '',
      cip: data.cip, //da vedere
      prod: filteredPosition.productCode + ' - ' + this.getProductFromCode(filteredPosition.productCode),
      descrizioneCip: data.subAgencyDescription,
      statoPolizza: data.policyStatus,
      agenzia: agencyCode ? agencyCode : '',
      dataEffetto: this.formatDate(filteredPosition.effectiveStartDate),
      dataIncasso: this.formatDate(filteredPosition.titleEmissionDate),
      addebitoRicorrente: filteredPosition.fractionation == "1" ? "NO" : "SI", //fractionation a livello di posizione
      metodoPagamento: this.getPaymentMethodFromCode(filteredPosition.paymentMethod),
      ultimoIncasso: filteredPosition.titleAmount ? filteredPosition.titleAmount + ' €' : '',
      codiceConvenzione: filteredPosition.conventionCode || "",
      nomeConvenzione: filteredPosition.conventionDescription || "", //da mappare dal codice
      fea: data.fea == 'FALSE' ? "NO" : "SI", //booleano facciamolo diventare sì no
      canaleAcquisto: data.channelCode,
      voucher: filteredPosition.voucherNumber || "",
      //upSelling: data.upselling,
      upSelling: ''
    };

    // Handle beneAssicurato based on tab type
    switch(filteredPosition.code) {
      case 'PUAUTO':
          const brand = this.getBrandFromCode(filteredPosition.vehicle?.brand);
          const model = this.getModelFromCode(filteredPosition.vehicle?.model);
          formattedResult.targa = filteredPosition.vehicle?.licensePlate || '';
          formattedResult.modello = model;
          formattedResult.beneAssicurato = `${filteredPosition.vehicle?.licensePlate || ''} ${brand || ''} ${model || ''}`;
          break;
      case 'PUCASA':
          const homeType = this.getHomeTypeFromCode(filteredPosition.home?.useType);
          formattedResult.beneAssicurato = `${filteredPosition.home?.address || ''} ${filteredPosition.home?.city || ''} ${homeType || ''}`;
          break;
      case 'PUFAM':
          formattedResult.beneAssicurato = `${filteredPosition.family?.firstname || ''} ${filteredPosition.family?.lastname || ''}`;
          break;
      case 'PUPET':
          const breed = this.getBreedFromCode(filteredPosition.pet?.breed);
          const petType = filteredPosition.pet?.type === '1' ? 'Cane' : 
                        filteredPosition.pet?.type === '2' ? 'Gatto' : '';
          formattedResult.beneAssicurato = `${petType} ${breed || ''}`;
          break;
      case 'PUMOBILITA':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUINF':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUSAL':
          formattedResult.beneAssicurato = `${filteredPosition.person?.birthDate || ''}`;
          break;
      case 'PUVIAGGIT':
          const destination = this.getDestinationFromCode(filteredPosition.travel?.destination);
          formattedResult.beneAssicurato = `${destination || ''} ${filteredPosition.travel?.startDate || ''} ${filteredPosition.travel?.endDate || ''}`;
          break;
      default:
          formattedResult.beneAssicurato = "-";
    }

    console.log('Filtered and formatted result:', formattedResult);
    
    return formattedResult;
    
  }

  filterEssigValues(IPResult, areaOfNeed) {
    const data = IPResult.response;
    const agencyCode = IPResult.agencyCode;

    const formattedResult = {
      code: '',
      policyNumber: data.policyNumber ? data.policyNumber : '',
      cip: data.cip, //da vedere
      prod: data.productName ? data.productName : '',
      descrizioneCip: data.subAgency,
      statoPolizza: data.policyStatus,
      agenzia: agencyCode ? agencyCode : '',
      dataEffetto: this.formatDate(data.effectiveStartDate),
      dataIncasso: this.formatDate(data.titleEmissionDate),
      addebitoRicorrente: data.fractionation == "1" ? "NO" : "SI", //1 sta per annuale
      metodoPagamento: this.getPaymentMethodFromCode(data.paymentMethod),
      ultimoIncasso: data.titleAmount ? data.titleAmount + ' €' : '',
      codiceConvenzione: data.conventionCode || "",
      nomeConvenzione: data.conventionDescription || "", //da mappare dal codice
      fea: data.fea == 'FALSE' ? "NO" : "SI", //booleano facciamolo diventare sì no
      canaleAcquisto: data.channelCode,
      voucher: data.voucherNumber || "",
      //upSelling: data.upselling,
      upSelling: ''
    };

    switch(areaOfNeed) {
      case 'Veicoli': //ESSIG_AUTO
        const model = data.asset[0]?.model || '';
        formattedResult.targa = data.asset[0]?.licensePlate || '';
        formattedResult.modello = model;
        formattedResult.beneAssicurato = model;
        break;
      case 'Famiglia', 'Persona': //ESSIG_RE
        formattedResult.beneAssicurato = `${data.firstname || ''} ${data.lastname || ''}`;
        break;
      case 'Vita': //ESSIG_VITA_INDIVIDUALE
        formattedResult.beneAssicurato = `${data.firstname || ''} ${data.lastname || ''}`;
        break;
      default:
        formattedResult.beneAssicurato = "-";
    }

    return formattedResult;
  }

  getOtherPolicies(policies, currentCode) {
    const otherPolicies = policies.filter(p => p.code !== currentCode);
    if (otherPolicies.length === 0) return [];
    return otherPolicies;
  }

  getFractionationFromCode(code){
    const FRACTIONATION_CODES = {
      '0': 'TEMPORANEO',
      '1': 'ANNUALE',
      '2': 'SEMESTRALE',
      '3': 'QUADRIMESTRALE',
      '4': 'TRIMESTRALE',
      '6': 'BIMESTRALE',
      '8': 'PREMIO UNICO ANTICIPATO',
      '9': 'PREMIO UNICO ANTICIPATO'
    }
    return FRACTIONATION_CODES[code];
  }

  getUnicaAreaFromCode(code) {
    const AREA_CODES = {
      'PUAUTO': 'motor',
      'PUMOBILITA': 'motor',
      'PUCASA': 'casa',
      'PUFAM': 'casa',
      'PUPET': 'casa',
      'PUVIAGGIT': 'casa',
      'PUINF': 'persona',
      'PUSAL': 'persona'
    }
    return AREA_CODES[code];
  }

  getEssigAreaFromAreaOfNeed(areaOfNeed) {
    const AREA_CODES = {
      'Veicoli': 'motor',
      'Famiglia': 'casa',
      'Vita': 'salute',
      'Persona': 'persona'
    }
    return AREA_CODES[areaOfNeed];
  }

  handleOtherPolicyClick(event) {
    const policyType = event.currentTarget.dataset.id;
    console.log("Clicked other policy ID: " + policyType);
    this.dispatchEvent(
      new CustomEvent("changetab", {
        detail: { policyType },
      })
    );

  }

  getBreedFromCode(breedCode) {
    const BREED_CODES = {
        '101': 'ALANO',
        '102': 'BARBONCINO/BARBONE',
        '103': 'BASSOTTO TEDESCO',
        '104': 'BEAGLE',
        '105': 'BORDER COLLIE',
        '106': 'BOXER',
        '107': 'BULLDOG INGLESE',
        '108': 'CANE CORSO',
        '109': 'CAVALIER KING',
        '110': 'CHIHUAHUA',
        '111': 'COCKER SPANIEL INGLESE',
        '112': 'DALMATA',
        '113': 'DEUTSCH KURZHAAR O BRACCO TEDESCO',
        '114': 'EPAGNEUL BRETON',
        '115': 'GOLDEN RETRIVER',
        '116': 'HUSKY',
        '117': 'JACK RUSSELL TERRIER',
        '118': 'LABRADOR',
        '119': 'LAGOTTO',
        '120': 'LEVRIERO',
        '121': 'MALTESE',
        '122': 'PASTORE MAREMMANO',
        '123': 'PASTORE TEDESCO',
        '124': 'POINTER',
        '125': 'SCHNAUZER',
        '126': 'SEGUGIO',
        '127': 'SETTER INGLESE',
        '128': 'SHIH TZU',
        '129': 'VOLPINO',
        '130': 'WEIMARANER',
        '131': 'METICCIO',
        '132': 'SAMOIEDO',
        '133': 'YORKSHIRE',
        '134': 'BOVARO DEL BERNESE',
        '135': 'BULLDOG FRANCESE',
        '136': 'CARLINO',
        '137': 'PINSCHER',
        '138': 'SHAR PEI',
        '139': 'TERRANOVA',
        '140': 'AKITA',
        '141': 'ALASKAN MALAMUTE',
        '142': 'AMERICAN BULLY',
        '143': 'AUSTRALIAN SHEPHERD',
        '144': 'BASSET HOUND',
        '145': 'BOLOGNESE',
        '146': 'BOSTON TERRIER',
        '147': 'BRACCO',
        '148': 'CANE DA PASTORE BELGA',
        '149': 'CANE DA PASTORE DEL CAUCASO',
        '150': 'CANE DA PASTORE SVIZZERO',
        '151': 'CANE SAN BERNARDO',
        '152': 'CHOW CHOW',
        '153': 'COLLIE',
        '154': 'FOX TERRIER',
        '155': 'LEONBERGER',
        '156': 'LUPO CECOSLOVACCO',
        '157': 'MALTIPOO',
        '158': 'PASTORE SCOZZESE',
        '159': 'PECHINESE',
        '160': 'RHODESIAN RIDGEBACK',
        '161': 'SETTER IRLANDESE',
        '162': 'SHIBA',
        '163': 'SPINONE',
        '164': 'WELSH CORGI PEMBROKE',
        '165': 'WEST HIGHLAND WHITE TERRIER',
        '300': 'AMERICAN BULLDOG',
        '301': 'AMERICAN STAFFORDSHIRE TERRIER O AMSTAFF',
        '302': 'BRIARD',
        '303': 'BULL TERRIER',
        '304': 'BULLMASTIFF',
        '305': 'CANE DA MONTAGNA DEI PIRENEI',
        '306': 'DOBERMANN',
        '307': 'DOGO ARGENTINO',
        '308': 'DOGUE DE BORDEAUX',
        '309': 'FILA BRAZILEIRO',
        '310': 'MASTINO NAPOLETANO',
        '311': 'PITT BULL',
        '312': 'ROTTWEILER',
        '313': 'STAFFORDSHIRE TERRIER',
        '314': 'TIBETAN MASTIFF BRIARD',
        '315': 'TOSA INU GIAPPONESE',
        '316': 'METICCIO (INCROCIO CON RAZZE PERICOLOSE)',
        '399': 'ALTRO'
    };

    return BREED_CODES[breedCode] || '';
  }

  getDestinationFromCode(destinationCode) {
    const DESTINATION_CODES = {
        '1101': 'ITALIA',
        '1201': 'ALBANIA',
        '1202': 'ANDORRA',
        '1203': 'AUSTRIA',
        '1204': 'BELGIO',
        '1205': 'BIELORUSSIA',
        '1206': 'BOSNIA ED ERZEGOVINA',
        '1207': 'BULGARIA',
        '1208': 'CITTA DEL VATICANO',
        '1209': 'CROAZIA',
        '1210': 'DANIMARCA',
        '1211': 'ESTONIA',
        '1212': 'FINLANDIA',
        '1213': 'FRANCIA',
        '1214': 'GERMANIA',
        '1215': 'GIBILTERRA',
        '1216': 'GRECIA',
        '1217': 'IRLANDA - EIRE',
        '1218': 'ISLANDA',
        '1219': 'ISOLA DI GUERNSEY',
        '1220': 'ISOLA DI JERSEY',
        '1221': 'ISOLA DI MAN',
        '1222': 'ISOLE FAROE',
        '1223': 'LETTONIA',
        '1224': 'LIECHTENSTEIN',
        '1225': 'LITUANIA',
        '1226': 'LUSSEMBURGO',
        '1227': 'MACEDONIA',
        '1228': 'MALTA',
        '1229': 'MONACO',
        '1230': 'MONTENEGRO',
        '1231': 'NORVEGIA',
        '1232': 'OLANDA - PAESI BASSI',
        '1233': 'POLONIA',
        '1234': 'PORTOGALLO',
        '1235': 'REGNO UNITO',
        '1236': 'REGNO UNITO - GALLESE',
        '1237': 'REGNO UNITO - INGHILTERRA',
        '1238': 'REGNO UNITO - IRLANDA DEL NORD',
        '1239': 'REGNO UNITO - SCOZIA',
        '1240': 'REPUBBLICA CECA',
        '1241': 'ROMANIA',
        '1242': 'SAN MARINO',
        '1243': 'SERBIA',
        '1244': 'SLOVACCHIA',
        '1245': 'SLOVENIA',
        '1246': 'SPAGNA',
        '1247': 'SVEZIA',
        '1248': 'SVIZZERA',
        '1249': 'UCRAINA',
        '1250': 'UNGHERIA',
        '1251': 'SVALBARD E JAN MAYEN',
        '1252': 'MOLDAVIA',
        '1301': 'ALGERIA',
        '1302': 'CIPRO',
        '1303': 'EGITTO',
        '1304': 'ISRAELE',
        '1305': 'LIBANO',
        '1306': 'LIBIA',
        '1307': 'MAROCCO',
        '1309': 'SIRIA',
        '1310': 'TUNISIA',
        '1311': 'TURCHIA',
        '1401': 'ANGOLA',
        '1402': 'BENIN',
        '1403': 'BOTSWANA',
        '1404': 'BURKINA FASO',
        '1405': 'CAMERUN',
        '1406': 'CAPO VERDE',
        '1407': 'COMORE',
        '1408': 'ERITREA',
        '1409': 'GABON',
        '1410': 'GAMBIA',
        '1411': 'GHANA',
        '1412': 'GIBUTI',
        '1413': 'GUINEA',
        '1414': 'GUINEA EQUATORIALE',
        '1415': 'GUINEA-BISSAU',
        '1416': 'KENYA',
        '1417': 'LESOTHO',
        '1418': 'MADAGASCAR',
        '1419': 'MALAWI',
        '1420': 'MALI',
        '1421': 'MAURITANIA',
        '1422': 'MAURITIUS - MAURIZIUS',
        '1423': 'MAYOTTE',
        '1424': 'MOZAMBICO',
        '1425': 'NAMIBIA',
        '1426': 'NIGER',
        '1427': 'NIGERIA',
        '1428': 'RIUNIONE - REUNION',
        '1429': 'SANT ELENA',
        '1430': 'SAO TOME E PRINCIPE',
        '1431': 'SENEGAL',
        '1432': 'SEYCHELLES',
        '1433': 'SUD AFRICA',
        '1434': 'SUDAN DEL SUD',
        '1435': 'SWAZILAND',
        '1436': 'TANZANIA',
        '1437': 'TERRITORI FRANCESI DEL SUD',
        '1438': 'TOGO',
        '1439': 'TRINIDAD E TOBAGO',
        '1440': 'ZAMBIA',
        '1441': 'ZIMBABWE',
        '1501': 'ANGUILLA',
        '1502': 'ANTIGUA E BARBUDA',
        '1503': 'ANTILLE OLANDESI',
        '1504': 'ARGENTINA',
        '1505': 'ARUBA',
        '1506': 'BAHAMAS',
        '1507': 'BARBADOS',
        '1508': 'BELIZE',
        '1509': 'BERMUDA',
        '1510': 'BOLIVIA',
        '1511': 'BRASILE',
        '1512': 'CANADA',
        '1513': 'CILE',
        '1514': 'COLOMBIA',
        '1515': 'COSTA RICA',
        '1516': 'CUBA',
        '1517': 'DOMINICA',
        '1518': 'ECUADOR',
        '1519': 'EL SALVADOR',
        '1520': 'GEORGIA DEL SUD E ISOLE SANDWICH MERIDIONALI',
        '1521': 'GIAMAICA',
        '1522': 'GRENADA',
        '1523': 'GROENLANDIA',
        '1524': 'GUADALUPA',
        '1525': 'GUATEMALA',
        '1526': 'GUYANA',
        '1527': 'GUYANA FRANCESE',
        '1528': 'HONDURAS',
        '1529': 'ISOLE BES',
        '1530': 'ISOLE CAYMAN',
        '1531': 'ISOLE COOK',
        '1532': 'ISOLE FALKLAND',
        '1533': 'ISOLE MARIANNE SETTENTRIONALI',
        '1534': 'ISOLE MINORI ESTERNE DEGLI STATI UNITI',
        '1535': 'ISOLE VERGINI AMERICANE',
        '1536': 'ISOLE VERGINI BRITANNICHE',
        '1537': 'MARTINICA',
        '1538': 'MESSICO',
        '1539': 'MONTSERRAT',
        '1540': 'NICARAGUA',
        '1541': 'PANAMA',
        '1542': 'PARAGUAY',
        '1543': 'PERU',
        '1544': 'PORTO RICO',
        '1545': 'REPUBBLICA DOMINICANA',
        '1546': 'SAINT KITTS E NEVIS',
        '1547': 'SAINT VINCENT E GRENADINE',
        '1548': 'SAINT-BARTH',
        '1549': 'SAINT-MARTIN',
        '1550': 'SAINT-PIERRE E MIQUELON',
        '1551': 'SAMOA AMERICANE',
        '1552': 'SANTA LUCIA',
        '1553': 'SINT MAARTEN',
        '1554': 'STATI UNITI D AMERICA - USA',
        '1555': 'SURINAME',
        '1557': 'TURKS E CAICOS',
        '1558': 'URUGUAY',
        '1559': 'VENEZUELA',
        '1601': 'ANTARTIDE',
        '1602': 'ISOLA BOUVET',
        '1603': 'ISOLE HEARD E MCDONALD',
        '1604': 'TERRE AUSTRALI E ANTARTICHE FRANCESI',
        '1701': 'ARABIA SAUDITA',
        '1702': 'ARMENIA',
        '1703': 'AZERBAIGIAN',
        '1704': 'BAHRAIN - BAHREIN',
        '1705': 'BANGLADESH',
        '1706': 'BHUTAN',
        '1707': 'BIRMANIA',
        '1708': 'BRUNEI',
        '1709': 'CAMBOGIA',
        '1710': 'CINA',
        '1711': 'COREA DEL SUD',
        '1712': 'EMIRATI ARABI UNITI',
        '1713': 'FEDERAZIONE RUSSA - RUSSIA',
        '1714': 'FILIPPINE',
        '1715': 'GEORGIA',
        '1716': 'GIAPPONE',
        '1717': 'GIORDANIA',
        '1718': 'HONG KONG',
        '1719': 'INDIA',
        '1720': 'INDONESIA',
        '1721': 'IRAN',
        '1722': 'KAZAKISTAN',
        '1723': 'KIRGHIZISTAN',
        '1724': 'KUWAIT',
        '1725': 'LAOS',
        '1726': 'MACAO',
        '1727': 'MALDIVE',
        '1728': 'MALESIA',
        '1730': 'MONGOLIA',
        '1731': 'OMAN',
        '1732': 'PAKISTAN',
        '1733': 'PITCAIRN',
        '1734': 'QATAR',
        '1735': 'SINGAPORE',
        '1736': 'SRI LANKA',
        '1737': 'TAGIKISTAN',
        '1738': 'TAILANDIA - THAILANDIA',
        '1739': 'TAIWAN',
        '1740': 'TERRITORI BRITANNICI DELL OCEANO INDIANO',
        '1741': 'TURKMENISTAN',
        '1742': 'UZBEKISTAN',
        '1743': 'VIETNAM',
        '1801': 'AUSTRALIA',
        '1802': 'GUAM',
        '1803': 'ISOLA DI NATALE',
        '1804': 'ISOLA NORFOLK',
        '1805': 'ISOLE COCOS (KEELING)',
        '1806': 'ISOLE FIJI - ISOLE FIGI',
        '1807': 'ISOLE MARSHALL',
        '1808': 'ISOLE MINORI DEGLI STATI UNITI D AMERICA',
        '1809': 'ISOLE SALOMONE',
        '1810': 'KIRIBATI',
        '1811': 'MICRONESIA',
        '1812': 'NAURU',
        '1813': 'NIUE',
        '1814': 'NUOVA CALEDONIA',
        '1815': 'NUOVA ZELANDA',
        '1816': 'PALAU',
        '1817': 'POLINESIA FRANCESE',
        '1818': 'SAMOA',
        '1819': 'TOKELAU',
        '1820': 'TONGA',
        '1821': 'TUVALU',
        '1822': 'VANUATU',
        '1823': 'WALLIS E FUTUNA'
    };

    return DESTINATION_CODES[destinationCode] || '';
  }

  getHomeTypeFromCode(homeTypeCode) {
    const HOME_TYPE_CODES = {
        '1': 'APPARTAMENTO',
        '2': 'VILLA SINGOLA',
        '3': 'VILLA A SCHIERA'
    };

    return HOME_TYPE_CODES[homeTypeCode] || '';
  }

  getBrandFromCode(brandCode) {
    const BRAND_CODES = {
        '8': 'FIAT',
        '13': 'FERRARI',
        '18': 'DACIA',
        '20': 'OPEL',
        '27': 'PORSCHE',
        '33': 'MERCEDES',
        '34': 'LANCIA',
        '35': 'AUTOBIANCHI',
        '39': 'FORD',
        '49': 'BERTONE',
        '53': 'SEAT',
        '62': 'RENAULT',
        '70': 'BENTLEY',
        '74': 'ASTON MARTIN',
        '76': 'CADILLAC',
        '80': 'AUSTIN ROVER',
        '81': 'BUICK',
        '83': 'ALFA ROMEO',
        '84': 'CHEVROLET',
        '85': 'BMW',
        '86': 'ROLLS-ROYCE',
        '87': 'CHRYSLER',
        '91': 'CITROEN',
        '94': 'SKODA',
        '95': 'NISSAN SPAGNA',
        '98': 'VOLVO',
        '101': 'VOLKSWAGEN',
        '103': 'TOYOTA',
        '104': 'DAIHATSU',
        '106': 'DAIMLER',
        '109': 'MORETTI',
        '110': 'JAGUAR',
        '113': 'OLTCIT',
        '114': 'DE TOMASO',
        '117': 'MASERATI',
        '120': 'INNOCENTI',
        '122': 'HYUNDAI',
        '128': 'FSO',
        '134': 'LOTUS',
        '135': 'LAMBORGHINI',
        '136': 'MORGAN',
        '137': 'PONTIAC',
        '140': 'LAND ROVER',
        '144': 'JEEP',
        '145': 'MAZDA',
        '146': 'ISUZU',
        '147': 'HONDA',
        '148': 'MITSUBISHI',
        '149': 'NISSAN',
        '151': 'PEUGEOT',
        '153': 'SAAB',
        '154': 'ROVER',
        '155': 'SUBARU',
        '156': 'SUZUKI',
        '160': 'ALPINA BMW',
        '161': 'AUDI',
        '173': 'VOLKSWAGEN MESSICO',
        '180': 'ARO',
        '193': 'DONKERVOORT',
        '196': 'GINETTA',
        '197': 'ZAZ',
        '216': 'VOLGA',
        '217': 'OTO MELARA',
        '222': 'UMM',
        '224': 'UAZ',
        '225': 'TVR',
        '240': 'PUMA ITALIA',
        '243': 'PANTHER',
        '250': 'CASALINI',
        '254': 'LIGIER',
        '255': 'SANTANA',
        '267': 'DODGE',
        '277': 'CATERHAM',
        '323': 'AC',
        '333': 'APAL',
        '397': 'TALBOT',
        '416': 'AIXAM',
        '422': 'JDM',
        '433': 'LADA',
        '436': 'RAYTON FISSORE',
        '495': 'MARCOS',
        '507': 'VENTURI',
        '515': 'AUVERLAND',
        '577': 'DAEWOO',
        '598': 'MIDDLEBRIDGE',
        '651': 'LUAZ',
        '657': 'MAHINDRA',
        '700': 'AMG',
        '717': 'OTO MELARA',
        '746': 'KIA',
        '766': 'MICROCAR',
        '772': 'P.G.O.',
        '776': 'IATO',
        '804': 'EVANTE',
        '848': 'MARUTI SUZUKI',
        '892': 'YUGO',
        '901': 'OMAI',
        '923': 'BEDFORD',
        '926': 'MICRO VET',
        '938': 'FIAT',
        '941': 'PIAGGIO',
        '945': 'ZAGATO',
        '979': 'SSANGYONG',
        '998': 'CITROEN',
        '1024': 'ACM',
        '1026': 'MOHR',
        '1036': 'TORPEDO',
        '1058': 'DE LA CHAPELLE',
        '1117': 'NOBLE',
        '1362': 'BIAGINI',
        '1421': 'ASIA MOTORS',
        '1504': 'BELLIER',
        '1522': 'BUGATTI',
        '1539': 'CHATENET',
        '1599': 'EPOCAR',
        '1852': 'KEWET',
        '1859': 'CITYCAR',
        '1938': 'MEGA',
        '1945': 'MG',
        '1960': 'MAZZIERI',
        '1962': 'LEXUS',
        '1970': 'INFINITI',
        '2092': 'MOKE',
        '2130': 'TATA',
        '2176': 'BOXEL',
        '2222': 'MINI',
        '2259': 'SMART',
        '2266': 'SAVEL-ERAD',
        '2269': 'TASSO',
        '2273': 'CARLETTI',
        '2275': 'CRECAV',
        '2277': 'SECMA',
        '2285': 'ISO',
        '2288': 'PAGANI',
        '2291': 'VALENTINI',
        '2293': 'TOWN LIFE',
        '2297': 'FEAB',
        '2300': 'QVALE',
        '2303': 'GEM',
        '2309': 'CMC',
        '2311': 'EFFEDI',
        '2313': 'BREMACH',
        '2316': 'RENAULT V.I.',
        '2318': 'IVECO',
        '2320': 'CO.VE.IN.',
        '2322': 'COMAI',
        '2324': 'MULTICAR',
        '2326': 'BONETTI',
        '2330': 'ROMANITAL',
        '2333': 'LEOMAR',
        '2336': 'O.ZETA CLES',
        '2339': 'ZK',
        '2341': 'SCAM',
        '2345': 'ARIEL',
        '2350': 'BUCHER-SCHORLING',
        '2352': 'MORONI',
        '2359': 'MAYBACH',
        '2363': 'DAF',
        '2367': 'FLEUR DE LYS',
        '2369': 'FRESIA',
        '2382': 'NYSA MOTOR',
        '2386': 'ANDORIA MOTOR',
        '2392': 'RENAULT TRUCKS',
        '2394': 'START LAB',
        '2398': 'HUMMER',
        '2401': 'MARANELLO',
        '2403': 'SECA',
        '2405': 'CORVETTE',
        '2407': 'INTRALL POLSKA',
        '2408': 'SALEEN',
        '2409': 'ABARTH',
        '2705': 'GAZ AUTOMOBILE',
        '2706': 'GREEN COMPANY',
        '2708': 'MITSUBISHI TRUCKS',
        '2709': 'ITALCAR',
        '2710': 'GIOTTILINE',
        '2712': 'MELEX',
        '2713': 'META',
        '2715': 'GREAT WALL MOTOR',
        '2716': 'KATAY',
        '2717': 'DRX',
        '2718': 'GIOTTI VICTORIA',
        '2719': 'SHUANGHUAN',
        '2736': 'I.CO.VE.CO.',
        '2737': 'CMI',
        '2738': 'BSI',
        '2740': 'GAC GONOW',
        '2743': 'MITSUBISHI FUSO',
        '2745': 'MARTIN MOTORS',
        '2749': 'TESLA',
        '2752': 'FISKER',
        '2753': 'NYSA',
        '2754': 'MC LAREN',
        '2755': 'VEM',
        '2759': 'MAN',
        '2766': 'MUSTANG',
        '2767': 'MIA ELECTRIC',
        '2768': 'HEIBAO ITALIA',
        '2769': 'I MOVING',
        '2770': 'TAZZARI EV',
        '2771': 'ALKÈ',
        '2772': 'BIRO',
        '2773': 'MINAUTO',
        '2774': 'ROMEO FERRARIS',
        '2775': 'CITROEN DS',
        '2776': 'MAZZANTI',
        '2777': 'XINDAYANG',
        '2779': 'DALLARA',
        '2780': 'ALPINE',
        '2781': 'MILITEM',
        '2782': 'CUPRA',
        '2785': 'MPM MOTORS',
        '2786': 'HAVAL',
        '2788': 'XEV',
        '2790': 'GOUPIL',
        '2791': 'EVO',
        '2792': 'DUCATI ENERGIA',
        '2793': 'MAXUS',
        '2794': 'NANOCAR',
        '2795': 'SERES',
        '2796': 'LYNK&CO',
        '2798': 'ZD',
        '2799': 'AIWAYS',
        '2800': 'INEOS',
        '2801': 'DFSK',
        '2802': 'ELI',
        '2803': 'TODAY SUNSHINE'
    };

    return BRAND_CODES[brandCode] || '';
  }

  getModelFromCode(modelCode) {
    const MODEL_CODES = {
        '4320': '500X',
        '3497': 'Multipla 2ª serie',
        '3900': 'Panamera',
        '4374': 'Tucson 2ª serie',
        '3694': 'Ypsilon 2ª serie', 
        '1029': 'LIBERTY S 50',
        '1371': 'F 800 GS',
        '387': 'VELVET 400',
        '3881': 'Insignia',
        '325': 'AREA 51',
        '2380': 'Corsa 2ª serie',
        '3641': 'IS 2ª serie',
        '3122': 'NQI',
        '923': 'R 1200 RT',
        '2826': 'SE 125',
        '3470': 'fortwo 1ª serie',
        '3072': '150 EXC',
        '3077': 'Punto 2ª serie',
        '1824': 'TRX 850',
        '3003': 'Atos',
        '985': 'AGILITY 125',
        '3788': 'Musa 2ª serie',
        '946': 'VESPA 150 LX',
        '4537': 'X2 (F39)',
        '2282': '200 DUKE',
        '480': 'Fiesta 2ª serie',
        '4221': 'S-Cross',
        '1567': 'Express',
        '352': 'Giulietta',
        '2880': 'SCRAMBLER 1100',
        '4080': 'Serie 3 (F30/F31)',
        '726': 'CALIFORNIA',
        '4570': 'Combo Life',
        '2806': 'V7 III',
        '4048': 'Picanto 3ª serie',
        '4406': 'Edge',
        '3167': 'X-Type X400/6/7/8/9',
        '2066': 'MP3 300',
        '2263': 'INTEGRA',
        '3682': 'Captiva',
        '3544': '800MT',
        '4414': 'A4 allroad 2ª serie',
        '3143': 'Corsa 3ª serie',
        '3197': 'Mini 2ª serie',
        '4125': 'Lodgy',
        '773': 'CBF 600',
        '3621': 'Grand Vitara 2ª',
        '3791': 'A4 4ª serie',
        '4358': 'Caddy 4ª serie',
        '3467': 'forfour',
        '2514': 'STREET 750',
        '4584': 'Combo 5ª serie',
        '904': 'AGILITY 50',
        '2986': 'Golf 4ª serie',
        '4157': 'Mokka',
        '4646': '208 2ª serie',
        '4667': '2008 2ª serie',
        '2950': 'TW 125 X',
        '4098': '208',
        '3244': 'TRIDENT 660',
        '3627': 'Cayman (987)',
        '3350': 'Trans/Tour Connect',
        '3133': 'RAV4 2ª serie',
        '4739': 'VESPA 125 (VNL2/VNL3)',
        '4769': 'Spring',
        '2630': 'Polo 3ª serie',
        '2268': 'XENTER 125',
        '4285': 'Mini 5 porte (F55)',
        '1377': 'GP 800',
        '356': 'SCARABEO 200',
        '2772': 'TRICITY 155',
        '4627': 'Vivaro 4ª serie',
        '323': 'YZF R6',
        '4278': 'Golf Sportsvan',
        '3538': 'Boxster (987)',
        '4713': 'VESPA 125 (VNB/VNC)',
        '2954': 'RKF 125',
        '4678': 'i10 3ª serie',
        '3121': 'MQI',
        '4803': '308 3ª serie',
        '4175': 'Kuga 2ª serie',
        '1807': 'XL 600 V TRANSALP',
        '1956': 'Clio',
        '2461': 'BURGMAN 200',
        '4019': 'A6 4ª serie',
        '5058': 'Mini (F66)',
        '3755': 'Classe C (W/S204)',
        '3901': 'Cruze',
        '4293': '695 C 1.4 TURBO T-JET 180 CV',
        '3960': 'Cayenne 3ª serie',
        '695': 'MONSTER 800',
        '6276': 'SKYTOWN 125',
        '3171': 'Classe C Sportcoupé',
        '858': 'CIAO',
        '3866': 'Q5',
        '6359': 'VESPA 310 GTS HPE',
        '4559': 'Focus 5ª serie',
        '1831': 'XJR 1200',
        '4479': 'Classe E Cpé (C238)',
        '827': 'XRV 750 AFRICA TWIN',
        '2792': 'TRK 502',
        '775': 'CBR 600',
        '3870': '',
        '3930': 'Transp. 6ª \'09->',
        '4268': 'X4 (F26)',
        '4459': 'Q5 2ª serie',
        '3709': 'Vivaro 2ª serie',
        '2807': 'X-MAX 300',
        '4916': 'VESPA 50 SUPER SPRINT',
        '498': 'TOURING ELECTRA GLIDE',
        '4834': 'dr 4.0',
        '4846': 'Mazda2 Hybrid',
        '3753': 'fortwo 2ª serie',
        '4052': 'Serie 1 (F20)',
        '692': 'MONSTER 600',
        '3911': '3008',
        '3095': 'A2',
        '2640': '146',
        '195': 'BURGMAN 400',
        '5098': 'ZT 350 D',
        '3647': 'M502',
        '4709': 'VESPA PX 150 E',
        '4044': 'Ypsilon 3ª serie',
        '1638': 'Beta Montecarlo',
        '878': 'VESPA 125 ET4',
        '4785': 'Kangoo 4ª serie',
        '281': 'T MAX',
        '4544': 'Duster 2ª serie',
        '4700': 'dr 5.0',
        '260': 'DT 125',
        '3553': 'Serie 3 (E90/E91)',
        '2450': 'NC 750',
        '4659': 'Kamiq',
        '4513': 'E-Pace (X540)',
        '366': 'SR 50',
        '253': 'AEROX 50',
        '4732': 'Mokka 2ª serie',
        '362': 'SPORTCITY 125',
        '4510': 'X3 (G01)',
        '5911': 'VALICO 900',
        '497': 'SPORTSTER 883',
        '3091': 'Premacy',
        '3759': 'Expert 3ª serie',
        '3845': 'Aveo 1ª serie',
        '4042': 'Range Rover Evoque',
        '4208': 'Ghibli',
        '4476': 'Civic 10ª serie',
        '3456': '407 1ª serie',
        '3081': 'Lybra',
        '98': 'BET & WIN 150',
        '1813': 'R 1100 GS',
        '536': 'W 650',
        '4634': 'Clio 5ª serie',
        '4913': '408',
        '2143': 'TIGER 800',
        '4281': 'e-up!',
        '5037': '3008 3ª serie',
        '1043': 'BEVERLY 400',
        '3502': 'Tucson',
        '889': 'X9 250',
        '4087': 'up!',
        '4796': 'Yaris Cross',
        '3605': '600',
        '9999999': 'modello con forzatura',
        '4259': 'C1 2ª serie',
        '3382': 'Touran',
        '3807': 'Focus 3ª serie',
        '3891': 'i20',
        '879': 'VESPA 125 L',
        '2757': 'Y 1.2 16V LS',
        '4649': 'Xceed',
        '2648': 'Galaxy 1ª serie',
        '2261': 'VISION 50',
        '3225': 'TRACER 7',
        '4443': '3008 2ª serie',
        '804': 'SILVER WING',
        '1012': 'GSR 600',
        '1127': '1098',
        '3335': 'Phedra',
        '3476': 'Altea',
        '1042': 'NEXUS 250',
        '2793': 'LEONCINO 500',
        '2780': 'Fiesta 3ª serie',
        '4869': 'Tonale',
        '4902': 'Classe GLC (X254)',
        '4924': 'e-C4 X',
        '3413': 'Vito (2ª serie)',
        '4279': 'X-Trail 3ª serie',
        '4531': 'Karoq',
        '4371': 'Astra 5ª serie',
        '3843': 'XC60 (2008--->)',
        '3879': 'Ka 2ª serie',
        '3031': 'TT',
        '4187': 'Octavia 3ª serie',
        '669': 'R 850 RT',
        '4093': 'Panda 3ª serie',
        '3851': 'Delta (2008--->)',
        '3631': 'Brera',
        '4091': 'Punto 4ª serie',
        '3018': 'SUPER CUB 125',
        '2972': 'Xsara',
        '4250': 'Classe V 2014--->',
        '2200': '19 2ª serie',
        '4639': 'Corsa 6ª serie',
        '4152': 'Golf 7ª serie',
        '4067': 'Yaris 3ª serie',
        '2788': 'X-ADV 750',
        '2543': 'SCRAMBLER',
        '5021': 'X2 (U10)',
        '3061': 'TENERE 700',
        '4804': 'Fabia 4ª serie',
        '3680': 'Ducato (4ª serie)',
        '4686': 'A3 4ª serie',
        '3321': 'DTX 360',
        '5063': 'E-Transit Custom',
        '3909': 'Scénic 3ª serie',
        '4961': 'Evo 5',
        '4653': 'Kuga 3ª serie',
        '956': 'X-MAX 250',
        '3947': 'Spark',
        '798': 'SH 125',
        '694': 'Defender',
        '538': 'Z 750',
        '4838': 'ZS (2021-->)',
        '1271': 'MULHACEN 125',
        '3261': 'V7 IV',
        '4408': 'Tiguan 2ª serie',
        '5070': 'Duster 3ª serie',
        '4381': 'F-Pace (X761)',
        '4367': 'X1 (F48)',
        '4441': 'Classe GLC Coupé',
        '779': 'DEAUVILLE',
        '4566': 'Rifter',
        '3797': 'GRANDE PUNTO PUNTO 1.4 T-JET 16V 180 CV',
        '4452': 'A3 SPORTBACK E-TRON SPB 40 E-TRON S TRO',
        '522': 'ER 5',
        '547': 'BOOSTER',
        '4917': 'HS',
        '2881': 'Passat 5ª serie',
        '2103': '106',
        '3808': 'Kangoo 3ª serie',
        '3269': 'TWEET 200',
        '2094': 'PEOPLE 300',
        '4603': 'RAV4 5ª serie',
        '4914': 'Austral',
        '3917': 'Yeti',
        '345': 'RS 125',
        '4029': 'Focus 4ª serie',
        '205': 'GSX HAYABUSA 1300',
        '272': 'MAJESTY 250',
        '4647': 'CX-30',
        '3997': 'Sportage 3ª serie',
        '1877': 'GSX 1250',
        '4363': 'Mini Clubman (F54)',
        '3236': 'H-100',
        '4300': 'Trafic 4ª serie',
        '4384': 'L200 (2015--->)',
        '3516': 'A4 3ª serie',
        '3590': 'Aygo',
        '2311': 'T MAX 530',
        '4269': '108',
        '4517': 'Polo 6ª serie',
        '358': 'SCARABEO 50',
        '4707': 'XT4',
        '1582': 'SYMPHONY 125',
        '4587': 'C5 Aircross',
        '355': 'SCARABEO 150',
        '5603': 'R 1300 GS',
        '4533': 'Kona',
        '3361': 'Daily (1996-2001)',
        '3042': 'Yaris',
        '4519': 'C3 Aircross',
        '870': 'NRG',
        '4273': 'Daily (2014--->)',
        '4511': 'Grandland X',
        '4275': 'Serie 2 Act. Tourer',
        '3958': 'ix35',
        '3999': 'V60 (2010---->)',
        '2064': 'AGILITY 200I',
        '4312': 'Twingo 3ª serie',
        '3408': 'Ypsilon',
        '3857': '500',
        '1069': 'VESPA 250 GTV',
        '3469': 'Classe SLK (R171)',
        '99': 'BET & WIN 250',
        '4133': 'B-Max',
        '2404': 'Punto',
        '409': 'RR ENDURO 50',
        '5183': 'ZS (2024-->)',
        '5611': 'V11 125',
        '343': 'PEGASO 650',
        '4751': 'Tucson 3ª serie',
        '3505': 'DESERTX',
        '4294': 'TT 3ª serie',
        '4724': 'Formentor',
        '4454': 'C-HR',
        '4063': 'Rio 3ª serie',
        '306': 'XV 535 VIRAGO',
        '3240': 'XEF 125',
        '4238': 'Classe C (W/S205)',
        '1585': 'SYMPHONY 50',
        '3024': 'Grand Vitara',
        '3954': 'CR-V 3ª serie 10-12',
        '4995': 'Z 350',
        '3689': 'Galaxy 2ª serie',
        '4799': 'T9L',
        '2599': 'FIDDLE III 125',
        '3875': 'Mégane 3ª serie',
        '3601': '159',
        '4058': 'Veloster',
        '4337': 'Serie 2 Cabrio(F23)',
        '4599': 'Corolla (2018--->)',
        '4674': 'VESPA P 200 E',
        '4232': 'Classe GLA (X156)',
        '3108': 'Voy./G.Voyager 3ª s',
        '3913': 'Polo 5ª serie',
        '227': 'V STROM DL 650',
        '3714': 'Qashqai 1ª serie',
        '3781': '500 (2007--->)',
        '3616': 'Doblò 2ª serie',
        '1827': 'K 100 RS',
        '2117': 'G 650 GS',
        '867': 'LIBERTY 125',
        '800': 'SH 50',
        '4928': 'Avenger',
        '2710': 'AGILITY 125I',
        '868': 'LIBERTY 150',
        '233': 'VZ MARAUDER 800',
        '2110': '125 DUKE',
        '1166': 'SH 300',
        '3164': 'Cherokee 2ª serie',
        '799': 'SH 150',
        '1482': 'VESPA 300 GTS',
        '192': 'AN BURGMAN 400',
        '4844': 'Sportage 5ª serie',
        '4356': 'Kadjar',
        '3007': 'Serie 3 (E46)',
        '774': 'CBR 1000',
        '2988': 'Kangoo 1ª serie',
        '308': 'XVS 1100A DRAG STAR',
        '3928': 'C3 2ª serie',
        '1393': 'PEOPLE 200I',
        '4681': 'Classe GLE Cpé C167',
        '4249': 'EcoSport',
        '5390': 'TRK 702',
        '4561': 'KUV100',
        '4496': 'Picanto 3ªs.(17-->)',
        '2991': '911 (996)',
        '3768': 'Wrangler 2ª serie',
        '4657': 'Juke 2ª serie',
        '3015': '650MT',
        '2496': 'CBR 650',
        '266': 'FZS 600 FAZER',
        '256': 'BW\'S',
        '2662': 'barchetta',
        '1280': 'CABALLERO REGOLARITA 50',
        '973': 'RR ENDURO 125',
        '245': 'SPEED TRIPLE',
        '3226': 'Stilo',
        '3906': 'Pixo',
        '1646': 'SFV GLADIUS 650',
        '3420': 'Focus 2/Focus C-Max',
        '3809': 'Nemo',
        '4211': '308 2ª serie',
        '1211': 'SL SHIVER 750',
        '3176': 'VALICO 500',
        '3170': '307',
        '5738': '',
        '3828': 'Picanto 2ª serie',
        '4430': 'Vito (4ª serie)',
        '277': 'NEO S',
        '3155': 'FELSBERG 125',
        '3376': 'Fiorino',
        '301': 'XT 660',
        '5622': 'SPEED 400',
        '3570': '107',
        '3572': 'Passat 6ª serie',
        '825': 'XL 650 V TRANSALP',
        '3505': 'Musa',
        '3780': '308 1ª serie',
        '150': 'PHANTOM 50',
        '4504': 'Crossland X',
        '4190': '2008',
        '4687': 'Yaris 4ª serie',
        '3457': 'X3 (E83)',
        '496': 'SPORTSTER 1200',
        '3388': 'A3 2ª serie',
        '897': 'MITO 125',
        '3676': 'S-Max',
        '1113': 'R 1200 R',
        '4168': 'Sandero 2ª serie',
        '4743': 'i20 3ª serie',
        '3770': 'Twingo 2ª serie',
        '2459': 'STREETZONE',
        '3370': 'Micra 3ª serie',
        '4575': 'A1 2ª serie',
        '4800': 'Serie 4 G.C. (G26)',
        '876': 'TYPHOON',
        '945': 'VESPA 125 LX',
        '2748': 'CROX 50',
        '3097': 'MP3 300 HPE',
        '3468': 'A6 3ª serie',
        '3147': '147',
        '3786': 'Mazda2 2ª serie',
        '3863': 'Massif (2008-2011)',
        '119': 'PEOPLE 50',
        '3579': 'RR Sport 1ª serie',
        '4156': 'Transit Custom',
        '4137': 'GT86',
        '4258': 'Serie 4 G.C. (F36)',
        '4122': 'V40',
        '4061': 'Daily (2011-2014)',
        '3888': 'Classe E (W/S212)',
        '3020': 'C 400 GT',
        '3626': 'Q7',
        '163': 'ELYSTAR 125',
        '3352': 'Fusion',
        '4144': 'Clio 4ª serie',
        '3415': 'Panda 2ª serie',
        '1623': 'BEVERLY 300',
        '3011': 'Seicento',
        '3946': 'DS3',
        '2967': 'R 1250 GS',
        '3854': 'MiTo',
        '1715': 'DOWNTOWN 300I',
        '3956': 'Giulietta (2010)',
        '283': 'TDM 900',
        '4525': 'Stonic',
        '4338': 'Vitara (2015)',
        '771': 'CB 500',
        '3571': 'C1',
        '3513': 'Classe A (W/C169)',
        '4330': 'Corsa 5ª serie',
        '3864': 'C4 2ª serie',
        '3530': 'Atom 2',
        '4301': 'forfour 2ªs. (W453)',
        '5084': 'SFIDA SR4 400',
        '2845': 'Classe SLK (R170)',
        '4665': 'Captur 2ª serie',
        '1130': 'GEOPOLIS 250',
        '3152': 'Doblò',
        '291': 'V MAX 1200',
        '5087': 'MG3',
        '4043': 'Freemont',
        '3769': 'Fabia 2ª serie',
        '3279': '4 Runner/Hilux 2ª',
        '2225': 'VISION 110',
        '389': 'ALP 200',
        '2057': 'TWEET 50',
        '661': 'R 1150 R',
        '4486': 'Rio 4ª serie',
        '1609': 'SW-T400',
        '1453': 'STELVIO 1200',
        '3040': 'Jimny',
        '4047': 'Master 5ª s.(10-->)',
        '4828': 'Range Rover 5ªserie',
        '856': 'BEVERLY 500',
        '3658': 'Note (2006-2013)',
        '3685': 'Daily (2006-2009)',
        '1164': 'PS 125',
        '911': 'VESPA 150 PX',
        '959': 'VESPA 250 GTS',
        '790': 'LEAD',
        '2409': 'Serie E (*124)',
        '2888': 'Ka 1ª serie',
        '2199': 'SR MAX 300',
        '3045': 'New Beetle',
        '3154': '',
        '3247': 'Porter 1ª/2ª serie',
        '2052': 'QUARTZ 50',
        '5096': '5 E-Tech Electric',
        '4342': 'XF 2ª serie (X260)',
        '3254': 'E4',
        '2413': '250 FREERIDE',
        '4236': 'Qashqai 2ª serie',
        '3439': 'Idea',
        '1900': 'MULTISTRADA 1200',
        '3789': 'Agila 2ª serie',
        '213': 'SV 650',
        '787': 'HORNET',
        '4024': 'Orlando',
        '2434': 'VESPA 50 PRIMAVERA',
        '4597': 'RR Evoque 2ª serie',
        '3763': 'C-Max 1ª serie',
        '537': 'Z 1000',
        '3721': 'NT 400/Cabstar 4ªs.',
        '3969': 'Meriva 2ª s.',
        '3283': 'Ibiza 3ª serie',
        '5391': 'T MAX 560',
        '4002': 'C-Max 2ª serie',
        '4498': 'Swift (2017--->)',
        '1173': 'X-CITY',
        '3242': 'XMF 125',
        '4326': 'Celerio',
        '5043': 'Tunland G7',
        '3540': 'CLASSIC 350',
        '2728': 'THRUXTON 1200',
        '5083': 'C3 4ª serie',
        '4702': 'Jazz 4ª serie \'20->',
        '2667': 'XSR 700',
        '3852': 'NP300',
        '4842': 'S-Cross 2ª serie',
        '5111': 'X3 (G45)',
        '3977': 'Juke',
        '3639': 'MX-5 3ª serie',
        '1196': 'FUOCO 500',
        '3221': 'Fiesta 4ª serie',
        '121': '2CV',
        '1138': 'CRE 50',
        '2242': 'BEVERLY 350',
        '3401': 'C2',
        '2518': 'MT-125',
        '2863': 'Marea',
        '912': 'VESPA 50 ET4',
        '4315': 'X6 (F16)',
        '1808': 'VFR 750 F',
        '354': 'SCARABEO 125',
        '4069': 'Fiesta 5ª serie Bs',
        '367': 'TUONO 1000',
        '2499': 'TWEET EVO 125',
        '2714': 'MEDLEY 125',
        '2915': 'Classe CLK (C/A208)',
        '4716': 'dr F35',
        '4233': 'Trans. Connect 2ª s',
        '3754': 'Bravo 2ª serie',
        '4610': 'T-Cross',
        '4282': 'Aygo 2ª serie',
        '3586': 'Classe B (T245)',
        '2902': 'Berlingo',
        '3968': 'A1/S1',
        '1208': 'SATELIS 400',
        '4595': 'Q3 2ª serie',
        '3517': 'C4',
        '4348': 'Karl',
        '2955': 'K-LIGHT 125',
        '3264': 'JET X 125',
        '4650': 'Defender (2019)',
        '2683': 'XSR 900',
        '116': 'PEOPLE 125',
        '4444': 'Q2',
        '3438': 'Golf 5ª serie',
        '4632': 'Serie 1 (F40)',
        '4555': '508 2ª serie',
        '2844': 'Elise',
        '322': 'YZF R1',
        '1212': 'HYPERMOTARD 1100',
        '146': 'MADISON 200',
        '4859': 'Aygo X',
        '3980': 'Punto Evo',
        '3628': 'Classe R (BR251)',
        '891': 'ZIP',
        '3653': 'RAV4 3ª serie',
        '819': 'VT 750 SHADOW',
        '3495': 'Serie 1 (E87)',
        '3128': '',
        '1464': 'MONSTER 696',
        '139': 'F 10',
        '944': 'VESPA 50 LX',
        '4721': '500 (2020--->)',
        '2633': 'DOWNTOWN 350I',
        '2323': 'VERSYS 1000',
        '4398': 'Sportage 4ª serie',
        '4242': 'Macan',
        '4134': 'Classe A (W176)',
        '4553': 'X4 (G02)',
        '4065': 'Classe B(T246/T242)',
        '697': 'MONSTER S4',
        '3826': 'C5 3ª serie',
        '3799': 'Tiguan',
        '2489': '1290 SUPER DUKE',
        '328': 'ATLANTIC 500',
        '487': 'DYNA SUPER GLIDE',
        '5062': 'Ypsilon 4ª serie',
        '2276': '1199 PANIGALE',
        '3521': 'Modus 1ª serie',
        '2435': 'VESPA 125 PRIMAVERA',
        '4360': '500 (2015--->)',
        '3265': 'MAXSYM TL 508',
        '4146': 'A3 3ª serie/S3',
        '3398': 'Punto 3ª serie',
        '3529': '147 2ª serie',
        '4142': '500L',
        '1213': 'STREET TRIPLE',
        '2341': '390 DUKE',
        '4359': 'Touran 3ª serie',
        '3861': 'Scirocco 2ª serie',
        '3010': 'Astra 2ª serie',
        '4974': 'Kona 2ªs. (2023-->)',
        '4491': 'Compass 2ª serie',
        '3384': 'Kangoo 2ª serie',
        '3391': 'Master 2ª serie',
        '3257': 'Combo (Corsa 3ª s.)',
        '3386': 'Meriva 1ª s.',
        '2715': 'MEDLEY 150',
        '4210': '500L Living',
        '3360': 'Daily (1992-1996)',
        '910': 'VESPA 125 PX',
        '4446': 'A5/S5 2a serie',
        '1639': 'Beta Berlina',
        '3266': 'CMX 1100 REBEL',
        '263': 'FZ6',
        '3859': 'Classe GLK (X204)',
        '1221': 'VESPA 125 S',
        '4178': 'Serie 3 G.T. (F34)',
        '4693': 'JOYRIDE 300',
        '3107': '890 DUKE',
        '4520': 'Arona',
        '4073': 'Mondeo 3ª serie Bs',
        '784': 'FORESIGHT',
        '3929': 'Astra 4ª serie',
        '855': 'BEVERLY 250',
        '4344': 'Q7 2ª serie',
        '3844': 'Ibiza 4ª serie',
        '1200': 'XEVO 250',
        '3599': 'Gr. Cherokee 2ª s.',
        '2990': 'Freelander 1ª serie',
        '4651': 'Korando 4ª serie',
        '1579': 'JOY MAX 300',
        '1613': 'MX-5 1ª serie',
        '5877': 'SRT 552',
        '3204': 'X-Trail 1ª serie',
        '3431': 'Picanto 1ª serie',
        '3902': 'Alto (2009--->)',
        '2849': 'CR-V 1ª serie 97-02',
        '3587': 'Fox',
        '3028': 'city coupé/cabrio',
        '866': 'LIBERTY 125',
        '4494': 'XC60 (2017--->)',
        '4534': 'DS7',
        '4668': 'Golf 8ª serie',
        '4362': 'Classe GLC (X253)',
        '3618': 'Grande Punto',
        '4230': 'Mini (F56)',
        '4501': 'Fiesta 6ª serie',
        '2716': 'ES2',
        '4150': 'Tourneo Custom',
        '4390': 'Tipo (2015--->)',
        '4770': 'Arkana',
        '5073': 'Swift (2024-->)',
        '3896': 'C3 Picasso',
        '1157': 'VERSYS 650',
        '2111': 'Cinquecento',
        '3096': 'Agila 1ª serie',
        '1220': 'VESPA 50 S',
        '1004': 'SILVER WING 400',
        '2309': 'BRUTALE 675',
        '2735': 'TR 50 SM',
        '3839': 'Sandero 1ª serie',
        '772': 'CBF 500',
        '3677': 'Caliber',
        '476': 'NEXUS 500',
        '4940': 'dr 3.0',
        '817': 'VT 600 SHADOW',
        '4426': 'GIULIA (2016) 2.9 T V6 AT8 QUADRIFOGLIO',
        '2823': 'CRUISYM 300',
        '4314': 'e-Golf',
        '3862': 'Serie 7 (F01/02/04)',
        '3425': 'Astra 3ª serie',
        '4276': 'C4 Cactus',
        '2641': 'NMAX 125',
        '3650': 'Sedici',
        '5926': '450 MT',
        '3918': 'X1 (E84)',
        '1070': 'FLY 100',
        '3830': 'i10 1ª serie',
        '3934': '5008',
        '1174': 'PHANTOM F12-R 50',
        '3308': 'Ducato (3ª serie)',
        '3979': 'Mini Countryman R60',
        '5136': 'T03',
        '883': 'VESPA 50 ET2',
        '462': '4',
        '2359': 'CBR 500',
        '3276': 'Daily (1999-2007)',
        '3800': 'Serie 1 Cabrio(E88)',
        '2142': 'K 1600 GTL',
        '3920': 'Punto Evo',
        '3498': 'H1',
        '2688': 'CRF1000L AFRICA TWIN',
        '4053': 'Q3',
        '3992': 'X3 (F25)',
        '4186': 'Captur',
        '3337': '9-3 2ª serie',
        '4588': 'Serie 3 (G20)',
        '2': 'Panda',
        '4753': 'Sandero 3ª serie',
        '4376': 'A4 5ª serie',
        '1184': 'CARNABY 125',
        '1031': 'LIBERTY S 200',
        '4991': 'ZR-V',
        '4009': 'ix20',
        '3869': 'Golf 6ª serie',
        '4325': 'i20 2ª serie',
        '4182': 'RAV4 4ª serie',
        '3970': 'Master 5ª serie',
        '293': 'WHY',
        '3964': 'Duster',
        '3008': 'Clio 2ª serie',
        '3883': 'iQ',
        '4437': 'Ka+',
        '3740': 'Scudo',
        '4205': 'C4 Picasso',
        '5057': 'e-C3',
        '2056': 'PCX 125',
        '4227': 'i10 2ª serie',
        '3066': 'LEONCINO 250',
        '4586': 'ceed',
        '337': 'LEONARDO 250',
        '1626': 'MOTARD 125',
        '4820': 'Taigo',
        '854': 'BEVERLY 200',
        '681': '749',
        '3333': 'V 435',
        '4765': 'EQA (H243)',
        '4170': 'Adam',
        '3231': 'FORZA 750',
        '3076': 'Strada',
        '3174': 'Trans/Tour/Bus 2000',
        '2786': 'MULTISTRADA 950',
        '4797': 'Bayon',
        '1625': 'MOTARD 50',
        '1602': 'AGILITY 150',
        '255': 'BT 1100',
        '1065': 'SX 50',
        '3243': 'TIGER 850',
        '4292': 'Passat 8ª serie',
        '666': 'R 1200 GS',
        '3581': 'Swift (2005-2010)',
        '3246': 'SH 350',
        '4831': 'CB 750',
        '3771': 'Mondeo 3ª serie',
        '4875': 'dr 6.0',
        '312': 'XVS 650A DRAG STAR',
        '2771': 'NMAX 155',
        '3125': 'Classe C (W/S203)',
        '249': 'TIGER',
        '2384': 'Ibiza 2ª serie',
        '': '', // Empty code
        '4299': 'fortwo 3ª s. (C453)',
        '273': 'MAJESTY 400',
        '4569': 'Q8',
        '4357': 'Tivoli',
        '4455': 'C3 3ª serie',
        '4683': 'Classe GLA (H247)',
        '3609': 'Clio 3ª serie',
        '3311': 'CR-V 2ª serie 02-07',
        '2797': 'AGILITY 150I',
        '869': 'LIBERTY 200',
        '2058': 'TWEET 125',
        '4256': 'Soul 2ª serie',
        '3259': 'Polo 4ª serie',
        '880': 'VESPA 150 ET4',
        '4552': 'Classe A (W177)',
        '3880': 'QUBO',
        '3591': 'Matiz 2ª serie',
        '4229': 'Tour. Connect 2ª s.',
        '3990': 'Touran 2ª serie',
        '4341': 'Outback 4ª serie',
        '4526': 'XC40 (2017--->)',
        '4746': 'VESPA 50 SPECIAL (V5B3)',
        '660': 'R 1150 GS',
        '3399': 'Serie 5 (E60/E61)',
        '3140': 'T MAX 560',
        '2390': 'Terrano II',
        '1126': 'MULTISTRADA 1100',
        '2456': 'R NINET',
        '4005': 'Micra 4ª serie',
        '2201': 'Ducato (2ª serie)',
        '902': 'ATLANTIC 200',
        '4068': 'C-Max 2ª serie Bs',
        '562': 'SKYLINER 250',
        '3803': 'Fiorino 2ª serie',
        '4524': 'T-Roc',
        '3343': 'Mégane 2ª serie',
        '997': 'MT 03',
        '996': 'X-MAX 125',
        '3693': 'Corsa 4ª serie',
        '4074': 'Ka 2ª serie Bs',
        '2473': 'MT-07',
        '4623': 'Classe CLA Coupé',
        '5044': 'VESPA PK 50',
        '147': 'MADISON 250',
        '4070': 'Focus 4ª serie Bs',
        '3712': 'C30 (2006-2012)',
        '974': 'K 1200 R',
        '4767': 'Qashqai 3ª serie',
        '4004': 'Passat 7ª serie',
        '4825': 'Astra 6ª serie',
        '2587': 'CB 125',
        '729': 'NEVADA 750',
        '4311': 'Renegade',
        '3642': 'Yaris 2ª serie',
        '3865': 'Fiesta 5ª serie',
        '4123': 'Mii',
        '3837': 'Berlingo 2ª serie',
        '4654': 'Classe GLB (X247)',
        '4458': 'Mini Countryman F60',
        '1501': 'SPORTCITY 300',
        '191': 'AN BURGMAN 250',
        '4613': 'S60 (2019-->)',
        '6020': 'HIMALAYAN 450',
        '4326': 'Fabia 3ª serie',
        '4472': 'Ignis (2016)',
        '3237': 'MULTISTRADA V4',
        '2393': 'X-MAX 400',
        '1528': 'Feroza',
        '2789': 'STREET SCRAMBLER',
        '2098': 'Serena',
        '2787': 'Z 650',
        '3756': 'Serie 1 (E81)',
        '204': 'GSX 750',
        '3027': '206',
        '4652': 'Puma',
        '174': 'Y10',
        '4611': 'Model 3',
        '3996': 'Swift (2010--->)',
        '2691': 'STREET TWIN',
        '5009': 'Tourneo Courier 2ªs',
        '3659': 'SX4',
        '4289': 'NX 1ª serie',
        '5717': 'SFIDA SR1 125',
        '395': 'EURO 350',
        '3036': 'Focus 1ª serie',
        '4473': 'Micra 5ª serie',
        '3241': 'C3 1ª serie',
        '4501': 'Ibiza 5ª serie',
        '813': 'VFR',
        '4313': 'Panda Cross',
        '123': 'VITALITY 50',
        '4368': 'Jazz 3ª serie \'15->',
        '3903': 'Soul 1ª serie',
        '3824': 'Splash',
        '207': 'GSX R 600',
        '4485': 'Range Rover Velar',
        '4471': 'Kodiaq',
        '1399': 'SOFTAIL CROSS BONES',
        '4022': 'Amarok',
        '4290': 'Vivaro 3ª serie',
        '4475': 'Stelvio',
        '3602': 'Zafira 2ª serie',
        '2588': 'DJANGO 125',
        '2917': 'Classe A (W/V168)',
        '4329': 'Mazda2 3ª serie',
        '2953': 'BN 125',
        '595': 'SMR 450 F'
    };

    return MODEL_CODES[modelCode] || '';
  }

  getPaymentMethodFromCode(paymentMethodCode) {
    const PAYMENT_METHOD_CODES = {
        'A': 'ASSEGNO',
        'B': 'BANCA TITOLO SINGOLO',
        'C': 'CONTANTE',
        'D': 'INCASSO DIREZIONE',
        'F': 'FINANZIAMENTO FINITALIA',
        'H': 'PAGAMENTO MULTICANALITà (CARTA DI CREDITO)',
        'J': 'POS AGENZIA',
        'K': 'REINVESTIMENTO',
        'L': 'LINK',
        'M': 'MISTO',
        'P': 'POSTA',
        'Q': 'ANTICIPAZIONE COPERTURA AGENTI',
        'R': 'BANCA/POSTA',
        'T': 'TRATTENUTA MENSILE CONVENZIONI TM00',
        'V': 'COPERTURA SENZA INCASSO',
        'W': 'BANCA TITOLI MULTIPLI',
        'X': 'TRATTENUTA MENSILE COMMISSIONI GCF',
        'Y': 'POS DIREZIONE'
    };

    return PAYMENT_METHOD_CODES[paymentMethodCode] || '';
  }

  getProductFromCode(productCode) {
    const PRODUCT_CODES = {
        '9080': 'PRODOTTO UNICO AUTO  RCA / ARD',
        '7301': 'CANE & GATTO',
        '7505': 'PRODOTTO UNICO MOBILITÀ',
        '9081': 'PRODOTTO UNICO AUTO  ARD',
        '1206': 'INFORTUNI',
        '1268': 'SALUTE',
        '7267': 'CASA',
        '2215': 'PRODOTTO UNICO VIAGGI TEMPORANEO',
        '7266': 'FAMIGLIA'
    };

    return PRODUCT_CODES[productCode] || '';
}

  formatDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

  // Permission check method
  checkPermission(type) {
    switch (type) {
      case "visualizzazione":
        return hasVisualizzazionePermission1 || hasVisualizzazionePermission2;
      case "variazione":
        return (
          hasVariazionePermission1 ||
          hasVariazionePermission2 ||
          hasVariazionePermission3 ||
          hasVariazionePermission4 ||
          hasVariazionePermission5 ||
          hasVariazionePermission6 ||
          hasVariazionePermission7 ||
          hasVariazionePermission8 ||
          hasVariazionePermission9 ||
          hasVariazionePermission10 ||
          hasVariazionePermission11 ||
          hasVariazionePermission12 ||
          hasVariazionePermission13 ||
          hasVariazionePermission14 ||
          hasVariazionePermission15 ||
          hasVariazionePermission16 ||
          hasVariazionePermission17 ||
          hasVariazionePermission18 ||
          hasVariazionePermission19 ||
          hasVariazionePermission20 ||
          hasVariazionePermission21 ||
          hasVariazionePermission22 ||
          hasVariazionePermission23 ||
          hasVariazionePermission24 ||
          hasVariazionePermission25 ||
          hasVariazionePermission26 ||
          hasVariazionePermission27 ||
          hasVariazionePermission28 ||
          hasVariazionePermission29 ||
          hasVariazionePermission30 ||
          hasVariazionePermission31 ||
          hasVariazionePermission32 ||
          hasVariazionePermission33 ||
          hasVariazionePermission34 ||
          hasVariazionePermission35 ||
          hasVariazionePermission36 ||
          hasVariazionePermission37 ||
          hasVariazionePermission38 ||
          hasVariazionePermission39 ||
          hasVariazionePermission40 ||
          hasVariazionePermission41 ||
          hasVariazionePermission42 ||
          hasVariazionePermission43 ||
          hasVariazionePermission44 ||
          hasVariazionePermission45
        );
      case "sostituzione":
        return (
          hasSostituzionePermission1 ||
          hasSostituzionePermission2 ||
          hasSostituzionePermission3 ||
          hasSostituzionePermission4 ||
          hasSostituzionePermission5 ||
          hasSostituzionePermission6 ||
          hasSostituzionePermission7 ||
          hasSostituzionePermission8 ||
          hasSostituzionePermission9 ||
          hasSostituzionePermission10 ||
          hasSostituzionePermission11 ||
          hasSostituzionePermission12
        );
      case "apertura_sinistro":
        return hasCustomPermission;
      default:
        return false;
    }
  }

  // Getters for template use
  get hasVisualizzazionePermission() {
    return this.checkPermission("visualizzazione");
  }

  get hasVariazionePermission() {
    return this.checkPermission("variazione");
  }

  get hasSostituzionePermission() {
    return this.checkPermission("sostituzione");
  }

  get hasAperturaSinistroPermission() {
    return this.checkPermission("apertura_sinistro");
  }

  @wire(CurrentPageReference)
  getStateParameters(currentPageReference) {
    if (currentPageReference && currentPageReference.state) {
      const agency = currentPageReference.state.c__agency;
      if (agency === 'other') {
        this.agencyType = 'other';
      } else {
        this.agencyType = 'same';
      }
    }
  }

  get agencyTypeIsOther() {
    return this.agencyType === 'other';
  }

  get isSameUserAgency() {
    return this.agencyType === 'same';
  }

  getTariffaFromCode(code) {
    const normalizedCode = '1' + this.normalizeCode(code);
    const tariffaMap = {
      "1000000TU31004FV": "INVESTIMENTO MULTIGEST (1-TU31004FV)",
      "1000000TU31004PR": "INVESTIMENTO MULTIGEST (1-TU31004PR)",
      "1000000TU31004PV": "INVESTIMENTO MULTIGEST (1-TU31004PV)",
      "1000000TU31004RE": "INVESTIMENTO MULTIGEST (1-TU31004RE)",
      "100000TU31004RFV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31004RFV)",
      "100000TU31004RPR": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31004RPR)",
      "100000TU31004RPV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31004RPV)",
      "100000TU31004RRE": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31004RRE)",
      "1000000TU31003FV": "INVESTIMENTO MULTIGEST (1-TU31003FV)",
      "1000000TU31003PR": "INVESTIMENTO MULTIGEST (1-TU31003PR)",
      "1000000TU31003RE": "INVESTIMENTO MULTIGEST (1-TU31003RE)",
      "100000TU31003RFV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31003RFV)",
      "100000TU31003RPR": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31003RPR)",
      "100000TU31003RRE": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31003RRE)",
      "1000000TU31002FV": "INVESTIMENTO MULTIGEST (1-TU31002FV)",
      "1000000TU31002PR": "INVESTIMENTO MULTIGEST (1-TU31002PR)",
      "1000000TU31002RE": "INVESTIMENTO MULTIGEST (1-TU31002RE)",
      "100000TU31002RFV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31002RFV)",
      "100000TU31002RPR": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31002RPR)",
      "100000TU31002RRE": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31002RRE)",
      "100000000TU20021": "UNIPOLSAI VITA DECRESCENTE - PA (1-TU20021)",
      "100000000TU20022": "UNIPOLSAI VITA COSTANTE - PU (1-TU20022)",
      "100000000TU20023": "UNIPOLSAI VITA DECRESCENTE - PU (1-TU20023)",
      "100000000TU30030": "UNIPOLSAI INVESTIMENTO VINCITA (1-TU30030)",
      "1000000TU31001FV": "INVESTIMENTO MULTIGEST (1-TU31001FV)",
      "1000000TU31001PR": "INVESTIMENTO MULTIGEST (1-TU31001PR)",
      "1000000TU31001RD": "INVESTIMENTO MULTIGEST (1-TU31001RD)",
      "100000TU31001RFV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31001RFV)",
      "100000TU31001RPR": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31001RPR)",
      "100000TU31001RRD": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31001RRD)",
      "1000000TU31000FV": "INVESTIMENTO MULTIGEST (1-TU31000FV)",
      "1000000TU31000PR": "INVESTIMENTO MULTIGEST (1-TU31000PR)",
      "100000TU31000RFV": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31000RFV)",
      "100000TU31000RPR": "INVESTIMENTO MULTIGEST - FIDELITY (1-TU31000RPR)",
      "10000TU60003DIFF": "YOU RISPARMIO BIMBO (1-TU60003DIFF)",
      "100000000TU30029": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30029)",
      "10000000TU30029R": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY (1-TU30029R)",
      "100000000TU70009": "UNIPOLSAI RENDITA (1-TU70009)",
      "100000000TU70015": "UNIPOLSAI RENDITA - CON CONTROASSICURAZIONE (1-TU70015)",
      "1000000000U70009": "UNIPOLSAI RENDITA (1-U70009)",
      "1000000000U70015": "UNIPOLSAI RENDITA - CON CONTROASSICURAZIONE (1-U70015)",
      "100000000TU30028": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30028)",
      "10000000TU30028R": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY (1-TU30028R)",
      "100000000TU10034": "UNIPOLSAI RISPARMIO ATTIVO (1-TU10034)",
      "10000000TU10034C": "UNIPOLSAI RISPARMIO ATTIVO - CONVENZIONE (1-TU10034C)",
      "10000000TU10034K": "UNIPOLSAI RISPARMIO ATTIVO - COOP (1-TU10034K)",
      "1000000TU30026RD": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY DANNI (1-TU30026RD)",
      "10000000TU30025R": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY (1-TU30025R)",
      "1000000TU30025RD": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY DANNI (1-TU30025RD)",
      "1000000TU30024RD": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY DANNI (1-TU30024RD)",
      "10000000TU30024R": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY (1-TU30024R)",
      "10000000TU30026R": "UNIPOLSAI INVESTIMENTO GARANTITO FIDELITY (1-TU30026R)",
      "100000000TU20020": "UNIPOLSAI VITA SERENA (1-TU20020)",
      "100000000TU20019": "UNIPOLSAI VITA (1-TU20019)",
      "1000000TU20019DD": "UNIPOLSAI VITA - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20019DD)",
      "1000000TU20019GF": "UNIPOLSAI VITA - GARANZIA ACCESSORIA FAMIGLIA (1-TU20019GF)",
      "10000TU20019INF1": "UNIPOLSAI VITA - GARANZIA COMPLEMENTARE INFORTUNI (1-TU20019INF1)",
      "10000TU20019INF2": "UNIPOLSAI VITA - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20019INF2)",
      "100000000TU30023": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30023)",
      "100000000TU30022": "UNIPOLSAI INVESTIMENTO DEDICATO (1-TU30022)",
      "100000000TU30021": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30021)",
      "10000TJ348DIFFBS": "MISTA DIFFERIMENTI (1-TJ348DIFFBS)",
      "100000000TU30020": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA 4 (1-TU30020)",
      "100000000TU30019": "UNIPOLSAI RISPARMIO PROTETTO (1-TU30019)",
      "10000000TU30019C": "UNIPOLSAI RISPARMIO PROTETTO - CONVENZIONE (1-TU30019C)",
      "100000TU30019CGF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30019CGF)",
      "1000TU30019CINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30019CINF1)",
      "1000TU30019CINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30019CINF2)",
      "1000000TU30019GF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30019GF)",
      "10000TU30019INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30019INF1)",
      "10000TU30019INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30019INF2)",
      "100000000TU60008": "UNIPOLSAI RISPARMIO BONUS (1-TU60008)",
      "10000000TU60008C": "UNIPOLSAI RISPARMIO BONUS - CONVENZIONI (1-TU60008C)",
      "100000000TU80002": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80002)",
      "10000000TU80002A": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80002A)",
      "1000000TU80002AC": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80002AC)",
      "10000000TU80002C": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80002C)",
      "100000000TU20018": "TCM A PREMIO UNICO APE (1-TU20018)",
      "100000000TU30018": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA 3 (1-TU30018)",
      "10000000TU30017R": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA 2 (1-TU30017R)",
      "1000000TU30017R1": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA 2 (1-TU30017R1)",
      "100000000TU30016": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA (1-TU30016)",
      "10000000TU30016C": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA - CONVENZIONE (1-TU30016C)",
      "10000000TU30016K": "UNIPOLSAI INVESTIMENTO GARANTITO EXTRA - CONVENZIONE (1-TU30016K)",
      "100000000TU10033": "UNIPOLSAI RISPARMIO ATTIVO (1-TU10033)",
      "10000000TU10033C": "UNIPOLSAI RISPARMIO ATTIVO - CONVENZIONE (1-TU10033C)",
      "10000000TU10033K": "UNIPOLSAI RISPARMIO ATTIVO - COOP (1-TU10033K)",
      "100000000TU30015": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30015)",
      "10000000TU30015C": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30015C)",
      "10000000TU30015K": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30015K)",
      "10000000TU30015R": "UNIPOLSAI INVESTIMENTO GARANTITO - FIDELITY (1-TU30015R)",
      "100000000TU30014": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30014)",
      "10000000TU30014C": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30014C)",
      "10000000TU30014K": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30014K)",
      "10000000TU30014R": "UNIPOLSAI INVESTIMENTO GARANTITO - FIDELITY (1-TU30014R)",
      "100000000TU30012": "PREMIO UNICO PER GDO (1-TU30012)",
      "100000000TU10032": "UNIPOLSAI RISPARMIO ATTIVO (1-TU10032)",
      "10000000TU10032C": "UNIPOLSAI RISPARMIO ATTIVO - CONVENZIONE (1-TU10032C)",
      "10000000TU10032K": "UNIPOLSAI RISPARMIO ATTIVO - COOP (1-TU10032K)",
      "100000000TU30011": "UNIPOLSAI INVESTIMENTO GARANTITO # SPECIALE PROTEZIONE 2015 (1-TU30011)",
      "100000000TU10030": "UNIPOLSAI RISPARMIO INSIEME (1-TU10030)",
      "10000000TU10030C": "UNIPOLSAI RISPARMIO INSIEME - CONVENZIONE (1-TU10030C)",
      "100000000TU10031": "VITA INTERA A PREMI ANNUI COSTANTI LIMITATI (1-TU10031)",
      "100000000TU30009": "UNIPOLSAI VITA - EVOLUTION (1-TU30009)",
      "10000000TU30009C": "UNIPOLSAI VITA - EVOLUTION - CONVENZIONE (1-TU30009C)",
      "100000000TU30010": "UNIPOLSAI RISPARMIO PROTETTO (1-TU30010)",
      "10000000TU30010C": "UNIPOLSAI RISPARMIO PROTETTO - CONVENZIONE (1-TU30010C)",
      "100000TU30010CGF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30010CGF)",
      "1000TU30010CINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30010CINF1)",
      "1000TU30010CINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30010CINF2)",
      "1000000TU30010GF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30010GF)",
      "10000TU30010INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30010INF1)",
      "10000TU30010INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30010INF2)",
      "100000000TU60007": "UNIPOLSAI RISPARMIO BONUS (1-TU60007)",
      "10000000TU60007C": "UNIPOLSAI RISPARMIO BONUS - CONVENZIONI (1-TU60007C)",
      "100000000TU80001": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80001)",
      "10000000TU80001A": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80001A)",
      "1000000TU80001AC": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80001AC)",
      "10000000TU80001C": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80001C)",
      "100000000TU30008": "UNIPOLSAI INVESTIMENTO GARANTITO (1-TU30008)",
      "10000000TU30008C": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30008C)",
      "10000000TU30008K": "UNIPOLSAI INVESTIMENTO GARANTITO - CONVENZIONE (1-TU30008K)",
      "10000000TU30008R": "UNIPOLSAI INVESTIMENTO GARANTITO - FIDELITY (1-TU30008R)",
      "1000000TU4C0VATT": "DIFFERIMENTO (1-TU4C0VATT)",
      "100000000TU10027": "RISPARMIINSIEME (1-TU10027)",
      "10000000TU10027C": "RISPARMIINSIEME - CONVENZIONE (1-TU10027C)",
      "100000000TU10028": "VITA INTERA A PREMI ANNUI COSTANTI LIMITATI (1-TU10028)",
      "100000000TU30006": "YOU VITA - EVOLUTION (1-TU30006)",
      "10000000TU30006C": "YOU VITA - EVOLUTION - CONVENZIONE (1-TU30006C)",
      "100000000TU30007": "UNIPOLSAI RISPARMIO PROTETTO (1-TU30007)",
      "10000000TU30007C": "UNIPOLSAI RISPARMIO PROTETTO - CONVENZIONE (1-TU30007C)",
      "100000TU30007CGF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30007CGF)",
      "1000TU30007CINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30007CINF1)",
      "1000TU30007CINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30007CINF2)",
      "1000000TU30007GF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30007GF)",
      "10000TU30007INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30007INF1)",
      "10000TU30007INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30007INF2)",
      "100000000TU10026": "UNIPOLSAI INVESTIMENTO TARIFFA U10026 (1-TU10026)",
      "1000000TU16003BG": "UNIPOLSAI INVESTIMENTO MIX 2 (1-TU16003BG)",
      "1000000TU16003VU": "UNIPOLSAI INVESTIMENTO MIX 2 (1-TU16003VU)",
      "100000000TU20015": "UNIPOLSAI VITA SMART - FUMATORI (1-TU20015)",
      "100000000TU20016": "UNIPOLSAI VITA SMART - NON FUMATORI (1-TU20016)",
      "100000000TU20017": "UNIPOLSAI VITA (1-TU20017)",
      "1000000TU20017GF": "UNIPOLSAI VITA - GARANZIA ACCESSORIA FAMIGLIA (1-TU20017GF)",
      "10000TU20017INF1": "UNIPOLSAI VITA - GARANZIA COMPLEMENTARE INFORTUNI (1-TU20017INF1)",
      "10000TU20017INF2": "UNIPOLSAI VITA - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20017INF2)",
      "100000000TU60006": "UNIPOLSAI RISPARMIO BONUS (1-TU60006)",
      "10000000TU60006C": "UNIPOLSAI RISPARMIO BONUS - CONVENZIONI (1-TU60006C)",
      "100000000TU80000": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80000)",
      "10000000TU80000A": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80000A)",
      "1000000TU80000AC": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80000AC)",
      "10000000TU80000C": "UNIPOLSAI RISPARMIO GIOVANE (1-TU80000C)",
      "100000000TU10025": "UNIPOLSAI RISPARMIO ATTIVO (1-TU10025)",
      "10000000TU10025C": "UNIPOLSAI RISPARMIO ATTIVO - CONVENZIONE (1-TU10025C)",
      "10000000TU10025K": "UNIPOLSAI RISPARMIO ATTIVO - COOP (1-TU10025K)",
      "1000000TU30004GF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30004GF)",
      "100000000TU30005": "UNIPOLSAI INVESTIMENTO FREE (1-TU30005)",
      "100000TU16002CFV": "UNIPOLSAI INVESTIMENTO MIX 4 - CONVENZIONE (1-TU16002CFV)",
      "100000TU16002CPR": "UNIPOLSAI INVESTIMENTO MIX 4 - CONVENZIONE (1-TU16002CPR)",
      "100000TU16002CSV": "UNIPOLSAI INVESTIMENTO MIX 4 - CONVENZIONE (1-TU16002CSV)",
      "100000TU16002CVA": "UNIPOLSAI INVESTIMENTO MIX 4 - CONVENZIONE (1-TU16002CVA)",
      "1000000TU16002FV": "UNIPOLSAI INVESTIMENTO MIX 4 (1-TU16002FV)",
      "100000TU16002KFV": "UNIPOLSAI INVESTIMENTO MIX 4 - COOP (1-TU16002KFV)",
      "100000TU16002KPR": "UNIPOLSAI INVESTIMENTO MIX 4 - COOP (1-TU16002KPR)",
      "100000TU16002KSV": "UNIPOLSAI INVESTIMENTO MIX 4 - COOP (1-TU16002KSV)",
      "100000TU16002KVA": "UNIPOLSAI INVESTIMENTO MIX 4 - COOP (1-TU16002KVA)",
      "1000000TU16002PR": "UNIPOLSAI INVESTIMENTO MIX 4 (1-TU16002PR)",
      "100000TU16002RFV": "UNIPOLSAI INVESTIMENTO MIX 4 - FIDELITY (1-TU16002RFV)",
      "100000TU16002RPR": "UNIPOLSAI INVESTIMENTO MIX 4 - FIDELITY (1-TU16002RPR)",
      "100000TU16002RSV": "UNIPOLSAI INVESTIMENTO MIX 4 - FIDELITY (1-TU16002RSV)",
      "100000TU16002RVA": "UNIPOLSAI INVESTIMENTO MIX 4 - FIDELITY (1-TU16002RVA)",
      "1000000TU16002SV": "UNIPOLSAI INVESTIMENTO MIX 4 (1-TU16002SV)",
      "1000000TU16002VA": "UNIPOLSAI INVESTIMENTO MIX 4 (1-TU16002VA)",
      "100000000TU10023": "UNIPOSAI INVESTIMENTO FLEX (1-TU10023)",
      "10000000TU10023C": "UNIPOSAI INVESTIMENTO FLEX - CONVENZIONE (1-TU10023C)",
      "10000000TU10023R": "UNIPOSAI INVESTIMENTO FLEX - FIDELITY (1-TU10023R)",
      "100000000TU10024": "UNIPOLSAI INVESTIMENTO COUPON (1-TU10024)",
      "10000000TU10024C": "UNIPOLSAI INVESTIMENTO COUPON - CONVENZIONE (1-TU10024C)",
      "10000000TU10024K": "UNIPOLSAI INVESTIMENTO COUPON - COOP (1-TU10024K)",
      "10000000TU10024R": "UNIPOLSAI INVESTIMENTO COUPON - FIDELITY (1-TU10024R)",
      "100000000TU30004": "UNIPOLSAI RISPARMIO PROTETTO (1-TU30004)",
      "10000000TU30004C": "UNIPOLSAI RISPARMIO PROTETTO - CONVENZIONE (1-TU30004C)",
      "100000TU30004CGF": "GARANZIA COMPLEMENTARE GARANZIA FAMIGLIA (1-TU30004CGF)",
      "1000TU30004CINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30004CINF1)",
      "1000TU30004CINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30004CINF2)",
      "10000TU30004INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30004INF1)",
      "10000TU30004INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30004INF2)",
      "10000000TU30005R": "UNIPOLSAI INVESTIMENTO FREE - FIDELITY (1-TU30005R)",
      "10000CV71PS3FI21": "OPZIONE DI RENDITA (1-CV71PS3FI21)",
      "10000CV71PS3FI39": "OPZIONE DI RENDITA (1-CV71PS3FI39)",
      "10000CV71PS4FI05": "OPZIONE DI RENDITA (1-CV71PS4FI05)",
      "10000CV71PS4FI11": "OPZIONE DI RENDITA (1-CV71PS4FI11)",
      "10000CV71PS4FI30": "OPZIONE DI RENDITA (1-CV71PS4FI30)",
      "100000000INFCON1": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFCON1)",
      "100000000INFCON2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFCON2)",
      "100000000INFINC1": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFINC1)",
      "100000000INFINC2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFINC2)",
      "100000000INFINC3": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFINC3)",
      "100000000INFINC4": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFINC4)",
      "10000000000INFS1": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFS1)",
      "10000000000INFS3": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-INFS3)",
      "1000000RV613FI01": "OPZIONE DI RENDITA (1-RV613FI01)",
      "1000000RV613FI39": "OPZIONE DI RENDITA (1-RV613FI39)",
      "10000000RV613IND": "OPZIONE DI RENDITA (1-RV613IND)",
      "10000000RV614IND": "OPZIONE DI RENDITA (1-RV614IND)",
      "10000RV71P25FI06": "OPZIONE DI RENDITA (1-RV71P25FI06)",
      "10000RV71PS0FI06": "OPZIONE DI RENDITA (1-RV71PS0FI06)",
      "10000RV71PS3FI01": "OPZIONE DI RENDITA (1-RV71PS3FI01)",
      "10000RV71PS3FI21": "OPZIONE DI RENDITA (1-RV71PS3FI21)",
      "10000RV71PS3FI39": "OPZIONE DI RENDITA (1-RV71PS3FI39)",
      "10000RV71PS4FI01": "OPZIONE DI RENDITA (1-RV71PS4FI01)",
      "10000RV71PS4FI02": "OPZIONE DI RENDITA (1-RV71PS4FI02)",
      "10000RV71PS4FI03": "OPZIONE DI RENDITA (1-RV71PS4FI03)",
      "10000RV71PS4FI04": "OPZIONE DI RENDITA (1-RV71PS4FI04)",
      "10000RV71PS4FI05": "OPZIONE DI RENDITA (1-RV71PS4FI05)",
      "10000RV71PS4FI06": "OPZIONE DI RENDITA (1-RV71PS4FI06)",
      "10000RV71PS4FI21": "OPZIONE DI RENDITA (1-RV71PS4FI21)",
      "10000RV71PS4FI30": "OPZIONE DI RENDITA (1-RV71PS4FI30)",
      "10000RVRG482FI30": "OPZIONE DI RENDITA (1-RVRG482FI30)",
      "10000RVRG482FI40": "OPZIONE DI RENDITA (1-RVRG482FI40)",
      "10000RVRG483FI39": "OPZIONE DI RENDITA (1-RVRG483FI39)",
      "10000RVRG483FI40": "OPZIONE DI RENDITA (1-RVRG483FI40)",
      "10000000000T0101": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0101)",
      "10000000000T0106": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A (1-T0106)",
      "10000000000T0107": "VITA INTERA A CAPITALE RIVALUTABILE A PREMI (1-T0107)",
      "10000000000T0108": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A (1-T0108)",
      "10000000000T0109": "NO LIMITS - ASS. IN CASO DI MORTE A VITA INTERA A (1-T0109)",
      "10000000000T0110": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A (1-T0110)",
      "10000000000T0111": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0111)",
      "10000000000T0113": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0113)",
      "10000000000T0114": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A (1-T0114)",
      "10000000000T0115": "NO LIMITS - ASS. IN CASO DI MORTE A VITA INTERA A (1-T0115)",
      "10000000000T0116": "VITA INTERA A CAPITALE RIVALUTABILE A PREMI (1-T0116)",
      "10000000000T0117": "NO LIMITS - ASS. IN CASO DI MORTE A VITA INTERA A (1-T0117)",
      "10000000000T0118": "VITA INTERA A CAPITALE RIVALUTABILE A PREMI RICORR (1-T0118)",
      "10000000000T0119": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA CON (1-T0119)",
      "10000000000T0121": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0121)",
      "10000000000T0122": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0122)",
      "10000000000T0123": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T0123)",
      "10000000000T0135": "DOPPIO SEGNO POSITIVO (1-T0135)",
      "100000000T0135GS": "DOPPIO SEGNO POSITIVO (1-T0135GS)",
      "10000000000T0136": "DOPPIO SEGNO POSITIVO (ED. 2003) (1-T0136)",
      "100000000T0136GS": "DOPPIO SEGNO POSITIVO (ED. 2003) (1-T0136GS)",
      "10000000000T0137": "DOPPIO SEGNO POSITIVO (ED. 2003/ TASSO FISSO 4%) (1-T0137)",
      "100000000T0137GS": "DOPPIO SEGNO POSITIVO (ED. 2003/ TASSO FISSO 4%) (1-T0137GS)",
      "10000000000T0138": "DOPPIO SEGNO POSITIVO (ED. 2004/ TASSO FISSO 3.9%) (1-T0138)",
      "100000000T0138GS": "DOPPIO SEGNO POSITIVO (ED. 2004/ TASSO FISSO 3.9%) (1-T0138GS)",
      "10000000000T0226": "TEMPORANEA CASO MORTE DEL DEBITO RESIDO (1-T0226)",
      "10000000000T0231": "TEMPORANEA CASO MORTE A CAPITALE (1-T0231)",
      "10000000000T0233": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T0233)",
      "10000000000T0234": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T0234)",
      "10000000000T0235": "TEMPORANEA CASO MORTE AD ANNUALITA' TEMPORANEE (1-T0235)",
      "10000000000T0240": "TEMPORANEA CASO MORTE A CAPITALE (1-T0240)",
      "10000000000T0242": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T0242)",
      "10000000000T0244": "TEMPORANEA CASO MORTE A CAPITALE (1-T0244)",
      "10000000000T0246": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T0246)",
      "10000000000T0265": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T0265)",
      "10000000000T0275": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T0275)",
      "10000000000T0301": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T0301)",
      "10000000000T0302": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-T0302)",
      "10000000000T0303": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T0303)",
      "10000000000T0304": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-T0304)",
      "10000000000T0313": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T0313)",
      "10000000000T0314": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-T0314)",
      "10000000000T0323": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE 3% (1-T0323)",
      "10000000000T0324": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE 3% (1-T0324)",
      "10000000000T0333": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE 2% (1-T0333)",
      "10000000000T0334": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE 2% (1-T0334)",
      "10000000000T0335": "MISTA RIVALUTABILE A PREMIO UNICO (1-T0335)",
      "10000000000T0382": "ASSICURAZIONE MISTA CON INDICIZZAZIONE DEL (1-T0382)",
      "10000000000T0401": "TERMINE FISSO CON RIVALUTAZIONE ANNUA DEL CAPITALE (1-T0401)",
      "10000000000T0402": "TERMINE FISSO CON RIVALUTAZIONE ANNUA DEL CAPITALE (1-T0402)",
      "10000000000T0404": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T0404)",
      "10000000000T0412": "TERMINE FISSO CON RIVALUTAZIONE ANNUA DEL CAPITALE (1-T0412)",
      "10000000000T0414": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T0414)",
      "10000000000T0422": "TERMINE FISSO CON RIVALUTAZIONE ANNUA DEL CAPITALE (1-T0422)",
      "10000000000T0424": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T0424)",
      "10000000000T0502": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0502)",
      "10000000T0505MG2": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO UNICO (1-T0505MG2)",
      "10000000T0505MG3": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO UNICO (1-T0505MG3)",
      "10000000T0505MG4": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO UNICO (1-T0505MG4)",
      "10000000000T0511": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0511)",
      "10000000000T0513": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0513)",
      "10000000000T0521": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0521)",
      "10000000000T0522": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0522)",
      "10000000000T0523": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T0523)",
      "10000000000T0901": "RENDITA VITALIZIA DIFFERITA CON CONTROASSICUR. E (1-T0901)",
      "10000000000T0902": "RENDITA VITALIZIA DIFFERITA CON CONTROASSICURAZ. (1-T0902)",
      "10000000000T0903": "RENDITA VITALIZIA DIFFERITA CON CONTROASSICUR. (1-T0903)",
      "10000000000T0904": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T0904)",
      "10000000000T0908": "RENDITA VITALIZIA D'OPZIONE IMMEDIATA RIVALUTABILE (1-T0908)",
      "10000000000T0914": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T0914)",
      "10000000000T0924": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T0924)",
      "10000000000T0928": "RENDITA VITALIZIA IMMEDIATA RIVALUTABILE SENZA (1-T0928)",
      "10000000000T0970": "RENDITA VITALIZIA DIFFERITA CON CONTROASS. A PREMI (1-T0970)",
      "10000000000T0980": "RENDITA VITALIZIA DIFFERITA CON CONTROASSIC. (1-T0980)",
      "10000000000T106A": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T106A)",
      "10000000000T109A": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A PRE (1-T109A)",
      "10000000000T115A": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T115A)",
      "10000000000T115B": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T115B)",
      "10000000000T115C": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-T115C)",
      "10000000000T119A": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A PRE (1-T119A)",
      "10000000000T119B": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A PRE (1-T119B)",
      "10000000000T200B": "TEMPORANEA CASO MORTE A CAPITALE (1-T200B)",
      "10000000000T278A": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T278A)",
      "10000000000T278B": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T278B)",
      "10000000000T280A": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T280A)",
      "10000000000T280B": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T280B)",
      "10000000000T287B": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-T287B)",
      "10000000000T290B": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T290B)",
      "10000000000T299B": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-T299B)",
      "10000000000T311A": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE CON (1-T311A)",
      "10000000000T311B": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE CON (1-T311B)",
      "10000000000T318A": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T318A)",
      "10000000000T318B": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T318B)",
      "10000000000T342A": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T342A)",
      "10000000000T342B": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T342B)",
      "10000000000T372B": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T372B)",
      "10000000000T372C": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-T372C)",
      "10000000000T382A": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-T382A)",
      "10000000000T382B": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-T382B)",
      "10000000000T391A": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE CON (1-T391A)",
      "10000000T391BMG0": "MISTA RIVALUTABILE A PREMIO ANNUO CON TERMINAL (1-T391BMG0)",
      "10000000T391BMG1": "MISTA RIVALUTABILE A PREMIO ANNUO CON TERMINAL (1-T391BMG1)",
      "10000000000T391C": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE CON (1-T391C)",
      "10000000000T391W": "MISTA RIVALUTABILE A PREMIO ANNUO CON TERMINAL (1-T391W)",
      "10000000000T392A": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T392A)",
      "10000000T392BMG0": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T392BMG0)",
      "10000000T392BMG1": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T392BMG1)",
      "10000000000T392C": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-T392C)",
      "10000000000T399A": "MISTA IMMEDIATA A PREMIO UNICO CON RIVALUTAZIONE (1-T399A)",
      "10000000000T399C": "MISTA IMMEDIATA A PREMIO UNICO CON RIVALUTAZIONE (1-T399C)",
      "10000000000T402T": "MISTA A PREMI PERIODICI ED A PRESTAZIONI RIVALUTAB (1-T402T)",
      "10000000000T430A": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T430A)",
      "10000000000T430B": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T430B)",
      "10000000000T430C": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-T430C)",
      "10000000T546AMG0": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T546AMG0)",
      "10000000T546AMG1": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T546AMG1)",
      "10000T546AMG1BIS": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T546AMG1BIS)",
      "10000000000T546B": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T546B)",
      "1000000000T546B2": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T546B2)",
      "10000000T548AMG0": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T548AMG0)",
      "10000000T548AMG1": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T548AMG1)",
      "10000000000T548B": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T548B)",
      "10000000000T556A": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T556A)",
      "10000000T556BMG0": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T556BMG0)",
      "10000000T556BMG1": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T556BMG1)",
      "10000000000T556C": "CAPITALE DIFFERITO RIVALUTABILE CON (1-T556C)",
      "10000000000T566A": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-T566A)",
      "10000000T566BMG0": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-T566BMG0)",
      "10000000T566BMG1": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-T566BMG1)",
      "10000000000T566C": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-T566C)",
      "10000000000T635A": "ASSICURAZIONE SU DUE TESTE A P.A. DI UN CAPITALE (1-T635A)",
      "10000000000T635B": "ASSICURAZIONE SU DUE TESTE A P.A. DI UN CAPITALE (1-T635B)",
      "10000000000T635F": "ASSICURAZIONE SU DUE TESTE A P.A. DI UN CAPITALE (1-T635F)",
      "10000000000T635M": "ASSICURAZIONE SU DUE TESTE A P.A. DI UN CAPITALE (1-T635M)",
      "10000000000T814A": "CAPITALIZZAZIONE A PREMIO UNICO (1-T814A)",
      "10000000000T814B": "CAPITALIZZAZIONE A PREMIO UNICO (1-T814B)",
      "10000000000T857A": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857A)",
      "10000000000T857C": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857C)",
      "10000000000T857E": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857E)",
      "10000000000T857F": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857F)",
      "10000000000T857G": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857G)",
      "10000000000T857H": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857H)",
      "10000000000T857I": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857I)",
      "10000000000T857L": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857L)",
      "10000000000T857M": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857M)",
      "10000000000T857N": "CAPITALIZZAZIONE A PREMI RICORRENTI (1-T857N)",
      "10000000000T900A": "RENDITA VITALIZIA DIFFERITA CON ADEGUAMENTO ANNUO (1-T900A)",
      "10000000000T922A": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T922A)",
      "10000000000T922B": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T922B)",
      "10000000000T922C": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T922C)",
      "10000000000T922W": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T922W)",
      "10000000000T929A": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T929A)",
      "10000000000T929B": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T929B)",
      "10000000000T932A": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T932A)",
      "10000000000T932C": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-T932C)",
      "10000000000TA120": "RISULTATO AURORA (1-TA120)",
      "10000000000TA122": "CSLIFEBOND (1-TA122)",
      "10000000000TA123": "DOPPIO SEGNO POSITIVO (ED. 2004/ TASSO FISSO 3.9%) (1-TA123)",
      "100000000TA123GS": "DOPPIO SEGNO POSITIVO (ED. 2004/ TASSO FISSO 3.9%) (1-TA123GS)",
      "10000000000TA125": "RISULTATO AURORA PER REINVESTIMENTO (1-TA125)",
      "10000000000TA128": "PERLA AURORA ED. 2005 (1-TA128)",
      "100000000TA128GS": "PERLA AURORA ED. 2005 (1-TA128GS)",
      "10000000000TA12A": "PERLA AURORA ED.2006 (1-TA12A)",
      "100000000TA12AGS": "PERLA AURORA ED.2006 (1-TA12AGS)",
      "10000000000TA12B": "NEW LIFEBOND (1-TA12B)",
      "10000000000TA12C": "NEW LIFEBOND - CON CEDOLA (1-TA12C)",
      "10000000000TA12D": "PERLA AURORA F6 (1-TA12D)",
      "100000000TA12DGS": "PERLA AURORA F6 (1-TA12DGS)",
      "10000000000TA12E": "PERLA AURORA F6 (1-TA12E)",
      "100000000TA12EGS": "PERLA AURORA F6 (1-TA12EGS)",
      "10000000000TA12F": "PERLA AURORA 2007 - AGENZIE (1-TA12F)",
      "100000000TA12FGS": "PERLA AURORA 2007 - AGENZIE (1-TA12FGS)",
      "10000000000TA12G": "PERLA AURORA 2007 DA REINVESTIM. - AGENZIE (1-TA12G)",
      "100000000TA12GGS": "PERLA AURORA 2007 DA REINVESTIM. - AGENZIE (1-TA12GGS)",
      "10000000000TA12H": "PERLA AURORA 2007 CSI (1-TA12H)",
      "100000000TA12HGS": "PERLA AURORA 2007 CSI (1-TA12HGS)",
      "10000000000TA12L": "PERLA AURORA F6 (1-TA12L)",
      "100000000TA12LGS": "PERLA AURORA F6 (1-TA12LGS)",
      "10000000000TA12N": "PERLA AURORA F2 (1-TA12N)",
      "100000000TA12NGS": "PERLA AURORA F2 (1-TA12NGS)",
      "10000000000TA12O": "PERLA AURORA F2 (1-TA12O)",
      "100000000TA12OGS": "PERLA AURORA F2 (1-TA12OGS)",
      "10000000000TA12P": "PERLAAURORA 4CARATI (1-TA12P)",
      "100000000TA12PGS": "PERLAAURORA 4CARATI (1-TA12PGS)",
      "10000000000TA12Q": "PERLA AURORA EXCLUSIVE (1-TA12Q)",
      "100000000TA12QGS": "PERLA AURORA EXCLUSIVE (1-TA12QGS)",
      "10000000000TA12R": "PERLA AURORA 2008 CSI (1-TA12R)",
      "100000000TA12RGS": "PERLA AURORA 2008 CSI (1-TA12RGS)",
      "10000000000TA12S": "PERLAAURORA BONUS (1-TA12S)",
      "100000000TA12SGS": "PERLAAURORA BONUS (1-TA12SGS)",
      "10000000000TA12T": "PERLAAURORA BONUS (1-TA12T)",
      "100000000TA12TGS": "PERLAAURORA BONUS (1-TA12TGS)",
      "1000000000TA12ZR": "PERLAAURORAMIX REINV II SERIE (1-TA12ZR)",
      "10000000000TA131": "AURORA RISPARMIO COOPERATIVO (1-TA131)",
      "10000000000TA132": "ASSICURAZIONE VI PUR A CAPITALE RIVAL. NO LOAD (1-TA132)",
      "10000000000TA133": "ASSICURAZIONE A VITA INTERA PUR A CAPITALE RIVAL. (1-TA133)",
      "10000000000TA13B": "NEW LIFEBOND (1-TA13B)",
      "10000000000TA13C": "NEW LIFEBOND - CON CEDOLA (1-TA13C)",
      "10000000000TA149": "AURORA VITA INTERA VIP 2 (1-TA149)",
      "10000000000TA150": "VITA INTERA A PREMIO UNICO CON VERS. AGG. (1-TA150)",
      "10000000000TA151": "VITA INTERA A PREMIO UNICO CON VERS. AGG. - TAR. D (1-TA151)",
      "10000000000TA153": "AURORA NEW RISPARMIO COOPERATIVO (1-TA153)",
      "10000000000TA154": "VITA INTERA A PREMIO UNICO CON VERS. AGG. (1-TA154)",
      "10000000000TA155": "VITA INTERA A PREMIO UNICO CON VERS. AGG. PER REI (1-TA155)",
      "10000000000TA158": "VALORE AURORA BONUS (1-TA158)",
      "1000000TA159A160": "ASSICURAZIONE CASO MORTE A VITA INTERA CON RIVALUT (1-TA159A160)",
      "1000000TA161A162": "ASSICURAZIONE CASO MORTE A VITA INTERA CON RIVALUT (1-TA161A162)",
      "10000000000TA163": "ASSICURAZIONE VI PUR A CAPITALE RIVAL. NO LOAD (1-TA163)",
      "10000000000TA164": "ASSICURAZIONE A VITA INTERA PUR A CAPITALE RIVAL. (1-TA164)",
      "10000000000TA166": "ASSICURAZIONE VI PUR A CAPITALE RIVAL. (1-TA166)",
      "10000000000TA170": "PERLAAURORAMIX (1-TA170)",
      "1000000000TA171R": "PERLAAURORAMIX REINV (1-TA171R)",
      "10000000000TA172": "PERLAAURORAMIX II SERIE (1-TA172)",
      "1000000000TA173R": "PERLAAURORAMIX REINV II SERIE (1-TA173R)",
      "10000000000TA175": "CAPITALE BONUS (1-TA175)",
      "10000000000TA210": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE (1-TA210)",
      "10000000000TA211": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE NON FUM. (1-TA211)",
      "10000000000TA212": "TCM A PREMIO ANNUO LIMITATO E K DECRESC. (1-TA212)",
      "10000000000TA213": "TCM A PREMIO ANNUO LIMITATO E K DECRESC. NON FUM. (1-TA213)",
      "10000000000TA216": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE (1-TA216)",
      "10000000000TA218": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE FUMATORI (1-TA218)",
      "10000000000TA21A": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE NON FUM. (1-TA21A)",
      "10000000000TA220": "TCM A PREMIO UNICO E K COSTANTE (1-TA220)",
      "10000000000TA222": "TCM A PREMIO UNICO E K DECRESCENTE (1-TA222)",
      "10000000000TA224": "TEMPORANEA CASO MORTE DEL DEBITO RESIDO (1-TA224)",
      "10000000000TA225": "TCM A PREMIO UNICO E K COSTANTE (1-TA225)",
      "10000000000TA227": "TCM A PREMIO UNICO E K DECRESCENTE (1-TA227)",
      "10000000000TA233": "TCM A PREMIO ANNUO LIMITATO E CAPITALE DECRESCENTE (1-TA233)",
      "10000000000TA234": "TCM A PREMIO UNICO E CAPITALE DECRESCENTE (1-TA234)",
      "10000000000TA250": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE (1-TA250)",
      "10000000000TA252": "TCM A PREMIO ANNUO COSTANTE E K COSTANTE (1-TA252)",
      "10000000000TA254": "TCM A PREMIO ANNUO LIMITATO E K DECRESCENTE (1-TA254)",
      "10000000000TA256": "TCM A PREMIO ANNUO LIMITATO E K DECRESCENTE (1-TA256)",
      "10000000000TA258": "TCM A PREMIO UNICO E K COSTANTE - CAP<400 (1-TA258)",
      "10000000000TA262": "TCM A PREMIO UNICO E K DECRESC. - CAP<400 (1-TA262)",
      "10000000000TA310": "MISTA A P.A. COSTANTE CON TERMINAL BONUS (1-TA310)",
      "10000000000TA311": "MISTA A P.A. RIVALUTABILE CON TERMINAL BONUS (1-TA311)",
      "10000000000TA321": "MISTA A P.A. RIVALUTABILE CON TERMINAL BONUS (1-TA321)",
      "10000000000TA510": "CAPITALE DIFFERITO CON CONTROASS. A P.A.COSTANTE (1-TA510)",
      "10000000000TA511": "CAPITALE DIFFERITO CON CONTROASS. A P.A.RIVALUTAB. (1-TA511)",
      "10000000000TA521": "CAPITALE DIFFERITO CON CONTROASS. A P.A.RIVALUTAB. (1-TA521)",
      "10000000000TA530": "CAPITALE DIFFERITO PUR CON COEFF.GARANTITI (1-TA530)",
      "10000000000TA540": "CAPITALE DIFFERITO CON CONTROASS. A P.A.COSTANTE (1-TA540)",
      "10000000000TA610": "FUTURO SERENO (1-TA610)",
      "10000000000TA721": "RENDITA IMMEDIATA REVERSIBILE A PREMIO UNICO (1-TA721)",
      "10000000000TA724": "RENDITA IMMEDIATA REVERSIBILE A PREMIO UNICO (1-TA724)",
      "10000000000TA725": "RENDITA IMMEDIATA A PREMIO UNICO (1-TA725)",
      "10000000000TB101": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-TB101)",
      "10000000000TB102": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-TB102)",
      "10000000000TB103": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-TB103)",
      "10000000000TB109": "NO LIMITS - ASS. IN CASO DI MORTE A VITA INTERA A (1-TB109)",
      "10000000000TB114": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A (1-TB114)",
      "10000000000TB313": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-TB313)",
      "10000000000TB314": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-TB314)",
      "10000000000TB315": "MISTA RIVALUTABILE A PREMIO UNICO (1-TB315)",
      "10000000000TB404": "ASSICURAZIONE DI PREVIDENZA SCOLASTICA A PREMIO (1-TB404)",
      "10000000000TB501": "CAPITALE DIFFERITO RIVALUTABILE CON (1-TB501)",
      "10000000000TB502": "CAPITALE DIFFERITO RIVALUTABILE CON (1-TB502)",
      "10000000000TB503": "CAPITALE DIFFERITO RIVALUTABILE CON (1-TB503)",
      "10000000TB504MG2": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-TB504MG2)",
      "10000000TB504MG3": "CAPITALE DIFFERITO RIVALUTABILE A PREMIO ANNUO (1-TB504MG3)",
      "10000000000TB904": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TB904)",
      "10000000000TB905": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TB905)",
      "10000000000TB906": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TB906)",
      "1000TIADIF391BG0": "VITA INTERA DIFFERIMENTI (1-TIADIF391BG0)",
      "1000TIADIF391BG1": "VITA INTERA DIFFERIMENTI (1-TIADIF391BG1)",
      "1000TIADIF392BG0": "VITA INTERA DIFFERIMENTI (1-TIADIF392BG0)",
      "1000TIADIF392BG1": "VITA INTERA DIFFERIMENTI (1-TIADIF392BG1)",
      "1000TIADIF546AG0": "VITA INTERA DIFFERIMENTI (1-TIADIF546AG0)",
      "1000TIADIF546AG1": "VITA INTERA DIFFERIMENTI (1-TIADIF546AG1)",
      "1000TIADIF548AG0": "VITA INTERA DIFFERIMENTI (1-TIADIF548AG0)",
      "1000TIADIF548AG1": "VITA INTERA DIFFERIMENTI (1-TIADIF548AG1)",
      "1000TIADIF556BG0": "VITA INTERA DIFFERIMENTI (1-TIADIF556BG0)",
      "1000TIADIF556BG1": "VITA INTERA DIFFERIMENTI (1-TIADIF556BG1)",
      "1000TIADIF566BG1": "VITA INTERA DIFFERIMENTI (1-TIADIF566BG1)",
      "1000TIADIFB504G3": "VITA INTERA DIFFERIMENTI (1-TIADIFB504G3)",
      "10000TIADIFF0314": "VITA INTERA DIFFERIMENTI (1-TIADIFF0314)",
      "10000TIADIFF0323": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF0323)",
      "10000TIADIFF0324": "VITA INTERA DIFFERIMENTI (1-TIADIFF0324)",
      "10000TIADIFF0404": "VITA INTERA DIFFERIMENTI (1-TIADIFF0404)",
      "10000TIADIFF0414": "VITA INTERA DIFFERIMENTI (1-TIADIFF0414)",
      "10000TIADIFF0424": "VITA INTERA DIFFERIMENTI (1-TIADIFF0424)",
      "10000TIADIFF0511": "VITA INTERA A T.T. 0% (DIFFERIMENTO) (1-TIADIFF0511)",
      "10000TIADIFF0521": "VITA INTERA A T.T. 0% (DIFFERIMENTO) (1-TIADIFF0521)",
      "10000TIADIFF0902": "VITA INTERA DIFFERIMENTI (1-TIADIFF0902)",
      "10000TIADIFF0924": "VITA INTERA DIFFERIMENTI (1-TIADIFF0924)",
      "10000TIADIFF311A": "VITA INTERA DIFFERIMENTI (1-TIADIFF311A)",
      "10000TIADIFF311B": "VITA INTERA DIFFERIMENTI (1-TIADIFF311B)",
      "10000TIADIFF318B": "VITA INTERA A TT 0% (DIFFERIMENTO) (1-TIADIFF318B)",
      "100000TIADIFF372": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF372)",
      "10000TIADIFF391A": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF391A)",
      "10000TIADIFF391C": "VITA INTERA A TT 0% (DIFFERIMENTO) - MINGAR=0 (1-TIADIFF391C)",
      "10000TIADIFF392A": "VITA INTERA DIFFERIMENTI (1-TIADIFF392A)",
      "10000TIADIFF392C": "VITA INTERA A TT 0% (DIFFERIMENTO) - MINGAR=0 (1-TIADIFF392C)",
      "10000TIADIFF399A": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF399A)",
      "10000TIADIFF430A": "VITA INTERA A TT 1%, ALIQRET=85% (DIFFERIMENTO) (1-TIADIFF430A)",
      "10000TIADIFF548B": "VITA INTERA A TT 2.5% (DIFFERIMENTO) (1-TIADIFF548B)",
      "10000TIADIFF556A": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF556A)",
      "10000TIADIFF556C": "VITA INTERA A TT 2.5% (DIFFERIMENTO) - MINGAR=0 (1-TIADIFF556C)",
      "10000TIADIFF566A": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFF566A)",
      "10000TIADIFF635A": "VITA INTERA DIFFERIMENTI (1-TIADIFF635A)",
      "10000TIADIFF635M": "VITA INTERA A TT 2.5% (DIFFERIMENTO) (1-TIADIFF635M)",
      "10000TIADIFF814A": "VITA INTERA DIFFERIMENTI (1-TIADIFF814A)",
      "10000TIADIFF814B": "VITA INTERA DIFFERIMENTI (1-TIADIFF814B)",
      "10000TIADIFF857A": "VITA INTERA DIFFERIMENTI (1-TIADIFF857A)",
      "10000TIADIFF857C": "VITA INTERA DIFFERIMENTI (1-TIADIFF857C)",
      "10000TIADIFF857E": "VITA INTERA DIFFERIMENTI (1-TIADIFF857E)",
      "10000TIADIFF857G": "VITA INTERA DIFFERIMENTI (1-TIADIFF857G)",
      "10000TIADIFF857I": "VITA INTERA DIFFERIMENTI (1-TIADIFF857I)",
      "10000TIADIFF857M": "VITA INTERA DIFFERIMENTI (1-TIADIFF857M)",
      "10000TIADIFF922A": "VITA INTERA DIFFERIMENTI (1-TIADIFF922A)",
      "10000TIADIFF929A": "VITA INTERA DIFFERIMENTI (1-TIADIFF929A)",
      "10000TIADIFFB313": "VITA INTERA DIFFERIMENTI (1-TIADIFFB313)",
      "10000TIADIFFB314": "VITA INTERA A TT 4% (DIFFERIMENTO) (1-TIADIFFB314)",
      "10000TIADIFFB404": "VITA INTERA A TT 4% (DIFFERIMENTO) (1-TIADIFFB404)",
      "10000TIADIFFB501": "VITA INTERA A TT 4% (DIFFERIMENTO) (1-TIADIFFB501)",
      "10000TIADIFFB503": "VITA INTERA A TT 4% (DIFFERIMENTO) (1-TIADIFFB503)",
      "10000TIADIFFB904": "VITA INTERA DIFFERIMENTI (1-TIADIFFB904)",
      "10000TIADIFFB906": "VITA INTERA A TT 4% (DIFFERIMENTO) (1-TIADIFFB906)",
      "10000TIADIFFM501": "VITA INTERA DIFFERIMENTI (1-TIADIFFM501)",
      "10000TIADIFFM905": "VITA INTERA DIFFERIMENTI (1-TIADIFFM905)",
      "10000TIADIFFM906": "VITA INTERA DIFFERIMENTI (1-TIADIFFM906)",
      "10000TIADIFFM916": "VITA INTERA A TT 3% (DIFFERIMENTO) (1-TIADIFFM916)",
      "10000TIADIFFM926": "VITA INTERA A TT 2% (DIFFERIMENTO) (1-TIADIFFM926)",
      "10000TIADIFFR72C": "VITA INTERA DIFFERIMENTI (1-TIADIFFR72C)",
      "10000TIADIFFR82B": "VITA INTERA DIFFERIMENTI (1-TIADIFFR82B)",
      "10000TIADIFFR91A": "VITA INTERA DIFFERIMENTI (1-TIADIFFR91A)",
      "10000TIADIFFR91B": "VITA INTERA DIFFERIMENTI (1-TIADIFFR91B)",
      "10000TIADIFFR92A": "VITA INTERA DIFFERIMENTI (1-TIADIFFR92A)",
      "10000TIADIFFR92B": "VITA INTERA DIFFERIMENTI (1-TIADIFFR92B)",
      "10000TIADIFFT291": "VITA INTERA DIFFERIMENTI (1-TIADIFFT291)",
      "10000TIADIFFW220": "VITA INTERA DIFFERIMENTI (1-TIADIFFW220)",
      "10000TIADIFFW221": "VITA INTERA DIFFERIMENTI (1-TIADIFFW221)",
      "10000TIADIFFW223": "VITA INTERA DIFFERIMENTI (1-TIADIFFW223)",
      "10000TIADIFFW224": "VITA INTERA DIFFERIMENTI (1-TIADIFFW224)",
      "10000TIADIFFW225": "VITA INTERA DIFFERIMENTI (1-TIADIFFW225)",
      "10000TIADIFFW261": "VITA INTERA DIFFERIMENTI (1-TIADIFFW261)",
      "10000TIADIFFW262": "VITA INTERA DIFFERIMENTI (1-TIADIFFW262)",
      "10000TIADIFFW291": "VITA INTERA DIFFERIMENTI (1-TIADIFFW291)",
      "10000TIADIFFW292": "RENDITA DIFFERIMENTI (1-TIADIFFW292)",
      "10000TIADIFFW293": "VITA INTERA DIFFERIMENTI (1-TIADIFFW293)",
      "10000TIADIFFW294": "VITA INTERA DIFFERIMENTI (1-TIADIFFW294)",
      "10000TIADIFFW296": "VITA INTERA DIFFERIMENTI (1-TIADIFFW296)",
      "10000TIADIFFW603": "CAPITALIZZAZIONE A PREMIO UNICO TT 4% (DIFFERIMENTO) (1-TIADIFFW603)",
      "10000TIADIFFW643": "VITA INTERA DIFFERIMENTI (1-TIADIFFW643)",
      "10000TIADIFFW663": "VITA INTERA DIFFERIMENTI (1-TIADIFFW663)",
      "10000TIADIFFW694": "VITA INTERA DIFFERIMENTI (1-TIADIFFW694)",
      "10000TIADIFFW695": "VITA INTERA DIFFERIMENTI (1-TIADIFFW695)",
      "10000TIADIFFW808": "RENDITA DIFFERIMENTI (1-TIADIFFW808)",
      "10000TIADIFFW824": "RENDITA DIFFERIMENTI (1-TIADIFFW824)",
      "10000TIADIFFW856": "RENDITA DIFFERIMENTI (1-TIADIFFW856)",
      "10000TIADIFFW897": "RENDITA A TT 2.5% (DIFFERIMENTO) (1-TIADIFFW897)",
      "10000TIADIFFW966": "VITA INTERA DIFFERIMENTI (1-TIADIFFW966)",
      "10000TIADIFFW998": "RENDITA DIFFERIMENTI (1-TIADIFFW998)",
      "10000TIATT0MG0DF": "VITA INTERA DIFFERIMENTI (1-TIATT0MG0DF)",
      "10000TIATT0MG4DF": "VITA INTERA DIFFERIMENTI (1-TIATT0MG4DF)",
      "10000TIU4C3FPREV": "VITA INTERA DIFFERIMENTI (1-TIU4C3FPREV)",
      "10000000000TM102": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA (1-TM102)",
      "10000000000TM211": "TEMPORANEA CASO MORTE A CAPITALE (1-TM211)",
      "10000000000TM213": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TM213)",
      "10000000000TM315": "MISTA RIVALUTABILE A PREMIO UNICO (1-TM315)",
      "10000000000TM501": "CAPITALE DIFFERITO RIVALUTABILE CON (1-TM501)",
      "10000000000TM503": "CAPITALE DIFFERITO RIVALUTABILE CON (1-TM503)",
      "10000000000TM905": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TM905)",
      "10000000000TM906": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TM906)",
      "10000000000TM915": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TM915)",
      "10000000000TM916": "RENDITA VITALIZIA DIFFERITA RIVALUATBILE CON (1-TM916)",
      "10000000000TM925": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TM925)",
      "10000000000TM926": "RENDITA VITALIZIA DIFFERITA RIVALUTABILE CON (1-TM926)",
      "10000000000TM990": "RENDITA DIFFERITA A PREMIO UNICO CON (1-TM990)",
      "10000000000TR72C": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-TR72C)",
      "10000000000TR82B": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE (1-TR82B)",
      "10000000000TR91A": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE CON (1-TR91A)",
      "10000000000TR91B": "MISTA RIVALUTABILE A PREMIO ANNUO CON TERMINAL (1-TR91B)",
      "10000000000TR92A": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-TR92A)",
      "10000000000TR92B": "MISTA RIVALUTABILE A PREMIO ANNUO COSTANTE CON (1-TR92B)",
      "10000000000TT022": "MISTE VECCHI BG GARANZIA TR (20,22,24) (1-TT022)",
      "10000000000TT035": "RENDITE VECCHI BG GARANZIA TR (50,54) (1-TT035)",
      "10000000000TT044": "MISTE BG GARANZIA TR (1-TT044)",
      "10000000000TT050": "MISTA A BENEFICI GARANTITI A PREMIO UNICO (1-TT050)",
      "10000000000TT058": "RENDITA DIFF.PU CONTR.GAR TR (150-151BG-550) (1-TT058)",
      "10000000000TT073": "CAPITALE DIFF.PU CONTR.GAR TR (190-191-590) (1-TT073)",
      "10000000000TT221": "MISTA PREMIO UNICO ACCRES. AUT. ANNUALE PREST. (1-TT221)",
      "10000000000TT225": "MISTA RIVALUTABILE VEVIAS A PREMIO UNICO (1-TT225)",
      "10000000000TT231": "VITA INTERA RIVALUTABILE A PREMIO UNICO (1-TT231)",
      "10000000000TT233": "VITA INTERA RIVALUTABILE A PREMIO UNICO (1-TT233)",
      "10000000000TT237": "VITA INTERA A PREMI UNICO CAPITALE RIVALUT.ANNUO (1-TT237)",
      "10000000000TT291": "RENDITA RIVALUTABILE A PREMIO UNICO (1-TT291)",
      "10000000000TT296": "CAPITALE RIVALUTABILE A PREMIO UNICO (1-TT296)",
      "10000000000TT379": "ASSICURAZIONE MISTA TRASFOR.- CAP.RIV PU (1-TT379)",
      "10000000000TT382": "ASS. MISTA TRASFORMAZIONI - CAP.RIV PU (1-TT382)",
      "10000000000TT391": "ASS.CAPITALE DIFF.CONTROASS. TRASF. CAP.RIV PU (1-TT391)",
      "10000000000TT395": "ASS.CAPITALE DIFF.CONTROASS. TRASF. CAP.RIV PU (1-TT395)",
      "10000000000TT420": "ASS.RENDITA VIT.DIFF.CONTROASS TRASFREND.RIV PU (1-TT420)",
      "10000000000TT655": "GARANZIA TR RENDITA VIT. DIFF. RIVALUT. A PU (1-TT655)",
      "10000000000TT664": "GARANZIA TR MISTA A BENEFICI GARANTITI A PREMIO UN (1-TT664)",
      "10000000000TT679": "GAR TR REND.VIT.DIFF.A BENGAR PU CONTROASS. (1-TT679)",
      "10000000000TT695": "GAR.TR CAP.DIFF. A BENGAR PU CONTROASS. (1-TT695)",
      "10000000000TT715": "GARANZIA TR MISTA RIV. A PU (1-TT715)",
      "100000000TUT01GS": "INVESTISMART (1-TUT01GS)",
      "100000000TUT01SP": "INVESTISMART (1-TUT01SP)",
      "100000000TUT02GS": "INVESTISMART (1-TUT02GS)",
      "100000000TUT02SP": "INVESTISMART (1-TUT02SP)",
      "10000000000TW015": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW015)",
      "10000000000TW017": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW017)",
      "10000000000TW018": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW018)",
      "10000000000TW019": "ACCANTONAMENTO PER INVESTIMENTO (1-TW019)",
      "10000000000TW020": "MISTA IMMEDIATA A BENEFICI GARANTITI (1-TW020)",
      "100000000TW020BG": "MISTA IMMEDIATA A BENEFICI GARANTITI (1-TW020BG)",
      "10000000000TW022": "MISTA IMMEDIATA A PREMIO UNICO A BENEFICI GARANTIT (1-TW022)",
      "100000000TW022BG": "MISTA IMMEDIATA A PREMIO UNICO A BENEFICI GARANTIT (1-TW022BG)",
      "10000000000TW024": "MISTA IMMEDIATA CON INDICIZZAZIONE DEL CAPITALE E (1-TW024)",
      "10000000000TW025": "MISTA IMMEDIATA CON INDICIZZAZIONE DEL CAPITALE E (1-TW025)",
      "10000000000TW028": "MISTA IMMEDIATA CON TRIPLICAZIONE DEL CAPITALE CAS (1-TW028)",
      "100000000TW028BG": "MISTA IMMEDIATA CON TRIPLICAZIONE DEL CAPITALE CAS (1-TW028BG)",
      "10000000000TW031": "RENDITA VITALIZIA DIFFERITA ADEGUABILE CON (1-TW031)",
      "100000000TW031BG": "RENDITA VITALIZIA DIFFERITA ADEGUABILE CON (1-TW031BG)",
      "10000000000TW033": "RENDITA VITALIZIA DIFFERITA AD ALTA INDICIZZAZIONE (1-TW033)",
      "10000000000TW035": "RENDITA VITALIZIA DIFFERITA A PREMIO UNICO CON (1-TW035)",
      "100000000TW035BG": "RENDITA VITALIZIA DIFFERITA A PREMIO UNICO CON (1-TW035BG)",
      "10000000000TW043": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW043)",
      "10000000000TW044": "MISTA A BENEFICI GARANTITI A PREMIO UNICO (1-TW044)",
      "10000000000TW045": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW045)",
      "10000000000TW046": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW046)",
      "10000000000TW048": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW048)",
      "10000000000TW049": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW049)",
      "10000000000TW050": "MISTA A BENEFICI GARANTITI A PREMIO UNICO (1-TW050)",
      "10000000000TW052": "VITA INTERA A BENEFICI GARANTITI A PREMI ANNUI (1-TW052)",
      "10000000000TW057": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW057)",
      "10000000000TW058": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW058)",
      "10000000000TW072": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW072)",
      "10000000000TW073": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW073)",
      "10000000000TW076": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW076)",
      "10000000000TW077": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A (1-TW077)",
      "10000000000TW078": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW078)",
      "10000000000TW079": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW079)",
      "10000000000TW080": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW080)",
      "10000000000TW081": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW081)",
      "10000000000TW082": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW082)",
      "10000000000TW084": "ACCANTONAMENTO PER INVESTIMENTO (1-TW084)",
      "10000000000TW085": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW085)",
      "10000000000TW107": "WINSTEPS (1-TW107)",
      "10000000000TW109": "TEMPORANEA CASO MORTE A CAPITALE E PREMIO COSTANTI (1-TW109)",
      "10000000000TW110": "TEMPORANEA CASO MORTE A CAPITALE E PREMI CRESCENTI (1-TW110)",
      "10000000000TW111": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW111)",
      "10000000000TW113": "WINBOND 2003 (1-TW113)",
      "10000000000TW115": "CSLIFEBOND 2003 (1-TW115)",
      "10000000000TW117": "WINBOND 2003 (1-TW117)",
      "10000000000TW118": "CSLIFEBOND 2003 (1-TW118)",
      "10000000000TW119": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW119)",
      "10000000000TW220": "MISTA PREMIO ANNUO COST. ACCRES. AUT. ANNUALE PRES (1-TW220)",
      "10000000000TW221": "MISTA PREMIO UNICO ACCRES. AUT. ANNUALE PREST. (1-TW221)",
      "10000000000TW223": "MISTA RIVALUTABILE A PREMIO ANNUO (1-TW223)",
      "10000000000TW224": "MISTA RIVALUTABILE A PREMIO ANNUO RIVALUTABILE (1-TW224)",
      "10000000000TW225": "MISTA RIVALUTABILE VEVIAS A PREMIO UNICO (1-TW225)",
      "10000000000TW230": "VITA INTERA RIVALUTABILE A PREMIO ANNUO TEMPORANEA (1-TW230)",
      "10000000000TW233": "VITA INTERA RIVALUTABILE A PREMIO UNICO (1-TW233)",
      "10000000000TW234": "VITA INTERA RIVALUTABILE A PREMIO ANNUO (1-TW234)",
      "10000000000TW235": "VITA INTERA RIVALUTABILE A PREMIO ANNUO RIVALUT. (1-TW235)",
      "10000000000TW236": "VITA INTERA A PREMI RICORRENTI CAPITALE RIVALUT. (1-TW236)",
      "1000000000TW236B": "VITA INTERA A PREMI RICORRENTI CAPITALE RIVALUT. (1-TW236B)",
      "10000000000TW237": "VITA INTERA A PREMI UNICO CAPITALE RIVALUT.ANNUO (1-TW237)",
      "10000000000TW261": "RENDITA RIVALUTABILE A PREMIO UNICO (1-TW261)",
      "10000000000TW262": "RENDITA RIVALUTABILE A PREMIO ANNUO RATE SEMETRALI (1-TW262)",
      "10000000000TW264": "TERMINE FISSO CON RIVAL.DEL CAPITALE E PR.COSTANTE (1-TW264)",
      "10000000000TW266": "ASS.NE PREV.SCOLASTICA A PRESTAZ.E PR.ANNUO RIVAL. (1-TW266)",
      "10000000000TW268": "RENDITA A MEDIA INDICIZZAZIONE A PR.ANNUO INDIC. (1-TW268)",
      "10000000000TW270": "TEMPORANEA CASO MORTE A PREMIO ANNUO (1-TW270)",
      "10000000000TW276": "CASO MORTE E RENDITA TEMP.CERTA A PR.ANNUO (1-TW276)",
      "10000000000TW283": "T.CASO MORTE A CAP.CRESC.5% E PREMIO ANNUO COST. (1-TW283)",
      "10000000000TW284": "T.CASO MORTE A CAP.CRESC.10% E PREMIO ANNUO COST. (1-TW284)",
      "10000000000TW291": "RENDITA RIVALUTABILE A PREMIO UNICO (1-TW291)",
      "10000000000TW292": "RENDITA RIVALUTABILE A PREMIO ANNUO (1-TW292)",
      "10000000000TW293": "CAPITALE RIVALUTABILE A PREMIO ANNUO (1-TW293)",
      "10000000000TW294": "CAPITALE RIVALUTABILE A PREMIO ANNUO RIVALUTATO (1-TW294)",
      "10000000000TW295": "RENDITA RIVALUTABILE A PREMIO ANNUO RIVALUTATO (1-TW295)",
      "10000000000TW296": "CAPITALE RIVALUTABILE A PREMIO UNICO (1-TW296)",
      "10000000000TW301": "ASSICURAZIONE DI CAPITALE CASO MORTE VITA INTERA A (1-TW301)",
      "10000000000TW309": "MISTA A CAPITALE E PREMI ANNUALMENTE RIVALUTABILI (1-TW309)",
      "10000000000TW311": "ASS.MISTA CON CAPITALE E PREMI CRESCENTI IN MISURA (1-TW311)",
      "10000000000TW312": "MISTA A CAPITALE ANNUALMENTE RIVALUTABILE E PREMI (1-TW312)",
      "10000000000TW320": "ASS.DI RENDITA VITALIZIA DIFFERITA A PREMI ANNUI C (1-TW320)",
      "10000000000TW321": "ASS.DI RENDITA VITALIZIA DIFFERITA A PREMI ANNUI C (1-TW321)",
      "10000000000TW322": "ASS.RENDITA VITALIZIA DIFFERITA CON RIVALUTAZIONE (1-TW322)",
      "10000000000TW323": "ASS.DI RENDITA VITALIZIA DIFFERITA ANNUALMENTE RIV (1-TW323)",
      "10000000000TW324": "ASS.RENDITA VIT.DIFF.CON PRESTAZIONI E PREMI ANNUI (1-TW324)",
      "10000000000TW325": "ASS. RENDITA TEMPORANEA CON PRESTAZIONI E PREMI IN (1-TW325)",
      "10000000000TW326": "ASS. RENDITA TEMPORANEA CON PRESTAZIONI E PREMI IN (1-TW326)",
      "10000000000TW335": "ASSICURAZIONE MISTA CON CAPITALE ADEGUABILE E PREM (1-TW335)",
      "10000000000TW340": "ASS.RENDITA VITALIZIA DIFF. CON PRESTAZIONI E PREM (1-TW340)",
      "10000000000TW341": "RENDITA VIT. DIFF.ADEGUABILE A PREMIO ANNUO COSTAN (1-TW341)",
      "10000000000TW347": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A CAP (1-TW347)",
      "10000000000TW348": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A CAP (1-TW348)",
      "10000000000TW350": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A CAP (1-TW350)",
      "10000000000TW354": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A CAP (1-TW354)",
      "10000000000TW356": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A DOP (1-TW356)",
      "10000000000TW357": "ASSICURAZIONE IN CASO DI MORTE A VITA INTERA A DOP (1-TW357)",
      "10000000000TW359": "TEMP. PER IL CASO MORTE A CAPITALE E P.A. COSTANTI (1-TW359)",
      "10000000000TW368": "280A-TEMPORANEA CASO MORTE A CAPITALE CRESCENTE AN (1-TW368)",
      "10000000000TW371": "ASSICURAZIONE MISTA RIVALUTABILE A PREMIO ANNUO RI (1-TW371)",
      "10000000000TW373": "MISTA A CAPITALE E PREMI ANNUALMENTE RIVALUTABILI (1-TW373)",
      "10000000000TW375": "MISTA A CAPITALE ANNUALMENTE RIVALUTABILE E PREMI (1-TW375)",
      "10000000000TW376": "MISTA A CAPITALE E PREMIO ANNUO RIVALUTABILI (SIM8 (1-TW376)",
      "10000000000TW377": "MISTA A CAPITALE RIVALUTABILE E A PREMIO UNICO (SI (1-TW377)",
      "10000000000TW378": "MISTA A CAPITALE RIVALUTABILE E A PREMIO ANNUO COS (1-TW378)",
      "10000000000TW380": "ASSICURAZIONE MISTA RIVALUTABILE A PREMIO ANNUO CO (1-TW380)",
      "10000000000TW381": "ASSICURAZIONE MISTA RIVALUTABILE A PREMIO ANNUO RI (1-TW381)",
      "10000000000TW384": "MISTA A CAPITALE RIVALUTABILE E PREMIO ANNUO COSTA (1-TW384)",
      "10000000000TW386": "ASSICURAZIONE DI TERMINE FISSO RIVALUTABILE PREMIO (1-TW386)",
      "10000000000TW387": "ASSICURAZIONE A TERMINE FISSO CON RIVALUTAZIONE AN (1-TW387)",
      "10000000000TW390": "ASS. DI CAP. DIFFERITO A PREMIO ANNUO COSTANTE CON (1-TW390)",
      "10000000000TW393": "ASS.DI CAP.DIFFERITO A PREMIO ANNUO RIVALUTABILE C (1-TW393)",
      "10000000000TW394": "ASS.DI CAP.DIFFERITO A PREMIO ANNUO RIVALUTABILE C (1-TW394)",
      "10000000000TW398": "ASSICURAZIONE DI CAP.DIFFERITO A PREMIO UNICO CON (1-TW398)",
      "10000000000TW399": "TEMP. PER IL CASO MORTE A CAPITALE E P.A. COSTANTI (1-TW399)",
      "10000000000TW413": "REND.VITALIZIA DIFF.A PREMIO ANNUO COSTANTE CON CO (1-TW413)",
      "10000000000TW415": "REND.VITALIZIA DIFF.A PREMIO ANNUO COSTANTE CON CO (1-TW415)",
      "10000000000TW418": "REND.VITALIZIA DIFF.A.PREMIO ANNUO CON CONTROASS. (1-TW418)",
      "10000000000TW419": "REND.VITALIZIA DIFF.A PREMIO ANNUO CON CONTROASS.E (1-TW419)",
      "10000000000TW423": "RENDITA VITALIZIA DIFFERITA A PREMIO UNICO (TASSO (1-TW423)",
      "10000000000TW424": "CAPITALE DIFFERITO A PREMIO ANNUO CON CONTROAS. E (1-TW424)",
      "10000000000TW425": "CAPITALE DIFFERITO A PREMIO UNICO CON CONTROAS. E (1-TW425)",
      "10000000000TW426": "ASS. DI CAPITALE DIFFERITO A PREMIO ANNUO COSTANTE (1-TW426)",
      "10000000000TW430": "REND.VITALIZIA DIFF.A PREMIO ANNUO COSTANTE CON CO (1-TW430)",
      "10000000000TW431": "REND.VITALIZIA DIFF.A PREMIO ANNUO CON CONTROASS.E (1-TW431)",
      "10000000000TW432": "RENDITA VITALIZIA DIFFERITA A PREMIO UNICO CON CON (1-TW432)",
      "10000000000TW433": "REND.VITALIZIA DIFF.A PREMIO ANNUO COSTANTE CON CO (1-TW433)",
      "10000000000TW650": "VITA INTERA ADEGUABILE A PREMI ANNUI TEMPORANEI (1-TW650)",
      "10000000000TW651": "RENDITA VITALIZIA DIFFERITA ADEGUABILE CON CONTROA (1-TW651)",
      "10000000000TW653": "RENDITA VITALIZIA DIFFERITA AD ALTA INDICIZZAZIONE (1-TW653)",
      "10000000000TW654": "RENDITA VITALIZIA DIFFERITA A MEDIA INDICIZZAZIONE (1-TW654)",
      "10000000000TW662": "CAPITALE DIFFERITO SPECIALE A PREMIO ANNUO (1-TW662)",
      "10000000000TW663": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW663)",
      "10000000000TW666": "MISTA COST. A PA COST. (C2) (1-TW666)",
      "10000000000TW667": "VITA INTERA A BENEFICI GARANTITI A PREMIO ANNUO TE (1-TW667)",
      "10000000000TW669": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO ANNUO (1-TW669)",
      "10000000000TW674": "RENDITA VITALIZIA IMMEDIATA RIVALUTABILE (1-TW674)",
      "10000000000TW678": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW678)",
      "10000000000TW679": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW679)",
      "10000000000TW682": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE A PREMIO (1-TW682)",
      "10000000000TW685": "TEMPORANEA CASO MORTE A CAPIT. CRESCENTE ANNUAL. D (1-TW685)",
      "10000000000TW694": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO A (1-TW694)",
      "10000000000TW695": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO U (1-TW695)",
      "10000000000TW698": "REND. VIT. IMMEDIATA A PREMIO UNICO (R1) (1-TW698)",
      "10000000000TW699": "RENDITA VIT. DIFF. A PA (R7) (1-TW699)",
      "10000000000TW701": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO RIVALUTA (1-TW701)",
      "10000000000TW702": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A BENEFICI (1-TW702)",
      "10000000000TW703": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO ANNUO (1-TW703)",
      "10000000000TW704": "MISTA CON RIVALUTAZIONE ANNUALE DEL CAPITALE E DEL (1-TW704)",
      "10000000000TW705": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW705)",
      "10000000000TW707": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO A (1-TW707)",
      "10000000000TW712": "MISTA RIV. A PA COST. (C2RC) (1-TW712)",
      "10000000000TW713": "REND. VIT. DIFF. A PA RIV. (R7RV) (1-TW713)",
      "10000000000TW714": "REND. VIT. DIFF. A PA COST.(R7RC) (1-TW714)",
      "10000000000TW802": "TEMPORANEA CASO MORTE A CAPITALE E PREMIO COSTANTI (1-TW802)",
      "10000000000TW803": "TEMPORANEA CASO MORTE A CAPITALE E PREMI CRESCENTI (1-TW803)",
      "10000000000TW804": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW804)",
      "10000000000TW805": "TEMPORANEA CASO MORTE A CAPITALE E PREMI COSTANTI (1-TW805)",
      "10000000000TW808": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW808)",
      "10000000000TW809": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW809)",
      "10000000000TW810": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW810)",
      "10000000000TW812": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW812)",
      "10000000000TW813": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW813)",
      "10000000000TW814": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW814)",
      "10000000000TW815": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW815)",
      "10000000000TW816": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW816)",
      "10000000000TW817": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW817)",
      "10000000000TW818": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW818)",
      "10000000000TW819": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW819)",
      "10000000000TW820": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A (1-TW820)",
      "10000000000TW821": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW821)",
      "10000000000TW824": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW824)",
      "10000000000TW825": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW825)",
      "10000000000TW826": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW826)",
      "10000000000TW827": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW827)",
      "10000000000TW828": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW828)",
      "10000000000TW829": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW829)",
      "10000000000TW830": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW830)",
      "10000000000TW831": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW831)",
      "10000000000TW832": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW832)",
      "10000000000TW833": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW833)",
      "10000000000TW834": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW834)",
      "10000000000TW835": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A (1-TW835)",
      "10000000000TW836": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW836)",
      "10000000000TW839": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW839)",
      "10000000000TW840": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW840)",
      "10000000000TW841": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW841)",
      "10000000000TW842": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW842)",
      "10000000000TW843": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW843)",
      "10000000000TW844": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW844)",
      "10000000000TW845": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW845)",
      "10000000000TW846": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW846)",
      "10000000000TW847": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A (1-TW847)",
      "10000000000TW848": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW848)",
      "10000000000TW849": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW849)",
      "10000000000TW850": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW850)",
      "10000000000TW851": "TEMPORANEA CASO MORTE A CAPITALE E PREMIO COSTANTI (1-TW851)",
      "10000000000TW852": "TEMPORANEA CASO MORTE A CAPITALE E PREMI CRESCENTI (1-TW852)",
      "10000000000TW853": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW853)",
      "10000000000TW854": "TEMPORANEA CASO MORTE A CAPITALE E PREMI COSTANTI (1-TW854)",
      "10000000000TW856": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW856)",
      "10000000000TW872": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW872)",
      "10000000000TW874": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW874)",
      "10000000000TW875": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW875)",
      "10000000000TW876": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW876)",
      "10000000000TW877": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW877)",
      "10000000000TW878": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW878)",
      "10000000000TW879": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW879)",
      "10000000000TW881": "TEMPORANEA CASO MORTE A CAPITALE E PREMIO COSTANTI (1-TW881)",
      "10000000000TW883": "TEMPORANEA CASO MORTE A CAPITALE E PREMI CRESCENTI (1-TW883)",
      "10000000000TW884": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW884)",
      "10000000000TW885": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW885)",
      "10000000000TW892": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW892)",
      "10000000000TW894": "TEMPORANEA CASO MORTE A CAPITALE E PREMIO COSTANTI (1-TW894)",
      "10000000000TW895": "TEMPORANEA CASO MORTE A CAPITALE E PREMI CRESCENTI (1-TW895)",
      "10000000000TW896": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW896)",
      "10000000000TW897": "WINSAVING (1-TW897)",
      "10000000000TW899": "CAPITALE RISPARMIO AD ALTA PREVIDENZA (E60) (1-TW899)",
      "10000000000TW900": "RISPARMIO E PROTEZIONE (1-TW900)",
      "10000000000TW901": "WINBOND (E14) (1-TW901)",
      "10000000000TW902": "RISPARMIO E PROTEZIONE (E00) (1-TW902)",
      "10000000000TW907": "VITA INTERA A CAPITALE RIVALUTATO ED A PREMIO UNIC (1-TW907)",
      "10000000000TW909": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW909)",
      "10000000000TW913": "CAPITALE RISPARMIO (1-TW913)",
      "10000000000TW914": "WINBOND (1-TW914)",
      "10000000000TW915": "WINSAVING (R5E) (1-TW915)",
      "10000000000TW918": "RENDITA IMMEDIATA REVERSIBILE A PREMIO UNICO (1-TW918)",
      "10000000000TW921": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW921)",
      "10000000000TW922": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW922)",
      "10000000000TW923": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW923)",
      "10000000000TW924": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW924)",
      "10000000000TW925": "VITA INTERA A BENEF.GARANTITI A PREMIO UNICO (1-TW925)",
      "10000000000TW927": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW927)",
      "10000000000TW928": "VITA INTERA A BENEFICI GARANTITI A PREMIO UNICO (1-TW928)",
      "10000000000TW929": "ACCANTONAMENTO PER INVESTIMENTO (1-TW929)",
      "10000000000TW930": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO COSTANTE (1-TW930)",
      "10000000000TW931": "MISTA A BENEFICI GARANTITI A PREMIO UNICO (1-TW931)",
      "10000000000TW932": "MISTA CON TRIPLICAZIONE DEL CAPITALE CASO MORTE A (1-TW932)",
      "10000000000TW933": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW933)",
      "10000000000TW934": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW934)",
      "10000000000TW935": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW935)",
      "10000000000TW936": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW936)",
      "10000000000TW937": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW937)",
      "10000000000TW938": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW938)",
      "10000000000TW939": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW939)",
      "10000000000TW940": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW940)",
      "10000000000TW941": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW941)",
      "10000000000TW943": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW943)",
      "10000000000TW944": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW944)",
      "10000000000TW945": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW945)",
      "10000000000TW946": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW946)",
      "10000000000TW948": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW948)",
      "10000000000TW949": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-TW949)",
      "10000000000TW953": "TEMPORANEA CASO MORTE A CAPITALE ED A PREMIO ANNUO (1-TW953)",
      "10000000000TW955": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW955)",
      "10000000000TW956": "TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE (1-TW956)",
      "10000000000TW959": "TEMPORANEA CASO MORTE DI RENDITA CERTA IN RATE (1-TW959)",
      "10000000000TW963": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW963)",
      "10000000000TW964": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW964)",
      "10000000000TW965": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW965)",
      "10000000000TW967": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW967)",
      "10000000000TW970": "MISTA A BENEFICI GARANTITI A PREMIO ANNUO (1-TW970)",
      "10000000000TW971": "VITA INTERA A PREMIO ANNUO RIVALUTABILE A (1-TW971)",
      "10000000000TW972": "TERMINE FISSO A BENEFICI GARANTITI A PREMIO (1-TW972)",
      "10000000000TW974": "RENDITA VITALIZIA DIFFERITA A BENEFICI GARANTITI A (1-TW974)",
      "10000000000TW976": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW976)",
      "10000000000TW977": "CAPITALE DIFFERITO A BENEFICI GARANTITI A PREMIO (1-TW977)",
      "10000000000TW979": "TEMPORANEA CASO MORTE A CAPITALE COSTANTE (1-TW979)",
      "10000000000TW981": "TERMINE FISSO A BENEF.GARANTITI A PREMIO (1-TW981)",
      "10000000000TW984": "TERMINE FISSO A BENEF.GARANTITI A PREMIO (1-TW984)",
      "10000000000TW985": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW985)",
      "10000000000TW986": "VITA INTERA A BENEFICI GARANTITI A PREMIO (1-TW986)",
      "10000000000TW996": "CSLIFEBOND (1-TW996)",
      "10000000000TW997": "WINBOND (VERS. 2002) (1-TW997)",
      "10000000000TW998": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW998)",
      "10000000000TW999": "RENDITA VITALIZIA DIFFERITA A PREMI UNICI RICORREN (1-TW999)",
      "100000TU16001CGA": "YOU INVESTIMENTO MIX II - CONVENZIONE (1-TU16001CGA)",
      "100000TU16001CGM": "YOU INVESTIMENTO MIX II - CONVENZIONE (1-TU16001CGM)",
      "1000000TU16001GA": "YOU INVESTIMENTO MIX II (1-TU16001GA)",
      "1000000TU16001GM": "YOU INVESTIMENTO MIX II (1-TU16001GM)",
      "100000TU16001KGA": "YOU INVESTIMENTO MIX II - COOP (1-TU16001KGA)",
      "100000TU16001KGM": "YOU INVESTIMENTO MIX II - COOP (1-TU16001KGM)",
      "100000TU16001RGA": "YOU INVESTIMENTO MIX II - FIDELITY (1-TU16001RGA)",
      "100000TU16001RGM": "YOU INVESTIMENTO MIX II - FIDELITY (1-TU16001RGM)",
      "100000000TU10022": "RISPARMISICURO II (1-TU10022)",
      "10000000TU10022C": "RISPARMISICURO II (1-TU10022C)",
      "10000000TU10022K": "RISPARMISICURO II (1-TU10022K)",
      "10000000TU20007P": "YOU VITA - FORMULA BASE (1-TU20007P)",
      "1000TU20007PINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU20007PINF1)",
      "1000TU20007PINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20007PINF2)",
      "100000000TU70004": "YOU RISPARMIO RENDITA (1-TU70004)",
      "100000000TU70005": "YOU RISPARMIO RENDITA - RENDITA CERTA 5 (1-TU70005)",
      "100000000TU70006": "YOU RISPARMIO RENDITA - RENDITA CERTA 10 (1-TU70006)",
      "100000000TU70007": "YOU RISPARMIO RENDITA - RENDITA REVERSIBILE (1-TU70007)",
      "100000000TU70008": "YOU RISPARMIO RENDITA - CON CONTROASSICURAZIONE (1-TU70008)",
      "100000000TU10020": "VITA INERA RIVALUTABILE A PREMIO UNICO (1-TU10020)",
      "10000000TU10020C": "VITA INERA RIVALUTABILE A PREMIO UNICO (1-TU10020C)",
      "10000000TU10020K": "VITA INERA RIVALUTABILE A PREMIO UNICO (1-TU10020K)",
      "10000000TU10020R": "VITA INERA RIVALUTABILE A PREMIO UNICO (1-TU10020R)",
      "100000000TU10021": "VITA INERA RIVALUTABILE A PREMIO UNICO (1-TU10021)",
      "100000000TU10018": "YOU RISPARMIO INSIEME (1-TU10018)",
      "10000000TU10018C": "YOU RISPARMIO INSIEME - CONVENZIONE (1-TU10018C)",
      "100000000TU10019": "VITA INTERA A PREMI ANNUI COSTANTI LIMITATI (1-TU10019)",
      "100000000TU20008": "YOU VITA - FORMULA TOP (1-TU20008)",
      "10000TU20008INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU20008INF1)",
      "10000TU20008INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20008INF2)",
      "100000000TU20009": "YOU VITA - FORMULA BASE (1-TU20009)",
      "100000000TU20010": "YOU VITA - FORMULA TOP (1-TU20010)",
      "100000000TU20011": "YOU VITA - FORMULA (1-TU20011)",
      "100000000TU20012": "YOU VITA - FORMULA BASE (1-TU20012)",
      "100000000TU20013": "YOU VITA - FORMULA FACILE (1-TU20013)",
      "10000TU20013INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU20013INF1)",
      "10000TU20013INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU20013INF2)",
      "100000000TU30002": "YOU RISPARMIO FAMIGLIA (1-TU30002)",
      "10000000TU30002C": "YOU RISPARMIO FAMIGLIA - CONVENZIONE (1-TU30002C)",
      "1000TU30002CINF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30002CINF1)",
      "1000TU30002CINF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30002CINF2)",
      "10000TU30002INF1": "GARANZIA COMPLEMENTARE INFORTUNI (1-TU30002INF1)",
      "10000TU30002INF2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30002INF2)",
      "100000000TU60003": "YOU RISPARMIO BIMBO (1-TU60003)",
      "10000000TU60003B": "YOU RISPARMIO BIMBO - VERSAMENTI AGGIUNTIVI (1-TU60003B)",
      "100000000TU60004": "YOU RISPARMIO BIMBO (1-TU60004)",
      "10000000TU60004B": "YOU RISPARMIO BIMBO - VERSAMENTI AGGIUNTIVI (1-TU60004B)",
      "100000000TU60005": "YOU RISPARMIO FLESSIBILE (1-TU60005)",
      "10000000TU60005C": "YOU RISPARMIO FLESSIBILE (1-TU60005C)",
      "100000TU16000CGA": "YOU INVESTIMENTO MIX - CONVENZIONE (1-TU16000CGA)",
      "100000TU16000CGM": "YOU INVESTIMENTO MIX - CONVENZIONE (1-TU16000CGM)",
      "1000000TU16000GA": "YOU INVESTIMENTO MIX (1-TU16000GA)",
      "1000000TU16000GM": "YOU INVESTIMENTO MIX (1-TU16000GM)",
      "100000TU16000KGA": "YOU INVESTIMENTO MIX - COOP (1-TU16000KGA)",
      "100000TU16000KGM": "YOU INVESTIMENTO MIX - COOP (1-TU16000KGM)",
      "100000TU16000RGA": "YOU INVESTIMENTO MIX - FIDELITY (1-TU16000RGA)",
      "100000TU16000RGM": "YOU INVESTIMENTO MIX - FIDELITY (1-TU16000RGM)",
      "100000000TU10017": "YOU INVESTIMENTO COUPON (1-TU10017)",
      "10000000TU10017C": "YOU INVESTIMENTO COUPON - CONVENZIONE (1-TU10017C)",
      "10000000TU10017K": "YOU INVESTIMENTO COUPON - COOP (1-TU10017K)",
      "10000000TU10017R": "YOU INVESTIMENTO COUPON - FIDELITY (1-TU10017R)",
      "100000000TU10016": "YOU INVESTIMENTO FACILE (1-TU10016)",
      "10000000TU10016C": "YOU INVESTIMENTO FACILE - CONVENZIONE (1-TU10016C)",
      "10000000TU10016K": "YOU INVESTIMENTO FACILE - COOP (1-TU10016K)",
      "10000000TU10016R": "YOU INVESTIMENTO FACILE - FIDELITY (1-TU10016R)",
      "100000000TU10014": "RISPARMI INSIEME (1-TU10014)",
      "10000000TU10014C": "RISPARMI INSIEME - CONVENZIONE (1-TU10014C)",
      "100000000TU30001": "PROTEGGICHIAMI-EVOLUTION (1-TU30001)",
      "10000000TU30001C": "PROTEGGICHIAMI-EVOLUTION - CONVENZIONE (1-TU30001C)",
      "100000000TU10015": "INVESTICAPITAL II (1-TU10015)",
      "100000000TU20000": "PROTEGGICHIAMI FORMULA BASE (1-TU20000)",
      "10000TU20000INF1": "PROTEGGICHIAMI FORMULA BASE - GARANZIA COMPLEMENTARE INFORTUNI (1-TU20000INF1)",
      "10000TU20000INF2": "PROTEGGICHIAMI FORMULA BASE - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRA (1-TU20000INF2)",
      "100000000TU20001": "PROTEGGICHIAMI FORMULA TOP (1-TU20001)",
      "10000TU20001INF1": "PROTEGGICHIAMI FORMULA TOP - GARANZIA COMPLEMENTARE INFORTUNI (1-TU20001INF1)",
      "10000TU20001INF2": "PROTEGGICHIAMI FORMULA TOP - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRAD (1-TU20001INF2)",
      "100000000TU20002": "PROTEGGICHIAMI FORMULA BASE (1-TU20002)",
      "100000000TU20003": "PROTEGGICHIAMI FORMULA TOP (1-TU20003)",
      "100000000TU20004": "PROTEGGICHIAMI FORMULA BASE (1-TU20004)",
      "100000000TU20005": "PROTEGGICHIAMI FORMULA BASE (1-TU20005)",
      "100000000TU20006": "PROTEGGICHIAMI FORMULA FACILE (1-TU20006)",
      "10000TU20006INF1": "PROTEGGICHIAMI FORMULA FACILE - GARANZIA COMPLEMENTARE INFORTUNI (1-TU20006INF1)",
      "10000TU20006INF2": "PROTEGGICHIAMI FORMULA FACILE - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE ST (1-TU20006INF2)",
      "100000TU15005CGS": "INVESTICERTO VI - CONVENZIONE (1-TU15005CGS)",
      "100000TU15005CSP": "INVESTICERTO VI - CONVENZIONE (1-TU15005CSP)",
      "1000000TU15005GS": "INVESTICERTO VI (1-TU15005GS)",
      "100000TU15005KGS": "INVESTICERTO VI - COOP (1-TU15005KGS)",
      "100000TU15005KSP": "INVESTICERTO VI - COOP (1-TU15005KSP)",
      "100000TU15005RGS": "INVESTICERTO VI - FIDELITY (1-TU15005RGS)",
      "100000TU15005RSP": "INVESTICERTO VI - FIDELITY (1-TU15005RSP)",
      "1000000TU15005SP": "INVESTICERTO VI (1-TU15005SP)",
      "100000000TU60002": "RISPARMI&SCEGLI (1-TU60002)",
      "10000000TU60002C": "RISPARMI&SCEGLI (1-TU60002C)",
      "100000TU15004CGS": "INVESTICERTO V - CONVENZIONE (1-TU15004CGS)",
      "100000TU15004CSP": "INVESTICERTO V - CONVENZIONE (1-TU15004CSP)",
      "1000000TU15004GS": "INVESTICERTO V (1-TU15004GS)",
      "100000TU15004KGS": "INVESTICERTO V - COOP (1-TU15004KGS)",
      "100000TU15004KSP": "INVESTICERTO V - COOP (1-TU15004KSP)",
      "100000TU15004RGS": "INVESTICERTO V - FIDELITY (1-TU15004RGS)",
      "100000TU15004RSP": "INVESTICERTO V - FIDELITY (1-TU15004RSP)",
      "1000000TU15004SP": "INVESTICERTO V (1-TU15004SP)",
      "100000000TU10011": "YOU INVESTIMENTO PLUS (1-TU10011)",
      "10000000TU10011C": "YOU INVESTIMENTO PLUS - CONVENZIONE (1-TU10011C)",
      "100000000TU10012": "INVESTICOUPON II (1-TU10012)",
      "10000000TU10012C": "INVESTICOUPON II - CONVENZIONE (1-TU10012C)",
      "10000000TU10012K": "INVESTICOUPON II - COOP (1-TU10012K)",
      "10000000TU10012R": "INVESTICOUPON II - FIDELITY (1-TU10012R)",
      "10000000TU10013R": "INVESTISMART II (1-TU10013R)",
      "100000TU10013RGS": "INVESTISMART II (1-TU10013RGS)",
      "100000TU15003CGS": "INVESTICERTO IV - CONVENZIONE (1-TU15003CGS)",
      "100000TU15003CSP": "INVESTICERTO IV - CONVENZIONE (1-TU15003CSP)",
      "1000000TU15003GS": "INVESTICERTO IV (1-TU15003GS)",
      "100000TU15003KGS": "INVESTICERTO IV - COOP (1-TU15003KGS)",
      "100000TU15003KSP": "INVESTICERTO IV - COOP (1-TU15003KSP)",
      "100000TU15003RGS": "INVESTICERTO IV - FIDELITY (1-TU15003RGS)",
      "100000TU15003RSP": "INVESTICERTO IV - FIDELITY (1-TU15003RSP)",
      "1000000TU15003SP": "INVESTICERTO IV (1-TU15003SP)",
      "100000TU15002CGS": "INVESTICERTO III - CONVENZIONE (1-TU15002CGS)",
      "100000TU15002CSP": "INVESTICERTO III - CONVENZIONE (1-TU15002CSP)",
      "1000000TU15002GS": "INVESTICERTO III (1-TU15002GS)",
      "100000TU15002KGS": "INVESTICERTO III - COOP (1-TU15002KGS)",
      "100000TU15002KSP": "INVESTICERTO III - COOP (1-TU15002KSP)",
      "100000TU15002RGS": "INVESTICERTO III - FIDELITY (1-TU15002RGS)",
      "100000TU15002RSP": "INVESTICERTO III - FIDELITY (1-TU15002RSP)",
      "1000000TU15002SP": "INVESTICERTO III (1-TU15002SP)",
      "100000000TU70000": "RISPARMI&RENDEORA (1-TU70000)",
      "100000000TU70001": "RISPARMI&RENDEORA - RENDITA CERTA 5 (1-TU70001)",
      "100000000TU70002": "RISPARMI&RENDEORA - RENDITA CERTA 10 (1-TU70002)",
      "100000000TU70003": "RISPARMI&RENDEORA - RENDITA REVERSIBILE (1-TU70003)",
      "100000TU15001CGS": "INVESTICERTO II - CONVENZIONE (1-TU15001CGS)",
      "100000TU15001CSP": "INVESTICERTO II - CONVENZIONE (1-TU15001CSP)",
      "1000000TU15001GS": "INVESTICERTO II (1-TU15001GS)",
      "100000TU15001KGS": "INVESTICERTO II - COOP (1-TU15001KGS)",
      "100000TU15001KSP": "INVESTICERTO II - COOP (1-TU15001KSP)",
      "100000TU15001RGS": "INVESTICERTO II - FIDELITY (1-TU15001RGS)",
      "100000TU15001RSP": "INVESTICERTO II - FIDELITY (1-TU15001RSP)",
      "1000000TU15001SP": "INVESTICERTO II (1-TU15001SP)",
      "100000000TU10009": "VITA INTERA LOAD PER BANCHE (1-TU10009)",
      "100000000TU10010": "VITA INTERA NO LOAD (1-TU10010)",
      "100000000TU10003": "RISPARMISICURO (1-TU10003)",
      "10000000TU10003C": "RISPARMISICURO - CONVENZIONE (1-TU10003C)",
      "10000000TU10003K": "RISPARMISICURO - COOP (1-TU10003K)",
      "100000TU15000CGS": "INVESTICERTO I - CONVENZIONE (1-TU15000CGS)",
      "100000TU15000CSP": "INVESTICERTO I - CONVENZIONE (1-TU15000CSP)",
      "1000000TU15000GS": "INVESTICERTO I (1-TU15000GS)",
      "100000TU15000KGS": "INVESTICERTO I - COOP (1-TU15000KGS)",
      "100000TU15000KSP": "INVESTICERTO I - COOP (1-TU15000KSP)",
      "100000TU15000RGS": "INVESTICERTO I - FIDELITY (1-TU15000RGS)",
      "100000TU15000RSP": "INVESTICERTO I - FIDELITY (1-TU15000RSP)",
      "1000000TU15000SP": "INVESTICERTO I (1-TU15000SP)",
      "100000000TU10002": "INVESTIFACILE (1-TU10002)",
      "10000000TU10002C": "INVESTIFACILE - CONVENZIONE (1-TU10002C)",
      "10000000TU10002R": "INVESTIFACILE - FIDELITY (1-TU10002R)",
      "100000000TU10005": "INVESTIPLUS (1-TU10005)",
      "10000000TU10005C": "INVESTIPLUS - CONVENZIONE (1-TU10005C)",
      "100000000TU10006": "INVESTICOUPON (1-TU10006)",
      "10000000TU10006C": "INVESTICOUPON - CONVENZIONE (1-TU10006C)",
      "10000000TU10006K": "INVESTICOUPON - COOP (1-TU10006K)",
      "10000000TU10006R": "INVESTICOUPON - FIDELITY (1-TU10006R)",
      "100000000TU10007": "INVESTICAPITAL (1-TU10007)",
      "100000000TU30000": "RISPARMI&TUTELI (1-TU30000)",
      "10000000TU30000C": "RISPARMI&TUTELI - CONVENZIONE (1-TU30000C)",
      "1000TU30000CINF1": "RISPARMI&TUTELI - CONVENZIONE - GARANZIA COMPLEMENTARE INFORTUNI (1-TU30000CINF1)",
      "1000TU30000CINF2": "RISPARMI&TUTELI - CONVENZIONE - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE ST (1-TU30000CINF2)",
      "10000TU30000INF1": "RISPARMI&TUTELI - GARANZIA COMPLEMENTARE INFORTUNI (1-TU30000INF1)",
      "10000TU30000INF2": "RISPARMI&TUTELI - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TU30000INF2)",
      "100000000TU60000": "RISPARMIBIMBO (1-TU60000)",
      "10000000TU60000B": "RISPARMIBIMBO - VERSAMENTI AGGIUNTIVI (1-TU60000B)",
      "100000000TU60001": "RISPARMIBIMBO (1-TU60001)",
      "1000000000TIA198": "UGF CAPITAL (1-TIA198)",
      "1000000000TIU198": "UGF CAPITAL (1-TIU198)",
      "1000000000TIA196": "CAPITAL PLUS II (1-TIA196)",
      "100000000TIA196C": "CAPITAL PLUS II - CONVENZIONE (1-TIA196C)",
      "1000000000TIA197": "CAPITAL COUPON II (1-TIA197)",
      "100000000TIA197C": "CAPITAL COUPON II (1-TIA197C)",
      "100000000TIA197R": "CAPITAL COUPON FIDELITY II (1-TIA197R)",
      "1000000000TIU196": "UGF CAPITALPLUS II (1-TIU196)",
      "100000000TIU196C": "UGF CAPITALPLUS II - CONVENZIONE (1-TIU196C)",
      "1000000000TIU197": "UGF CAPITALCOUPON II (1-TIU197)",
      "100000000TIU197C": "UGF CAPITALCOUPON II - CONVENZIONE (1-TIU197C)",
      "100000000TIU197R": "UGF CAPITALCOUPON II - REINVESTIMENTO (1-TIU197R)",
      "1000000000TIA201": "UNIPOL PER TE (TRASFORMAZIONE PER INDEX BANCHE ISLANDESI) (1-TIA201)",
      "10000000TIA201GS": "UNIPOL PER TE (TRASFORMAZIONE PER INDEX BANCHE ISLANDESI) (1-TIA201GS)",
      "1000000000TIU201": "UNIPOL PER TE (1-TIU201)",
      "10000000TIU201GS": "UNIPOL PER TE (TRASFORMAZIONE PER INDEX BANCHE ISLANDESI) (1-TIU201GS)",
      "1000000TIA195CGS": "INVESTIMENTO CERTO V - CONVENZIONE (1-TIA195CGS)",
      "1000000TIA195CSP": "INVESTIMENTO CERTO V - SPEC. PROVV. - CONVENZIONE (1-TIA195CSP)",
      "10000000TIA195GS": "INVESTIMENTO CERTO V (1-TIA195GS)",
      "1000000TIA195GSR": "INVESTIMENTO CERTO V FIDELITY (1-TIA195GSR)",
      "10000000TIA195SP": "INVESTIMENTO CERTO V - SPEC. PROVV. (1-TIA195SP)",
      "1000000TIA195SPR": "INVESTIMENTO CERTO V FIDELITY - SPEC. PROVV. (1-TIA195SPR)",
      "1000000TIU195CGS": "UGF INVESTIMENTO CERTO V - CONVENZIONE (1-TIU195CGS)",
      "1000000TIU195CSP": "UGF INVESTIMENTO CERTO V - CONVENZIONE (1-TIU195CSP)",
      "10000000TIU195GS": "UGF INVESTIMENTO CERTO V (1-TIU195GS)",
      "1000000TIU195GSR": "UGF INVESTIMENTO CERTO V - FIDELITY (1-TIU195GSR)",
      "10000000TIU195SP": "UGF INVESTIMENTO CERTO V (1-TIU195SP)",
      "1000000TIU195SPR": "UGF INVESTIMENTO CERTO V - FIDELITY (1-TIU195SPR)",
      "1000000TIA194CGS": "INVESTIMENTO CERTO IV - CONVENZIONE (1-TIA194CGS)",
      "1000000TIA194CSP": "INVESTIMENTO CERTO IV - SPEC. PROVV. - CONVENZIONE (1-TIA194CSP)",
      "10000000TIA194GS": "INVESTIMENTO CERTO IV (1-TIA194GS)",
      "1000000TIA194GSR": "INVESTIMENTO CERTO IV FIDELITY (1-TIA194GSR)",
      "10000000TIA194SP": "INVESTIMENTO CERTO IV - SPEC. PROVV. (1-TIA194SP)",
      "1000000TIA194SPR": "INVESTIMENTO CERTO IV FIDELITY - SPEC. PROVV. (1-TIA194SPR)",
      "1000000TIU194CGS": "UGF INVESTIMENTO CERTO IV - CONVENZIONE (1-TIU194CGS)",
      "1000000TIU194CSP": "UGF INVESTIMENTO CERTO IV - CONVENZIONE (1-TIU194CSP)",
      "10000000TIU194GS": "UGF INVESTIMENTO CERTO IV (1-TIU194GS)",
      "1000000TIU194GSR": "UGF INVESTIMENTO CERTO IV - FIDELITY (1-TIU194GSR)",
      "10000000TIU194SP": "UGF INVESTIMENTO CERTO IV (1-TIU194SP)",
      "1000000TIU194SPR": "UGF INVESTIMENTO CERTO IV - FIDELITY (1-TIU194SPR)",
      "1000000000TIA361": "UGF DOPPIA SICUREZZA II (1-TIA361)",
      "100000000TIA361C": "UGF DOPPIA SICUREZZA II - CONVENZIONE (1-TIA361C)",
      "1000TIA361CCOMP2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA361CCOMP2)",
      "1000TIA361CCOMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA361CCOMPL)",
      "10000TIA361COMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA361COMPL)",
      "1000TIA361COMPL2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA361COMPL2)",
      "1000000000TIU361": "UGF DOPPIA SICUREZZA II (1-TIU361)",
      "100000000TIU361C": "UGF DOPPIA SICUREZZA II - CONVENZIONE (1-TIU361C)",
      "1000TIU361CCOMP2": "UGF DOPPIA SICUREZZA II - CONVENZIONE - GARANZIA COMPLEMENTARE INFORTUNI DA INCI (1-TIU361CCOMP2)",
      "1000TIU361CCOMPL": "UGF DOPPIA SICUREZZA II - CONVENZIONE - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU361CCOMPL)",
      "10000TIU361COMPL": "UGF DOPPIA SICUREZZA II - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU361COMPL)",
      "1000TIU361COMPL2": "UGF DOPPIA SICUREZZA II - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIU361COMPL2)",
      "1000000TIA192CGS": "INVESTIMENTO CERTO III - CONVENZIONE (1-TIA192CGS)",
      "1000000TIA192CSP": "INVESTIMENTO CERTO III - SPEC. PROVV. - CONVENZIONE (1-TIA192CSP)",
      "10000000TIA192GS": "INVESTIMENTO CERTO III (1-TIA192GS)",
      "1000000TIA192GSR": "INVESTIMENTO CERTO III (1-TIA192GSR)",
      "10000000TIA192SP": "INVESTIMENTO CERTO III - SPEC. PROVV. (1-TIA192SP)",
      "1000000TIA192SPR": "INVESTIMENTO CERTO III - SPEC. PROVV. (1-TIA192SPR)",
      "1000000000TIA193": "INVESTIFACILE (1-TIA193)",
      "100000000TIA193C": "INVESTIFACILE - CONVENZIONE (1-TIA193C)",
      "100000000TIA193R": "INVESTIFACILE FIDELITY (1-TIA193R)",
      "1000000TIU192CGS": "UGF INVESTIMENTO CERTO III - CONVENZIONE (1-TIU192CGS)",
      "1000000TIU192CSP": "UGF INVESTIMENTO CERTO III - CONVENZIONE (1-TIU192CSP)",
      "10000000TIU192GS": "UGF INVESTIMENTO CERTO III (1-TIU192GS)",
      "1000000TIU192GSR": "UGF INVESTIMENTO CERTO III - REINVESTIMENTO (1-TIU192GSR)",
      "10000000TIU192SP": "UGF INVESTIMENTO CERTO III (1-TIU192SP)",
      "1000000TIU192SPR": "UGF INVESTIMENTO CERTO III - REINVESTIMENTO (1-TIU192SPR)",
      "1000000000TIU193": "UGF INVESTIFACILE (1-TIU193)",
      "100000000TIU193C": "UGF INVESTIFACILE - CONVENZIONE (1-TIU193C)",
      "100000000TIU193R": "UGF INVESTIFACILE FIDELITY (1-TIU193R)",
      "100000000TU10000": "INVESTILIFEBONUS (1-TU10000)",
      "100000000TU10001": "INVESTILIFEVALUE (1-TU10001)",
      "100000000000UT01": "INVESTISMART (1-UT01)",
      "100000000000UT02": "INVESTISMART (1-UT02)",
      "1000000TIA191CGS": "INVESTIMENTO CERTO II - CONVENZIONE (1-TIA191CGS)",
      "1000000TIA191CSP": "INVESTIMENTO CERTO II - SPEC. PROVV. - CONVENZIONE (1-TIA191CSP)",
      "10000000TIA191GS": "INVESTIMENTO CERTO II (1-TIA191GS)",
      "1000000TIA191GSR": "INVESTIMENTO CERTO II (1-TIA191GSR)",
      "10000000TIA191SP": "INVESTIMENTO CERTO II - SPEC. PROVV. (1-TIA191SP)",
      "1000000TIA191SPR": "INVESTIMENTO CERTO II - SPEC. PROVV. (1-TIA191SPR)",
      "1000000000TIA188": "UGF INVESTICHIARO (1-TIA188)",
      "10000000TIA188GS": "UGF INVESTICHIARO (1-TIA188GS)",
      "10000000TIA190GS": "UGF INVESTICHIARO (1-TIA190GS)",
      "10000000TIU188GS": "UGF INVESTICHIARO (1-TIU188GS)",
      "10000000TIU190GS": "UGF INVESTICHIARO (1-TIU190GS)",
      "1000000000TIA190": "UGF INVESTICHIARO SECOND EDITION (1-TIA190)",
      "1000000000TIU190": "UGF INVESTICHIARO II (1-TIU190)",
      "1000000000TIU188": "UGF INVESTICHIARO (1-TIU188)",
      "1000000TIA181CGS": "INVESTIMENTO CERTO - CONVENZIONE (1-TIA181CGS)",
      "1000000TIA181CSP": "INVESTIMENTO CERTO - SPEC. PROVV. - CONVENZIONE (1-TIA181CSP)",
      "1000000TIU181CGS": "INVESTIMENTO CERTO - CONVENZIONE (1-TIU181CGS)",
      "1000000TIU181CSP": "INVESTIMENTO CERTO - CONVENZIONE (1-TIU181CSP)",
      "100000000TIA351A": "UGF DOPPIA SICUREZZA (1-TIA351A)",
      "100000000TIA351B": "UGF DOPPIA SICUREZZA (1-TIA351B)",
      "100000000TIA351C": "UGF DOPPIA SICUREZZA (1-TIA351C)",
      "10000TIA351COMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA351COMPL)",
      "1000TIA351COMPL2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA351COMPL2)",
      "100000000TIU351A": "UGF DOPPIA SICUREZZA (1-TIU351A)",
      "100000000TIU351B": "UGF DOPPIA SICUREZZA (1-TIU351B)",
      "100000000TIU351C": "UGF DOPPIA SICUREZZA (1-TIU351C)",
      "10000TIU351COMPL": "UGF DOPPIA SICUREZZA - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU351COMPL)",
      "1000TIU351COMPL2": "UGF DOPPIA SICUREZZA - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIU351COMPL2)",
      "100000000TIA611A": "UGF FUTURO (1-TIA611A)",
      "100000000TIA611B": "UGF FUTURO (1-TIA611B)",
      "100000000TIA611D": "UGF FUTURO CON VERSAMENTI AGGIUNTIVI (1-TIA611D)",
      "100000000TIU611A": "UGF FUTURO (1-TIU611A)",
      "100000000TIU611B": "UGF FUTURO (1-TIU611B)",
      "100000000TIU611D": "UGF FUTURO - VERSAMENTI AGGIUNTIVI (1-TIU611D)",
      "1000000000TIA182": "VALORIZZA RISPARMIO (1-TIA182)",
      "1000000000TIA186": "UGF PERFORMER (1-TIA186)",
      "10000000TIA186GS": "UGF PERFORMER (1-TIA186GS)",
      "1000000000TIU182": "UGF VALORIZZA RISPARMIO (1-TIU182)",
      "1000000000TIU186": "UGF PERFORMER (1-TIU186)",
      "10000000TIU186GS": "UGF PERFORMER (1-TIU186GS)",
      "1000000000TIA183": "CAPITAL PLUS (1-TIA183)",
      "100000000TIA183C": "CAPITAL PLUS - CONVENZIONE (1-TIA183C)",
      "1000000000TIA184": "CAPITAL COUPON (1-TIA184)",
      "100000000TIA184R": "CAPITAL COUPON FIDELITY (1-TIA184R)",
      "1000000000TIA185": "UGF VALUE (1-TIA185)",
      "1000000000TIU183": "UGF CAPITALPLUS (1-TIU183)",
      "100000000TIU183C": "UGF CAPITALPLUS - CONVENZIONE (1-TIU183C)",
      "1000000000TIU184": "UGF CAPITALCOUPON (1-TIU184)",
      "100000000TIU184R": "UGF CAPITALCOUPONFIDELITY (1-TIU184R)",
      "1000000000TIU185": "UGF VALUE (1-TIU185)",
      "10000000TIU181GS": "INVESTIMENTO CERTO (1-TIU181GS)",
      "10000000TIU181SP": "INVESTIMENTO CERTO (1-TIU181SP)",
      "1000000TIU181GSR": "INVESTIMENTO CERTO - REINVESTIMENTO (1-TIU181GSR)",
      "1000000TIU181SPR": "INVESTIMENTO CERTO - REINVESTIMENTO (1-TIU181SPR)",
      "10000000TIA181GS": "INVESTIMENTO CERTO (1-TIA181GS)",
      "1000000TIA181GSR": "INVESTIMENTO CERTO (1-TIA181GSR)",
      "10000000TIA181SP": "INVESTIMENTO CERTO - SPEC. PROVV. (1-TIA181SP)",
      "1000000TIA181SPR": "INVESTIMENTO CERTO - SPEC. PROVV. (1-TIA181SPR)",
      "1000000TIU191CGS": "INVESTIMENTO CERTO II - CONVENZIONE (1-TIU191CGS)",
      "1000000TIU191CSP": "INVESTIMENTO CERTO II - CONVENZIONE (1-TIU191CSP)",
      "10000000TIU191GS": "INVESTIMENTO CERTO II (1-TIU191GS)",
      "1000000TIU191GSR": "INVESTIMENTO CERTO II - REINVESTIMENTO (1-TIU191GSR)",
      "10000000TIU191SP": "INVESTIMENTO CERTO II (1-TIU191SP)",
      "1000000TIU191SPR": "INVESTIMENTO CERTO II - REINVESTIMENTO (1-TIU191SPR)",
      "1000000000TIA180": "ATTIVOGARANTITO (1-TIA180)",
      "10000000TIA180GS": "ATTIVOGARANTITO (1-TIA180GS)",
      "10000000TIA185GS": "UGF VALUEANTITO (1-TIA185GS)",
      "1000000000TIU180": "ATTIVOGARANTITO (1-TIU180)",
      "10000000TIU180GS": "ATTIVOGARANTITO (1-TIU180GS)",
      "10000000TIU185GS": "ATTIVOGARANTITO (1-TIU185GS)",
      "100000000TIA19GS": "SALVARISPARMIO (1-TIA19GS)",
      "10000000TIA19GSR": "SALVARISPARMIO REINV (1-TIA19GSR)",
      "100000000TIA19SP": "SALVARISPARMIO - SPEC. PROVV. (1-TIA19SP)",
      "10000000TIA19SPR": "SALVARISPARMIO REINV - SPEC. PROVV. (1-TIA19SPR)",
      "100000000TIU19GS": "SALVARISPARMIO (1-TIU19GS)",
      "10000000TIU19GSR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU19GSR)",
      "100000000TIU19SP": "SALVARISPARMIO (1-TIU19SP)",
      "10000000TIU19SPR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU19SPR)",
      "1000000000TIA189": "UGF LIFEVALUE (1-TIA189)",
      "1000000000TIA266": "GRANDI VALORI FORMULA BASE - TEMPORANEA CASO MORTE A CAPITALE E PREMIO ANNUO COS (1-TIA266)",
      "10000TIA266COMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA266COMPL)",
      "1000TIA266COMPL2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA266COMPL2)",
      "1000000000TIA267": "GRANDI VALORI FORMULA TOP - TEMPORANEA CASO MORTE A CAPITALE E PREMIO ANNUO COST (1-TIA267)",
      "10000TIA267COMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA267COMPL)",
      "1000TIA267COMPL2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA267COMPL2)",
      "1000000000TIA268": "GRANDI VALORI GRANDI VALORI FORMULA BASE - TEMPORANEA CASO MORTE A CAPITALE DECR (1-TIA268)",
      "1000000000TIA269": "GRANDI VALORI FORMULA TOP - TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE E PREMI (1-TIA269)",
      "1000000000TIA270": "GRANDI VALORI FORMULA BASE - TEMPORANEA CASO MORTE A CAPITALE COSTANTE E PREMIO (1-TIA270)",
      "1000000000TIA271": "GRANDI VALORI FORMULA TOP - TEMPORANEA CASO MORTE A CAPITALE COSTANTE E PREMIO U (1-TIA271)",
      "1000000000TIA272": "GRANDI VALORI FORMULA BASE - TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE E PREM (1-TIA272)",
      "1000000000TIA273": "GRANDI VALORI FORMULA TOP - TEMPORANEA CASO MORTE A CAPITALE DECRESCENTE E PREMI (1-TIA273)",
      "1000000000TIA274": "FORMULA FACILE - TEMPORANEA CASO MORTE A CAPITALE E PREMIO ANNUO COSTANTI A TAGL (1-TIA274)",
      "10000TIA274COMPL": "GARANZIA COMPLEMENTARE INFORTUNI (1-TIA274COMPL)",
      "1000TIA274COMPL2": "GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TIA274COMPL2)",
      "10000000TIA17AGS": "SALVARISPARMIO (1-TIA17AGS)",
      "1000000TIA17AGSR": "SALVARISPARMIO REINV (1-TIA17AGSR)",
      "10000000TIA17ASP": "SALVARISPARMIO - SPEC. PROVV. (1-TIA17ASP)",
      "1000000TIA17ASPR": "SALVARISPARMIO REINV - SPEC. PROVV. (1-TIA17ASPR)",
      "1000000000TIA179": "ASSICURAZIONE VITA INTERA A PREMIO UNICO (1-TIA179)",
      "1000000000TIA177": "ATTIVOGARANTITO (1-TIA177)",
      "10000000TIA177GS": "ATTIVOGARANTITO (1-TIA177GS)",
      "10000000000TI16A": "ASSICURAZIONE VI PUR A CAPITALE RIVAL. (1-TI16A)",
      "1000000000TIA176": "CAPITALE SCELTO (1-TIA176)",
      "100000000TIA176R": "CAPITALE SCELTO FIDELITY (1-TIA176R)",
      "10000000TIA15UGS": "SALVARISPARMIO BONUS (1-TIA15UGS)",
      "10000000TIA15USP": "SALVARISPARMIO BONUS - SPEC. PROVV. (1-TIA15USP)",
      "1000000000TIA341": "CAPITALE 2VALORI (1-TIA341)",
      "10000000TIA13UGS": "SALVARISPARMIO (1-TIA13UGS)",
      "1000000TIA13UGSR": "SALVARISPARMIO REINV (1-TIA13UGSR)",
      "10000000TIA13USP": "SALVARISPARMIO - SPEC. PROVV. (1-TIA13USP)",
      "1000000TIA13USPR": "SALVARISPARMIO REINV - SPEC. PROVV. (1-TIA13USPR)",
      "100000000TIUC175": "CAPITALE BONUS - CONVENZIONE (1-TIUC175)",
      "100000000TIUU175": "CAPITALE BONUS (1-TIUU175)",
      "100000000000A173": "PERLAAURORAMIX REINV II SERIE (1-A173)",
      "100000000000A172": "PERLAAURORAMIX II SERIE (1-A172)",
      "100000000000C601": "VALORE UNIPOL (1-C601)",
      "100000000000S601": "VALORE UNIPOL (1-S601)",
      "100000000000V601": "VALORE UNIPOL (1-V601)",
      "1000000TIUC601GS": "VALORE UNIPOL (1-TIUC601GS)",
      "1000000TIUC601SP": "VALORE UNIPOL (1-TIUC601SP)",
      "1000000TIUS601GS": "VALORE UNIPOL (1-TIUS601GS)",
      "1000000TIUS601SP": "VALORE UNIPOL (1-TIUS601SP)",
      "1000000TIUV601GS": "VALORE UNIPOL (1-TIUV601GS)",
      "1000000TIUV601SP": "VALORE UNIPOL (1-TIUV601SP)",
      "100000000000A170": "PERLAAURORAMIX (1-A170)",
      "100000000000A171": "PERLAAURORAMIX REINV (1-A171)",
      "100000000000C600": "TARIFFA C600 (1-C600)",
      "100000000000S600": "TARIFFA S600 (1-S600)",
      "100000000000V600": "TARIFFA V600 (1-V600)",
      "1000000TIUC600GS": "TARIFFA C600 (1-TIUC600GS)",
      "1000000TIUC600SP": "TARIFFA C600 (1-TIUC600SP)",
      "1000000TIUS600GS": "TARIFFA S600 (1-TIUS600GS)",
      "1000000TIUS600SP": "TARIFFA S600 (1-TIUS600SP)",
      "100000000000C175": "CAPITALE BONUS - CONVENZIONE (1-C175)",
      "10000000000TI16U": "CAPITALE ADHOC (1-TI16U)",
      "10000000TIA12UGS": "VALORE UNIPOL (1-TIA12UGS)",
      "1000000TIA12UGSR": "VALORE UNIPOL (1-TIA12UGSR)",
      "100000TIA12UGSRB": "PERLAAURORAMIX II SERIE (1-TIA12UGSRB)",
      "10000000TIA12USP": "VALORE UNIPOL (1-TIA12USP)",
      "1000000TIA12USPR": "VALORE UNIPOL (1-TIA12USPR)",
      "10000000000TIA14": "VITA INTERA A PREMIO UNICO PER LIQUID. DANNI (1-TIA14)",
      "1000000000TIA178": "NEW LIFEBOND BONUS (1-TIA178)",
      "1000000TIA18AGSR": "SALVARISPARMIO REINV (1-TIA18AGSR)",
      "1000000TIA18ASPR": "SALVARISPARMIO REINV - SPEC. PROVV. (1-TIA18ASPR)",
      "100000000TICOMPL": "CAPITALE 2VALORI - GARANZIA COMPLEMENTARE INFORTUNI (1-TICOMPL)",
      "10000000TICOMPL2": "CAPITALE 2VALORI - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADALE (1-TICOMPL2)",
      "10000000TIU13UGS": "SALVARISPARMIO (1-TIU13UGS)",
      "1000000TIU13UGSR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU13UGSR)",
      "10000000TIU13USP": "SALVARISPARMIO (1-TIU13USP)",
      "1000000TIU13USPR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU13USPR)",
      "10000000TIU15UGS": "SALVARISPARMIO BONUS (1-TIU15UGS)",
      "10000000TIU15USP": "SALVARISPARMIO BONUS (1-TIU15USP)",
      "1000000000TIU176": "CAPITALE SCELTO (1-TIU176)",
      "100000000TIU176R": "CAPITALE SCELTO FIDELITY (1-TIU176R)",
      "1000000000TIU177": "ATTIVOGARANTITO (1-TIU177)",
      "10000000TIU177GS": "ATTIVOGARANTITO (1-TIU177GS)",
      "1000000000TIU179": "ASSICURAZIONE CASO MORTE A VITA INTERA (1-TIU179)",
      "10000000TIU17UGS": "SALVARISPARMIO (1-TIU17UGS)",
      "1000000TIU17UGSR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU17UGSR)",
      "10000000TIU17USP": "SALVARISPARMIO (1-TIU17USP)",
      "1000000TIU17USPR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU17USPR)",
      "1000000TIU18UGSR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU18UGSR)",
      "1000000TIU18USPR": "SALVARISPARMIO - REINVESTIMENTO (1-TIU18USPR)",
      "1000000000TIU266": "GRANDI VALORI FORMULA BASE (1-TIU266)",
      "10000TIU266COMPL": "GRANDI VALORI FORMULA BASE - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU266COMPL)",
      "1000TIU266COMPL2": "GRANDI VALORI FORMULA BASE - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRAD (1-TIU266COMPL2)",
      "1000000000TIU267": "GRANDI VALORI FORMULA TOP (1-TIU267)",
      "10000TIU267COMPL": "GRANDI VALORI FORMULA TOP - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU267COMPL)",
      "1000TIU267COMPL2": "GRANDI VALORI FORMULA TOP - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STRADA (1-TIU267COMPL2)",
      "1000000000TIU268": "GRANDI VALORI GRANDI VALORI FORMULA BASE (1-TIU268)",
      "1000000000TIU269": "GRANDI VALORI FORMULA TOP (1-TIU269)",
      "1000000000TIU270": "GRANDI VALORI FORMULA BASE (1-TIU270)",
      "1000000000TIU271": "GRANDI VALORI FORMULA TOP (1-TIU271)",
      "1000000000TIU272": "GRANDI VALORI FORMULA BASE (1-TIU272)",
      "1000000000TIU273": "GRANDI VALORI FORMULA TOP (1-TIU273)",
      "1000000000TIU274": "GRANDI VALORI FORMULA FACILE (1-TIU274)",
      "10000TIU274COMPL": "GRANDI VALORI FORMULA FACILE - GARANZIA COMPLEMENTARE INFORTUNI (1-TIU274COMPL)",
      "1000TIU274COMPL2": "GRANDI VALORI FORMULA FACILE - GARANZIA COMPLEMENTARE INFORTUNI DA INCIDENTE STR (1-TIU274COMPL2)",
      "1000000000TIU341": "CAPITALE 2VALORI (1-TIU341)",
      "100000000000U175": "CAPITALE BONUS (1-U175)",
      "1000000TIUV600GS": "TARIFFA V600 (1-TIUV600GS)",
      "1000000TIUV600SP": "TARIFFA V600 (1-TIUV600SP)",
      "100000000TIUTD08": "DIFFERIMENTO (1-TIUTD08)"
    };
    return tariffaMap[normalizedCode] || code;
  }

  normalizeCode(code) {
    // Restituisce una stringa di 15 caratteri, allineando il codice a destra e riempiendo a sinistra con zeri
    if (!code) return '';
    const str = String(code);
    return str.padStart(15, '0');
  }
}