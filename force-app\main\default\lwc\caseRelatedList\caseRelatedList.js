import { LightningElement, wire, api } from 'lwc';
import { NavigationMixin, CurrentPageReference } from 'lightning/navigation';
import { IsConsoleNavigation, openTab, openSubtab, getFocusedTabInfo, setTabIcon, getTabInfo, EnclosingTabId} from 'lightning/platformWorkspaceApi';
import getRelatedCases from '@salesforce/apex/CaseRelatedListController.getRelatedCases';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';
export default class CaseRelatedList extends NavigationMixin(LightningElement) {

    @wire(CurrentPageReference) currentPageRef;
    @wire(IsConsoleNavigation) IsConsoleNavigation;
    @wire(EnclosingTabId) tabId;
    parentTabId;

    @api recordId;
    @api stato;

    status = '';
    isViewAll = false;
    isViewMore = false;

    recordsToShow = 3;

    showRelated = true;
    newCaseFlow = false;
    flowFinished;

    flowInputs = {};

    showOtherAttività = 'Mostra Attività Chiuse';
    showTable = false;
    attivitàTitle = 'Attività Aperte';
    
    attivitàNumber = 0;
    noRecords = true;

    records;

    columns = [
        {label: 'Nome Attività', fieldName: 'caseUrl', type: 'url', typeAttributes: {label: {fieldName: 'CaseNumber'}, target: '_self'}},
        {label: 'Attività', fieldName: 'Activity__c'},
        {label: 'Dettaglio', fieldName: 'Detail__c'},
        {label: 'Ambito', fieldName: 'Area__c'},
        {label: 'Data Scadenza', fieldName: 'DueDate__c', type: 'date-local'},
        {label: 'Assegnatario', fieldName: 'Assignee__c', type: 'richText'}
        //{label: 'Assegnatario', fieldName: 'userUrl', type: 'url', typeAttributes: {label: {fieldName: 'userName'}, target: '_self'}},
    ];

    connectedCallback(){
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        this.newCaseFlow = false;
        this.showRelated = true;

        if (this.stato == 'Aperte') {
            
            this.status = '';
        } else if (this.stato == 'Chiuse') {
            
            this.status = 'Closed';
        }

        if (this.recordId === undefined && this.currentPageRef?.state?.c__recordId !== undefined) {

            this.recordId = this.currentPageRef.state.c__recordId;
        }
        
        if (this.currentPageRef?.state?.c__status !== undefined) {

            this.status = this.currentPageRef.state.c__status;
        }

        if (this.currentPageRef?.state?.c__isViewAll !== undefined) {

            this.isViewAll = this.currentPageRef.state.c__isViewAll;
        }

        if (this.isViewAll == true) {

            this.recordsToShow = 20;

            this.columns = [
                {label: 'Nome Attività', fieldName: 'caseUrl', type: 'url', typeAttributes: {label: {fieldName: 'CaseNumber'}, target: '_self'}},
                {label: 'Priorità', fieldName: 'Priority'},
                {label: 'Area', fieldName: 'Area__c'},
                {label: 'Attività', fieldName: 'Activity__c'},
                {label: 'Dettaglio', fieldName: 'Detail__c'},
                {label: 'Ambito', fieldName: 'Area__c'},
                {label: 'Soggetto', fieldName: 'AccountName'},
                {label: 'Data Creazione', fieldName: 'CreatedDate', type: 'date-local'},
                {label: 'Data Scadenza', fieldName: 'DueDate__c', type: 'date-local'},
                {label: 'Stato', fieldName: 'Status'},
                {label: 'Fonte', fieldName: 'Source__c'},
                {label: 'Assegnatario', fieldName: 'Assignee__c', type: 'richText'},
                {label: 'In Carico a', fieldName: 'AssignedToFormula__c'},
            ];
        }

        if (this.status == 'Closed') {
            this.attivitàTitle = 'Attività Chiuse';
            this.showOtherAttività = 'Mostra Attività Aperte';
        }

        this.showTable = false;

        if (this.currentPageRef?.state?.c__openFlow == true && this.flowFinished !== true) {
            this.showRelated = false;
            this.newCaseFlow = true;
        } else {

            this.getCases();
        }
        
        this.flowInputs = [
            { name: 'accountId', type: 'String', value: this.recordId}
        ];

        this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    getCases(){
        getRelatedCases({accountId: this.recordId, status: this.status})
        .then(result=>{        
            console.log('then');
            this.records = result;
            this.attivitàNumber = this.records.length? this.records.length : 0;
            console.log('data.length: ' + this.records.length);
            
            if(this.records.length > 0){

                if (this.isViewAll == true) {
        
                    if(this.records.length <= this.recordsToShow){
        
                        this.isViewMore = false;
                    } else {
                        this.isViewMore = true;
                    }
                } else {

                    this.noRecords = false;
                }

                if(this.records.length > this.recordsToShow ){
                    this.records = this.records.slice(0, this.recordsToShow);
                }

                this.records = this.records.map(element => 
                    ({...element,

                        caseUrl: '/' + element.Id,
                        userUrl: element.AssignedTo__c? '/' + element.AssignedTo__c : '',
                        userName: element.AssignedTo__r?.Name? element.AssignedTo__r.Name : '',
                        AccountName: element.Account.Name
                    })
                )

                this.showTable = true;
                this.showRelated = true;
            }

        }).catch(error=>{
            console.log(JSON.stringify(error));
        });
    }


    clickOtherAttività(){
        if (this.attivitàTitle === 'Attività Aperte') {

            this.attivitàTitle = 'Attività Chiuse';
            this.showOtherAttività = 'Mostra Attività Aperte';
            this.status = 'Closed';
            this.getCases();
        } else {

            this.attivitàTitle = 'Attività Aperte';
            this.showOtherAttività = 'Mostra Attività Chiuse';
            this.status = '';
            this.getCases();
        }
    }

    async clickViewAll(){

        /*
        this[NavigationMixin.Navigate]({
            type: 'standard__recordRelationshipPage',
            attributes: {
                recordId: this.recordId,
                objectApiName: 'Account',
                relationshipApiName: 'Cases',
                actionName: 'view'
            },
        });
        
        
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: '/lightning/cmp/force__dynamicRelatedListViewAll?force__flexipageId=Individual_Record_Page2&force__cmpId=lst_dynamicRelatedList2&force__recordId=' + this.recordId + '&ws=%2Flightning%2Fr%2FAccount%2F' + this.recordId + '%2Fview&uid=*************'
            },
        });
        */

        //commentato per test apertura sottotab
        /*
        if(!this.IsConsoleNavigation){
            console.log('check console false');
            return;
        }

        

        try{
            await openTab({
                pageReference: {
                    type: "standard__component",
                    attributes: {
                        componentName: "c__caseRelatedList",
                    },
                    state: {
                        c__recordId: this.recordId,
                        c__status: this.status,
                        c__isViewAll: true
                    }   
                },
                icon: "standard:case",
                label: "Tutte le Attività",
            });
        } catch (error) {
            console.log(JSON.stringify(error))
        }
        
        
        this[NavigationMixin.Navigate]({
            type: 'standard__component',
            attributes: {
                componentName: "c__caseRelatedList"
            },
            state: {
                c__recordId: this.recordId,
                c__status: this.status,
                c__isViewAll: true
            }
        });
        */

        if(!this.IsConsoleNavigation){
            console.log('check console false');
            return;
        }

        if (!this.tabId) {
            return;
        }

        console.log('tab id: ' + this.tabId );

        const tabInfo = await getTabInfo(this.tabId);
        const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId;
        console.log('TABINFO: ' + JSON.stringify(tabInfo));

        try{
            await openSubtab( 
                primaryTabId, {
                    pageReference: {
                        type: "standard__component",
                        attributes: {
                            componentName: "c__caseRelatedList",
                        },
                        state: {
                            c__recordId: this.recordId,
                            c__status: this.status,
                            c__isViewAll: true
                        }   
                    },
                    focus: true,
                    icon: "standard:case",
                    label: this.status == 'Closed' ? "Attività Chiuse" : "Attività Aperte",
                }
            );
        } catch (error) {
            console.log(JSON.stringify(error))
        }
        
    }

    clickLoadMore(){

        this.recordsToShow += 20;
        this.getCases();
    }

    async clickNuovaAttività(){

        try{
            await openTab({
                pageReference: {
                    type: "standard__component",
                    attributes: {
                        componentName: "c__caseRelatedList",
                    },
                    state: {
                        c__recordId: this.recordId,
                        c__openFlow: true
                    }   
                },
                icon: "standard:case",
                label: "Nuova Attività",
            });
        } catch (error) {
            // handle error
            console.log(JSON.stringify(error));
        }
    }

    clickNuovaAttivitàTest(){

        this.showRelated = false;
        this.newCaseFlow = true;
        this.isModalOpen = true;
        /*
        
        this[NavigationMixin.Navigate]({
            type: 'standard__quickAction',
            attributes: {
                globalActionName: 'Nuova_attivita' // Replace with the global action's API name
            },
        })
        */
    }

    async handleFlowStatusChange(event){
        console.log('handleChange: ' + JSON.stringify(event.detail));
        console.log(event.detail.status);
        if (event.detail.status === 'FINISHED') {

            console.log('entro check flow status');
            this.newCaseFlow = false;
            this.flowFinished = true;
            this.isModalOpen = false;
            this.getCases();
        }
    }

    closeModal(){

        this.showRelated = true;
        this.newCaseFlow = false;
        this.isModalOpen = false;
    }

    refreshContainer(refreshPromise) {
        console.log("refreshing of caseRelatedList");
        //location.reload(); 
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        this.newCaseFlow = false;
        this.showRelated = true;

        if (this.stato == 'Aperte') {
            
            this.status = '';
        } else if (this.stato == 'Chiuse') {
            
            this.status = 'Closed';
        }

        if (this.recordId === undefined && this.currentPageRef?.state?.c__recordId !== undefined) {

            this.recordId = this.currentPageRef.state.c__recordId;
        }
        
        if (this.currentPageRef?.state?.c__status !== undefined) {

            this.status = this.currentPageRef.state.c__status;
        }

        if (this.currentPageRef?.state?.c__isViewAll !== undefined) {

            this.isViewAll = this.currentPageRef.state.c__isViewAll;
        }

        if (this.isViewAll == true) {

            this.recordsToShow = 20;

            this.columns = [
                {label: 'Nome Attività', fieldName: 'caseUrl', type: 'url', typeAttributes: {label: {fieldName: 'CaseNumber'}, target: '_self'}},
                {label: 'Priorità', fieldName: 'Priority'},
                {label: 'Area', fieldName: 'Area__c'},
                {label: 'Attività', fieldName: 'Activity__c'},
                {label: 'Dettaglio', fieldName: 'Detail__c'},
                {label: 'Ambito', fieldName: 'Area__c'},
                {label: 'Soggetto', fieldName: 'AccountName'},
                {label: 'Data Creazione', fieldName: 'CreatedDate', type: 'date-local'},
                {label: 'Data Scadenza', fieldName: 'DueDate__c', type: 'date-local'},
                {label: 'Stato', fieldName: 'Status'},
                {label: 'Fonte', fieldName: 'Source__c'},
                {label: 'Assegnatario', fieldName: 'Assignee__c', type: 'richText'},
                {label: 'In Carico a', fieldName: 'userUrl', type: 'url', typeAttributes: {label: {fieldName: 'userName'}, target: '_self'}},
            ];
        }

        if (this.status == 'Closed') {
            this.attivitàTitle = 'Attività Chiuse';
            this.showOtherAttività = 'Mostra Attività Aperte';
        }

        this.showTable = false;

        if (this.currentPageRef?.state?.c__openFlow == true && this.flowFinished !== true) {
            console.log('entro check param flow');
            this.showRelated = false;
            this.newCaseFlow = true;
        } else {

            this.getCases();
        }
        
        this.flowInputs = [
            { name: 'accountId', type: 'String', value: this.recordId}
        ];

        
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("refresh of caseRelatedList Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("refresh of caseRelatedList Done, with issues refreshing some components");
            } else if (status === REFRESH_ERROR) {
                console.error("refresh of caseRelatedList Major error with refresh.");
            }
        });
    }

    /*

    async setTabIcon() {
        if (!this.isConsoleNavigation) {
            return;
        }
        console.log('setTabIcon');

        try {

            const { tabId } = await getFocusedTabInfo();
            console.log(JSON.stringify(tabId));
            setTabIcon(tabId, 'standard:case', {
                iconAlt: 'Tutte le Attività'
            });

        } catch (error) {
            console.log(JSON.stringify(error));
            // handle error))
        }
        
    }

    */
}