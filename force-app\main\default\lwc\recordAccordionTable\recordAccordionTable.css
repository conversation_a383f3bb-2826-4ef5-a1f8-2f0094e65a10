/* CONTENITORE PRINCIPALE */
.record-table {
  font-family: var(--lwc-fontFamily);
  border: 1px solid #d8dde6;
  border-radius: 0.25rem;
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
  width: 100%;
  transition: padding-bottom 0.3s ease;
}

/* HEADER TABELLA */
.table-header {
  display: flex;
  background-color: #f3f6f9;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #d8dde6;
  min-width: 960px;
}

/* ROW PRINCIPALE */
.table-row {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  position: relative;
  z-index: 0;
  overflow: visible;
  min-width: 960px;
}

.column {
  flex: 1;
  padding: 0.25rem 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.toggle {
  flex: 0 0 40px;
  text-align: center;
  padding-left: 0;
  padding-right: 0;
}

.product-name { flex: 2; }
.policy-id { flex: 2; }
.role { flex: 1.5; }
.prize { flex: 1; }
.due-date { flex: 1; }

.column.actions {
  flex: none;
  width: 60px;
  text-align: right;
  position: relative;
  overflow: visible;
  z-index: 100;
}

.custom-dropdown-wrapper {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  z-index: 3;
  padding: 0.5rem 0;
  list-style: none;
  margin: 0;
  min-width: 220px;
  max-width: 300px;
  overflow: visible;
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
}

.dropdown-menu li {
  padding: 0.25rem 0.75rem;
  cursor: pointer;
  white-space: nowrap;
  text-align: right;
  width: 100%;
}

.dropdown-menu li:hover {
  background-color: #f2f2f2;
}

.accordion-row {
  border-bottom: 1px solid #d8dde6;
  overflow: visible;
  width: 100%;
  display: block;
}

.accordion-row.dropdown-open {
  position: relative;
  z-index: 2;
}

.accordion-details {
  background-color: #f9f9f9;
  padding: 1rem 2rem;
  border-top: 1px solid #e5e5e5;
  border-radius: 0 0 0.5rem 0.5rem;
  overflow-x: auto; /* ATTENZIONE AGGIUNTO */
  width: 100%; /* LARGHEZZA TOTALE SEMPRE */
  box-sizing: border-box;
  display: block;
}

.accordion-details-content {
  min-width: 960px; /* NON LARGHEZZA FISSA MA MINIMA */
}


/* NESTED ACCORDION */
.nested-accordion {
  margin-top: 1rem;
  margin-left: 1rem;
  border-left: 2px solid #d8dde6;
  padding-left: 1rem;
}

.nested-accordion-item {
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e5e5;
}

.nested-accordion-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.3rem;
  cursor: pointer;
  background-color: #f3f6f9;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
  font-size: 0.75rem;
  font-weight: bold;
}

.nested-accordion-header:hover {
  background-color: #e5eaf0;
}

.nested-accordion-header .arrow-icon {
  flex-shrink: 0;
}

.nested-accordion-body {
  padding: 1rem;
  background-color: #fff;
  border-radius: 0.25rem;
  margin-top: 0.5rem;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem 2rem;
}

.card-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  word-break: break-word;
  overflow-wrap: anywhere;
}
.card-item strong,
.card-item span {
  display: inline-block;
  max-width: 100%;
  word-break: break-word;
  white-space: normal;
  overflow-wrap: anywhere;
  line-height: 1.2;
}
.card-item strong {
  font-weight: bold;
  font-size: 0.75rem;
}


.spinner-container {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

.spinner-container.absolute-overlay {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.6);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.no-data-message {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
}

.accordion-spinner-wrapper {
  position: relative;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media screen and (max-width: 768px) {
  .nested-accordion {
    margin-left: 0.5rem;
    padding-left: 0.5rem;
  }
  .card-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}
.polizza-link {
  color: #0176d3;
  text-decoration: underline;
  cursor: pointer;
}
/* Icon styling within tabs */
.tab-icon {
  margin-right: 8px;
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}

/* Static icon size adjustments */
.static-icon {
  width: 20px !important;
  height: 20px !important;
  object-fit: contain;
  display: inline-block;
  margin-right: 6px;
  vertical-align: middle;
}

/* Custom icon size adjustments */
.custom-icon lightning-primitive-icon {
  width: 18px !important;
  height: 18px !important;
}

@media screen and (max-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.card-highlight {
  background: #f4f4f4;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  width: 100%;
}