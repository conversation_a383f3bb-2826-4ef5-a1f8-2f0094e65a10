<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>SS_DettagliiSocioEconomiciChild/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Anag_GetDataEconomy&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{Params.c__accountId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Params.c__accountId\&quot;:\&quot;{Params.c__accountId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__accountId&quot;,&quot;val&quot;:&quot;0019X00001CTbpdQAD&quot;,&quot;id&quot;:10}]},&quot;state1element0block_element1_0&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\&quot;Params.c__editMode\&quot; : false\n}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__accountId&quot;,&quot;val&quot;:&quot;0019X000016F3tyQAC&quot;,&quot;id&quot;:10}]},&quot;state1element0block_element2_0&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\&quot;Params.c__editMode\&quot; : false\n}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state1element0block_element2_1&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Anag_UpsertDataEconomy&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ANAG2_STATOCIVILE&quot;:&quot;{accountKPI.ANAG2_STATOCIVILE.apiName}&quot;,&quot;ANAG2_FLAGCONIUGECARICO&quot;:&quot;{accountKPI.ANAG2_FLAGCONIUGECARICO.apiName}&quot;,&quot;ANAG2_DATANASCITACONIUGE&quot;:&quot;{accountKPI.ANAG2_DATANASCITACONIUGE.apiName}&quot;,&quot;ANAG2_FLAGFIGLI&quot;:&quot;{accountKPI.ANAG2_FLAGFIGLI.apiName}&quot;,&quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot;:&quot;{accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName}&quot;,&quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot;:&quot;{accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName}&quot;,&quot;ANAG2_TITOLODISTUDIO&quot;:&quot;{accountKPI.ANAG2_TITOLODISTUDIO.apiName}&quot;,&quot;ANAG2_FLAGANIMALIDOMESTICI&quot;:&quot;{accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName}&quot;,&quot;ANAG2_FLAGCASAPROPRIETA&quot;:&quot;{accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName}&quot;,&quot;ANAG2_FLAGSECONDACASA&quot;:&quot;{accountKPI.ANAG2_FLAGSECONDACASA.apiName}&quot;,&quot;ANAG2_FLAGCASAAFFITTO&quot;:&quot;{accountKPI.ANAG2_FLAGCASAAFFITTO.apiName}&quot;,&quot;ANAG2_FLAGMUTUO&quot;:&quot;{accountKPI.ANAG2_FLAGMUTUO.apiName}&quot;,&quot;ANAG2_REDDITOLAVORO&quot;:&quot;{accountKPI.ANAG2_REDDITOLAVORO.apiName}&quot;,&quot;ANAG2_SPESECOMPRIMIBILI&quot;:&quot;{accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName}&quot;,&quot;ANAG2_SPESEINCOMPRIMIBILI&quot;:&quot;{accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName}&quot;,&quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot;:&quot;{accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName}&quot;,&quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot;:&quot;{accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName}&quot;,&quot;ANAG2_DATIATTRIBUTI_UTILIZZO_INTERNET&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName}&quot;,&quot;ANAG2_DATIATTRIBUTI_INTERESSI&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName}&quot;,&quot;ANAG2_TIPORISPARMIO&quot;:&quot;{accountKPI.ANAG2_TIPORISPARMIO.apiName}&quot;,&quot;ANAG2_DATIATTRIBUTI_VORREI_SAPERNE_DI_PIU&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName}&quot;,&quot;ANAG2_DATIATTRIBUTI_A_BREVE_PREVEDO&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName}&quot;,&quot;societyAccountAccountRelationId&quot;:&quot;{accountKPI.society_account_account_relation_id}&quot;,&quot;ANAG2_NUMEROFIGLIOCARICO&quot;:&quot;{accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName}&quot;,&quot;ANAG2_DATENASCITAFIGLIO_1&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1}&quot;,&quot;ANAG2_DATENASCITAFIGLIO_2&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2}&quot;,&quot;ANAG2_DATENASCITAFIGLIO_3&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3}&quot;,&quot;ANAG2_DATENASCITAFIGLIO_4&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4}&quot;,&quot;ciu&quot;:&quot;{accountKPI.ciu}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;accountKPI.ANAG2_STATOCIVILE.apiName\&quot;:\&quot;{accountKPI.ANAG2_STATOCIVILE.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGCONIUGECARICO.apiName}\&quot;,\&quot;accountKPI.ANAG2_DATANASCITACONIUGE.apiName\&quot;:\&quot;{accountKPI.ANAG2_DATANASCITACONIUGE.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGFIGLI.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGFIGLI.apiName}\&quot;,\&quot;accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName\&quot;:\&quot;{accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName}\&quot;,\&quot;accountKPI.ANAG2_TITOLODISTUDIO.apiName\&quot;:\&quot;{accountKPI.ANAG2_TITOLODISTUDIO.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGSECONDACASA.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGSECONDACASA.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGCASAAFFITTO.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGCASAAFFITTO.apiName}\&quot;,\&quot;accountKPI.ANAG2_FLAGMUTUO.apiName\&quot;:\&quot;{accountKPI.ANAG2_FLAGMUTUO.apiName}\&quot;,\&quot;accountKPI.ANAG2_REDDITOLAVORO.apiName\&quot;:\&quot;{accountKPI.ANAG2_REDDITOLAVORO.apiName}\&quot;,\&quot;accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName\&quot;:\&quot;{accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName}\&quot;,\&quot;accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName\&quot;:\&quot;{accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName}\&quot;,\&quot;accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName\&quot;:\&quot;{accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName}\&quot;,\&quot;accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName\&quot;:\&quot;{accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName}\&quot;,\&quot;accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName}\&quot;,\&quot;accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName}\&quot;,\&quot;accountKPI.ANAG2_TIPORISPARMIO.apiName\&quot;:\&quot;{accountKPI.ANAG2_TIPORISPARMIO.apiName}\&quot;,\&quot;accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName}\&quot;,\&quot;accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName}\&quot;,\&quot;accountKPI.society_account_account_relation_id\&quot;:\&quot;{accountKPI.society_account_account_relation_id}\&quot;,\&quot;accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName\&quot;:\&quot;{accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName}\&quot;,\&quot;accountKPI.ciu\&quot;:\&quot;{accountKPI.ciu}\&quot;,\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1}\&quot;,\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2}\&quot;,\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3}\&quot;,\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;accountKPI.ANAG2_STATOCIVILE.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:58},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:59},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATANASCITACONIUGE.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:60},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGFIGLI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:61},{&quot;name&quot;:&quot;accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:62},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:63},{&quot;name&quot;:&quot;accountKPI.ANAG2_TITOLODISTUDIO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:64},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:65},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:66},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGSECONDACASA.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:67},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGCASAAFFITTO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:68},{&quot;name&quot;:&quot;accountKPI.ANAG2_FLAGMUTUO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:69},{&quot;name&quot;:&quot;accountKPI.ANAG2_REDDITOLAVORO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:70},{&quot;name&quot;:&quot;accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:71},{&quot;name&quot;:&quot;accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:72},{&quot;name&quot;:&quot;accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:73},{&quot;name&quot;:&quot;accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:74},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:75},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:76},{&quot;name&quot;:&quot;accountKPI.ANAG2_TIPORISPARMIO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:77},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:78},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:79},{&quot;name&quot;:&quot;accountKPI.society_account_account_relation_id&quot;,&quot;val&quot;:&quot;a009X00000YyGkiQAF&quot;,&quot;id&quot;:80},{&quot;name&quot;:&quot;accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:81},{&quot;name&quot;:&quot;accountKPI.ciu&quot;,&quot;val&quot;:&quot;7167158&quot;,&quot;id&quot;:82},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:58},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:59},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:60},{&quot;name&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:61}]},&quot;state1element0block_element2_2&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Anag_GetDataEconomy&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{Params.c__accountId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Params.c__accountId\&quot;:\&quot;{Params.c__accountId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__accountId&quot;,&quot;val&quot;:&quot;0019X000013IoN5QAK&quot;,&quot;id&quot;:3}]},&quot;state0element0block_element1_0&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\&quot;Params.c__editMode\&quot; : true\n}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__accountId&quot;,&quot;val&quot;:&quot;0019X000016F3tyQAC&quot;,&quot;id&quot;:10}]}}</dataSourceConfig>
    <description>Cioffarelli Livio: 1338146</description>
    <isActive>true</isActive>
    <name>SS_DettagliSocioEconomiciChild</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Params.c__editMode&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;name&quot;:&quot;Dettagli Socio Economici - Sola lettura&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;no-repeat&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;  background-repeat:no-repeat;    \n         &quot;},&quot;blankCardState&quot;:false,&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%20class=%22slds-text-heading_medium%22%3EDati%20Anagrafici%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_11-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;userUpdatedElementLabel&quot;:true,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_11-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;11&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;datasourceKey&quot;:&quot;state0element0block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_action_1_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;Custom\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;body\&quot;:\&quot;{\\n\\\&quot;Params.c__editMode\\\&quot; : true\\n}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Params.c__accountId\&quot;,\&quot;val\&quot;:\&quot;0019X000016F3tyQAC\&quot;,\&quot;id\&quot;:10}]}&quot;,&quot;eventName&quot;:&quot;reload&quot;},&quot;key&quot;:&quot;*************-0t60zkfmt&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconOnly&quot;:false,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;iconSize&quot;:&quot;small&quot;,&quot;flyoutDetails&quot;:{},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-8&quot;,&quot;field&quot;:&quot;User.userProfileName&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Contact Center&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-clone-0-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;parsedProperty&quot;:{&quot;label&quot;:&quot;Modifica&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:{&quot;isAbbinato&quot;:false,&quot;accountKPI&quot;:{&quot;ANAG2_DATIATTRIBUTI&quot;:{&quot;a_breve_prevedo&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;vorrei_saperne_di_piu&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;utilizzo_internet&quot;:{&quot;label&quot;:&quot;Per accedere ai social; Per lavoro/informazioni; Per fare acquisti&quot;,&quot;apiName&quot;:&quot;SOC;LAV;ACQ&quot;},&quot;interessi&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;INT&quot;:{&quot;MCT&quot;:false,&quot;VIA&quot;:false,&quot;FOT&quot;:false,&quot;LET&quot;:false,&quot;ITT&quot;:false,&quot;FDT&quot;:false,&quot;ENO&quot;:false,&quot;SPO&quot;:false,&quot;MOT&quot;:false,&quot;VOL&quot;:false,&quot;SEB&quot;:false,&quot;NAT&quot;:false},&quot;NET&quot;:{&quot;ACQ&quot;:true,&quot;SOC&quot;:true,&quot;APP&quot;:false,&quot;LAV&quot;:true},&quot;PRE&quot;:{&quot;FMU&quot;:false,&quot;GOD&quot;:false,&quot;PRO&quot;:false,&quot;VAC&quot;:false,&quot;ABI&quot;:false,&quot;TSF&quot;:false,&quot;ATT&quot;:false,&quot;AUT&quot;:false},&quot;COM&quot;:{&quot;IND&quot;:false,&quot;RIS&quot;:false,&quot;POL&quot;:false,&quot;PER&quot;:false,&quot;GSF&quot;:false,&quot;REN&quot;:false,&quot;ERE&quot;:false,&quot;PEN&quot;:false,&quot;RSC&quot;:false,&quot;SAN&quot;:false,&quot;TER&quot;:false},&quot;label&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;,&quot;apiName&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;},&quot;ANAG2_NUMEROFIGLIOCARICO&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:2},&quot;ANAG2_DATENASCITAFIGLI&quot;:{&quot;underaged_children_number&quot;:0,&quot;overaged_children_number&quot;:2,&quot;label&quot;:&quot;[\&quot;2007-06-10\&quot;,\&quot;1997-06-12\&quot;]&quot;,&quot;apiName&quot;:{&quot;child_1&quot;:&quot;2007-06-10&quot;,&quot;child_2&quot;:&quot;1997-06-12&quot;,&quot;child_3&quot;:null,&quot;child_4&quot;:null}},&quot;ANAG2_ATTRIBUTI_TSF&quot;:{&quot;label&quot;:&quot;TSF&quot;,&quot;apiName&quot;:&quot;TSF&quot;},&quot;ANAG2_ATTRIBUTI_ATT&quot;:{&quot;label&quot;:&quot;ATT&quot;,&quot;apiName&quot;:&quot;ATT&quot;},&quot;ANAG2_ATTRIBUTI_FMU&quot;:{&quot;label&quot;:&quot;FMU&quot;,&quot;apiName&quot;:&quot;FMU&quot;},&quot;ANAG2_ATTRIBUTI_TER&quot;:{&quot;label&quot;:&quot;TER&quot;,&quot;apiName&quot;:&quot;TER&quot;},&quot;ANAG2_ATTRIBUTI_GSF&quot;:{&quot;label&quot;:&quot;GSF&quot;,&quot;apiName&quot;:&quot;GSF&quot;},&quot;ANAG2_ATTRIBUTI_RIS&quot;:{&quot;label&quot;:&quot;RIS&quot;,&quot;apiName&quot;:&quot;RIS&quot;},&quot;ANAG2_ATTRIBUTI_POL&quot;:{&quot;label&quot;:&quot;POL&quot;,&quot;apiName&quot;:&quot;POL&quot;},&quot;ANAG2_ATTRIBUTI_APP&quot;:{&quot;label&quot;:&quot;APP&quot;,&quot;apiName&quot;:&quot;APP&quot;},&quot;ANAG2_ATTRIBUTI_ACQ&quot;:{&quot;label&quot;:&quot;ACQ&quot;,&quot;apiName&quot;:&quot;ACQ&quot;},&quot;ANAG2_ATTRIBUTI_SOC&quot;:{&quot;label&quot;:&quot;SOC&quot;,&quot;apiName&quot;:&quot;SOC&quot;},&quot;ANAG2_ATTRIBUTI_LAV&quot;:{&quot;label&quot;:&quot;LAV&quot;,&quot;apiName&quot;:&quot;LAV&quot;},&quot;ANAG2_ATTRIBUTI_FOT&quot;:{&quot;label&quot;:&quot;FOT&quot;,&quot;apiName&quot;:&quot;FOT&quot;},&quot;ANAG2_ATTRIBUTI_VOL&quot;:{&quot;label&quot;:&quot;VOL&quot;,&quot;apiName&quot;:&quot;VOL&quot;},&quot;ANAG2_ATTRIBUTI_SEB&quot;:{&quot;label&quot;:&quot;SEB&quot;,&quot;apiName&quot;:&quot;SEB&quot;},&quot;ANAG2_ATTRIBUTI_VIA&quot;:{&quot;label&quot;:&quot;VIA&quot;,&quot;apiName&quot;:&quot;VIA&quot;},&quot;ANAG2_ATTRIBUTI_FDT&quot;:{&quot;label&quot;:&quot;FDT&quot;,&quot;apiName&quot;:&quot;FDT&quot;},&quot;ANAG2_FLAGMUTUO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGSECONDACASA&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAAFFITTO&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAPROPRIETA&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGANIMALIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot;:{&quot;label&quot;:6,&quot;apiName&quot;:6},&quot;ANAG2_SPESECOMPRIMIBILI&quot;:{&quot;label&quot;:60050,&quot;apiName&quot;:60050},&quot;ANAG2_SPESEINCOMPRIMIBILI&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_REDDITOLAVORO&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_FLAGCONIUGECARICO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_TIPORISPARMIO&quot;:{&quot;label&quot;:&quot;fino a 5.000 annui&quot;,&quot;apiName&quot;:1},&quot;ANAG2_TITOLODISTUDIO&quot;:{&quot;label&quot;:&quot;Scuola d&apos;obbligo&quot;,&quot;apiName&quot;:&quot;O&quot;},&quot;ANAG2_FLAGFIGLI&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_STATOCIVILE&quot;:{&quot;label&quot;:&quot;CONIUGATO/CONVIVENTE&quot;,&quot;apiName&quot;:&quot;C&quot;},&quot;ciu&quot;:7167158,&quot;society_account_account_relation_id&quot;:&quot;a009X00000YyGkiQAF&quot;}},&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:{&quot;isAbbinato&quot;:false,&quot;accountKPI&quot;:{&quot;ANAG2_DATIATTRIBUTI&quot;:{&quot;a_breve_prevedo&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;vorrei_saperne_di_piu&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;utilizzo_internet&quot;:{&quot;label&quot;:&quot;Per accedere ai social; Per lavoro/informazioni; Per fare acquisti&quot;,&quot;apiName&quot;:&quot;SOC;LAV;ACQ&quot;},&quot;interessi&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;INT&quot;:{&quot;MCT&quot;:false,&quot;VIA&quot;:false,&quot;FOT&quot;:false,&quot;LET&quot;:false,&quot;ITT&quot;:false,&quot;FDT&quot;:false,&quot;ENO&quot;:false,&quot;SPO&quot;:false,&quot;MOT&quot;:false,&quot;VOL&quot;:false,&quot;SEB&quot;:false,&quot;NAT&quot;:false},&quot;NET&quot;:{&quot;ACQ&quot;:true,&quot;SOC&quot;:true,&quot;APP&quot;:false,&quot;LAV&quot;:true},&quot;PRE&quot;:{&quot;FMU&quot;:false,&quot;GOD&quot;:false,&quot;PRO&quot;:false,&quot;VAC&quot;:false,&quot;ABI&quot;:false,&quot;TSF&quot;:false,&quot;ATT&quot;:false,&quot;AUT&quot;:false},&quot;COM&quot;:{&quot;IND&quot;:false,&quot;RIS&quot;:false,&quot;POL&quot;:false,&quot;PER&quot;:false,&quot;GSF&quot;:false,&quot;REN&quot;:false,&quot;ERE&quot;:false,&quot;PEN&quot;:false,&quot;RSC&quot;:false,&quot;SAN&quot;:false,&quot;TER&quot;:false},&quot;label&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;,&quot;apiName&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;},&quot;ANAG2_NUMEROFIGLIOCARICO&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:2},&quot;ANAG2_DATENASCITAFIGLI&quot;:{&quot;underaged_children_number&quot;:0,&quot;overaged_children_number&quot;:2,&quot;label&quot;:&quot;[\&quot;2007-06-10\&quot;,\&quot;1997-06-12\&quot;]&quot;,&quot;apiName&quot;:{&quot;child_1&quot;:&quot;2007-06-10&quot;,&quot;child_2&quot;:&quot;1997-06-12&quot;,&quot;child_3&quot;:null,&quot;child_4&quot;:null}},&quot;ANAG2_ATTRIBUTI_TSF&quot;:{&quot;label&quot;:&quot;TSF&quot;,&quot;apiName&quot;:&quot;TSF&quot;},&quot;ANAG2_ATTRIBUTI_ATT&quot;:{&quot;label&quot;:&quot;ATT&quot;,&quot;apiName&quot;:&quot;ATT&quot;},&quot;ANAG2_ATTRIBUTI_FMU&quot;:{&quot;label&quot;:&quot;FMU&quot;,&quot;apiName&quot;:&quot;FMU&quot;},&quot;ANAG2_ATTRIBUTI_TER&quot;:{&quot;label&quot;:&quot;TER&quot;,&quot;apiName&quot;:&quot;TER&quot;},&quot;ANAG2_ATTRIBUTI_GSF&quot;:{&quot;label&quot;:&quot;GSF&quot;,&quot;apiName&quot;:&quot;GSF&quot;},&quot;ANAG2_ATTRIBUTI_RIS&quot;:{&quot;label&quot;:&quot;RIS&quot;,&quot;apiName&quot;:&quot;RIS&quot;},&quot;ANAG2_ATTRIBUTI_POL&quot;:{&quot;label&quot;:&quot;POL&quot;,&quot;apiName&quot;:&quot;POL&quot;},&quot;ANAG2_ATTRIBUTI_APP&quot;:{&quot;label&quot;:&quot;APP&quot;,&quot;apiName&quot;:&quot;APP&quot;},&quot;ANAG2_ATTRIBUTI_ACQ&quot;:{&quot;label&quot;:&quot;ACQ&quot;,&quot;apiName&quot;:&quot;ACQ&quot;},&quot;ANAG2_ATTRIBUTI_SOC&quot;:{&quot;label&quot;:&quot;SOC&quot;,&quot;apiName&quot;:&quot;SOC&quot;},&quot;ANAG2_ATTRIBUTI_LAV&quot;:{&quot;label&quot;:&quot;LAV&quot;,&quot;apiName&quot;:&quot;LAV&quot;},&quot;ANAG2_ATTRIBUTI_FOT&quot;:{&quot;label&quot;:&quot;FOT&quot;,&quot;apiName&quot;:&quot;FOT&quot;},&quot;ANAG2_ATTRIBUTI_VOL&quot;:{&quot;label&quot;:&quot;VOL&quot;,&quot;apiName&quot;:&quot;VOL&quot;},&quot;ANAG2_ATTRIBUTI_SEB&quot;:{&quot;label&quot;:&quot;SEB&quot;,&quot;apiName&quot;:&quot;SEB&quot;},&quot;ANAG2_ATTRIBUTI_VIA&quot;:{&quot;label&quot;:&quot;VIA&quot;,&quot;apiName&quot;:&quot;VIA&quot;},&quot;ANAG2_ATTRIBUTI_FDT&quot;:{&quot;label&quot;:&quot;FDT&quot;,&quot;apiName&quot;:&quot;FDT&quot;},&quot;ANAG2_FLAGMUTUO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGSECONDACASA&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAAFFITTO&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAPROPRIETA&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGANIMALIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot;:{&quot;label&quot;:6,&quot;apiName&quot;:6},&quot;ANAG2_SPESECOMPRIMIBILI&quot;:{&quot;label&quot;:60050,&quot;apiName&quot;:60050},&quot;ANAG2_SPESEINCOMPRIMIBILI&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_REDDITOLAVORO&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_FLAGCONIUGECARICO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_TIPORISPARMIO&quot;:{&quot;label&quot;:&quot;fino a 5.000 annui&quot;,&quot;apiName&quot;:1},&quot;ANAG2_TITOLODISTUDIO&quot;:{&quot;label&quot;:&quot;Scuola d&apos;obbligo&quot;,&quot;apiName&quot;:&quot;O&quot;},&quot;ANAG2_FLAGFIGLI&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_STATOCIVILE&quot;:{&quot;label&quot;:&quot;CONIUGATO/CONVIVENTE&quot;,&quot;apiName&quot;:&quot;C&quot;},&quot;ciu&quot;:7167158,&quot;society_account_account_relation_id&quot;:&quot;a009X00000YyGkiQAF&quot;}},&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;Custom\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;body\&quot;:\&quot;{\\n\\\&quot;Params.c__editMode\\\&quot; : true\\n}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Params.c__accountId\&quot;,\&quot;val\&quot;:\&quot;0019X000016F3tyQAC\&quot;,\&quot;id\&quot;:10}]}&quot;,&quot;eventName&quot;:&quot;reload&quot;},&quot;key&quot;:&quot;*************-0t60zkfmt&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconOnly&quot;:false,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;iconSize&quot;:&quot;small&quot;,&quot;flyoutDetails&quot;:{},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-8&quot;,&quot;field&quot;:&quot;User.userProfileName&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Contact Center&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;datasourceKey&quot;:&quot;state0element0block_element1&quot;}],&quot;elementLabel&quot;:&quot;Block-0-clone-0&quot;,&quot;userUpdatedElementLabel&quot;:true,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1757239693647-924&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Dati generici&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Stato civile&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_STATOCIVILE.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-47&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_STATOCIVILE.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;C&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:true},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Coniuge Carico&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.label&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Block-1-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_1_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element1block_element0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data Nascita Coniuge&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATANASCITACONIUGE.label&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;format&quot;:&quot;DD/MM/YYYY&quot;,&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Block-1-Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_1_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element1block_element1&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Block-1&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element1&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Figli&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGFIGLI.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Field-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Figli a carico&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-38&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_FLAGFIGLI.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;number&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Numero figli carico&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName&quot;,&quot;mask&quot;:&quot;&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data Nascita Figlio 1&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1&quot;,&quot;mask&quot;:&quot;#,##0.###&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;format&quot;:&quot;DD/MM/YYYY&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element1&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data Nascita Figlio 2&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2&quot;,&quot;mask&quot;:&quot;#,##0.###&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;format&quot;:&quot;DD/MM/YYYY&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element2&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data Nascita Figlio 3&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3&quot;,&quot;mask&quot;:&quot;#,##0.###&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;format&quot;:&quot;DD/MM/YYYY&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element3&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Data Nascita Figlio 4&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4&quot;,&quot;mask&quot;:&quot;#,##0.###&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;format&quot;:&quot;DD/MM/YYYY&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element4&quot;},{&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_5_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;number&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Figli Minorenni&quot;,&quot;mask&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.underaged_children_number&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-5&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element5&quot;},{&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_6_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;number&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Figli Maggiorenni&quot;,&quot;mask&quot;:&quot;&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATENASCITAFIGLI.overaged_children_number&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-5-Field-5-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element3block_element6&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Block-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element3&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_4_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;number&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Numero componenti nucleo familiare&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName&quot;,&quot;mask&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Field-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element4&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_5_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Collaboratori Domestici&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Field-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element5&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Titolo di studio&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_TITOLODISTUDIO.label&quot;,&quot;fieldLevelHelp&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;datasourceKey&quot;:&quot;state0element1block_element6&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_7_0&quot;,&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Animali Domestici&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGANIMALIDOMESTICI.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Field-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state0element1block_element7&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1757239693647-281&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Casa&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Proprietà&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGCASAPROPRIETA.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-4-clone-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Seconda/e casa/e&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGSECONDACASA.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Field-5-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element1&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Affitto&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGCASAAFFITTO.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-clone-0-Field-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element2&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Mutuo in corso&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_FLAGMUTUO.label&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-clone-0-Field-2-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element2block_element3&quot;}],&quot;elementLabel&quot;:&quot;Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1757239693647-520&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Reddito e Spese&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Reddito Netto Da Lavoro Annuo&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_REDDITOLAVORO.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Field-3-clone-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Spese Comprimibili Annue&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_SPESECOMPRIMIBILI.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-4-Field-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element1&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Spese Incomprimibili Annue&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_SPESEINCOMPRIMIBILI.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-4-Field-1-clone-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element2&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Evoluzione Reddito Netto Da Lavoro&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-4-Field-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element3&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Evoluzione Attesa Del Reddito Da Altre Fonti&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-4-Field-2-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_3_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;datasourceKey&quot;:&quot;state0element3block_element4&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1757239693647-825&quot;,&quot;datasourceKey&quot;:&quot;state0element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Altro&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Utilizzo Internet&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-4-Field-4-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_4_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element0&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Interessi&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.interessi.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Field-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_4_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element1&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Che risparmiatore sono&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_TIPORISPARMIO.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Field-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_4_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element2&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;Vorrei saperne di più come&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Field-2-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_4_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element3&quot;},{&quot;name&quot;:&quot;Field&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;placeholder&quot;:&quot;-&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;label&quot;:&quot;A breve Io prevedo&quot;,&quot;fieldName&quot;:&quot;accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.label&quot;,&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Field-3-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_4_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;datasourceKey&quot;:&quot;state0element4block_element4&quot;}],&quot;elementLabel&quot;:&quot;Block-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1757239693647-557&quot;,&quot;datasourceKey&quot;:&quot;state0element4&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]},{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Params.c__editMode&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-17&quot;,&quot;field&quot;:&quot;Params.c__editMode&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]},&quot;name&quot;:&quot;Dettagli Socio Economici - Modifica&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;no-repeat&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;  background-repeat:no-repeat;    \n         &quot;},&quot;blankCardState&quot;:false,&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%20class=%22slds-text-heading_medium%22%3EDati%20Anagrafici%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;,&quot;userUpdatedElementLabel&quot;:true,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_1_outputField_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_1&quot;,&quot;datasourceKey&quot;:&quot;state1element0block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_0_1_action_1_1&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Annulla&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1746719352015&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;Custom\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;body\&quot;:\&quot;{\\n\\\&quot;Params.c__editMode\\\&quot; : false\\n}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Params.c__accountId\&quot;,\&quot;val\&quot;:\&quot;0019X000016F3tyQAC\&quot;,\&quot;id\&quot;:10}]}&quot;,&quot;eventName&quot;:&quot;reload&quot;},&quot;key&quot;:&quot;*************-0t60zkfmt&quot;,&quot;label&quot;:&quot;Turn off Edit Mode&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconOnly&quot;:false,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;iconSize&quot;:&quot;small&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_1&quot;,&quot;elementLabel&quot;:&quot;Block-0-clone-0-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element0block_element1&quot;},{&quot;key&quot;:&quot;element_element_block_0_1_action_2_1&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Salva&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748273689622-3e8ie70k1&quot;,&quot;label&quot;:&quot;Turn off Edit Mode&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748274043592&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;Custom\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;body\&quot;:\&quot;{\\n\\\&quot;Params.c__editMode\\\&quot; : false\\n}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0},{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;Anag_UpsertDataEconomy\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ANAG2_STATOCIVILE\&quot;:\&quot;{accountKPI.ANAG2_STATOCIVILE.apiName}\&quot;,\&quot;ANAG2_FLAGCONIUGECARICO\&quot;:\&quot;{accountKPI.ANAG2_FLAGCONIUGECARICO.apiName}\&quot;,\&quot;ANAG2_DATANASCITACONIUGE\&quot;:\&quot;{accountKPI.ANAG2_DATANASCITACONIUGE.apiName}\&quot;,\&quot;ANAG2_FLAGFIGLI\&quot;:\&quot;{accountKPI.ANAG2_FLAGFIGLI.apiName}\&quot;,\&quot;ANAG2_NUMERONUCLEOFAMIGLIA\&quot;:\&quot;{accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName}\&quot;,\&quot;ANAG2_FLAGCOLLABORATORIDOMESTICI\&quot;:\&quot;{accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName}\&quot;,\&quot;ANAG2_TITOLODISTUDIO\&quot;:\&quot;{accountKPI.ANAG2_TITOLODISTUDIO.apiName}\&quot;,\&quot;ANAG2_FLAGANIMALIDOMESTICI\&quot;:\&quot;{accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName}\&quot;,\&quot;ANAG2_FLAGCASAPROPRIETA\&quot;:\&quot;{accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName}\&quot;,\&quot;ANAG2_FLAGSECONDACASA\&quot;:\&quot;{accountKPI.ANAG2_FLAGSECONDACASA.apiName}\&quot;,\&quot;ANAG2_FLAGCASAAFFITTO\&quot;:\&quot;{accountKPI.ANAG2_FLAGCASAAFFITTO.apiName}\&quot;,\&quot;ANAG2_FLAGMUTUO\&quot;:\&quot;{accountKPI.ANAG2_FLAGMUTUO.apiName}\&quot;,\&quot;ANAG2_REDDITOLAVORO\&quot;:\&quot;{accountKPI.ANAG2_REDDITOLAVORO.apiName}\&quot;,\&quot;ANAG2_SPESECOMPRIMIBILI\&quot;:\&quot;{accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName}\&quot;,\&quot;ANAG2_SPESEINCOMPRIMIBILI\&quot;:\&quot;{accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName}\&quot;,\&quot;ANAG2_EVOLUZIONEREDDITOLAVORO\&quot;:\&quot;{accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName}\&quot;,\&quot;ANAG2_EVOLUZIONEALTRIREDDITI\&quot;:\&quot;{accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName}\&quot;,\&quot;ANAG2_DATIATTRIBUTI_UTILIZZO_INTERNET\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName}\&quot;,\&quot;ANAG2_DATIATTRIBUTI_INTERESSI\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName}\&quot;,\&quot;ANAG2_TIPORISPARMIO\&quot;:\&quot;{accountKPI.ANAG2_TIPORISPARMIO.apiName}\&quot;,\&quot;ANAG2_DATIATTRIBUTI_VORREI_SAPERNE_DI_PIU\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName}\&quot;,\&quot;ANAG2_DATIATTRIBUTI_A_BREVE_PREVEDO\&quot;:\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName}\&quot;,\&quot;societyAccountAccountRelationId\&quot;:\&quot;{accountKPI.society_account_account_relation_id}\&quot;,\&quot;ANAG2_NUMEROFIGLIOCARICO\&quot;:\&quot;{accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName}\&quot;,\&quot;ANAG2_DATENASCITAFIGLIO_1\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1}\&quot;,\&quot;ANAG2_DATENASCITAFIGLIO_2\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2}\&quot;,\&quot;ANAG2_DATENASCITAFIGLIO_3\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3}\&quot;,\&quot;ANAG2_DATENASCITAFIGLIO_4\&quot;:\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4}\&quot;,\&quot;ciu\&quot;:\&quot;{accountKPI.ciu}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;accountKPI.ANAG2_STATOCIVILE.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_STATOCIVILE.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGCONIUGECARICO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATANASCITACONIUGE.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATANASCITACONIUGE.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGFIGLI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGFIGLI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_TITOLODISTUDIO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_TITOLODISTUDIO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGSECONDACASA.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGSECONDACASA.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGCASAAFFITTO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGCASAAFFITTO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_FLAGMUTUO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_FLAGMUTUO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_REDDITOLAVORO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_REDDITOLAVORO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_TIPORISPARMIO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_TIPORISPARMIO.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName}\\\&quot;,\\\&quot;accountKPI.society_account_account_relation_id\\\&quot;:\\\&quot;{accountKPI.society_account_account_relation_id}\\\&quot;,\\\&quot;accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName\\\&quot;:\\\&quot;{accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName}\\\&quot;,\\\&quot;accountKPI.ciu\\\&quot;:\\\&quot;{accountKPI.ciu}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3}\\\&quot;,\\\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4\\\&quot;:\\\&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_STATOCIVILE.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:58},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:59},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATANASCITACONIUGE.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:60},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGFIGLI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:61},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:62},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:63},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_TITOLODISTUDIO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:64},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:65},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:66},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGSECONDACASA.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:67},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGCASAAFFITTO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:68},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_FLAGMUTUO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:69},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_REDDITOLAVORO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:70},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:71},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:72},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:73},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:74},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:75},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:76},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_TIPORISPARMIO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:77},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:78},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:79},{\&quot;name\&quot;:\&quot;accountKPI.society_account_account_relation_id\&quot;,\&quot;val\&quot;:\&quot;a009X00000YyGkiQAF\&quot;,\&quot;id\&quot;:80},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:81},{\&quot;name\&quot;:\&quot;accountKPI.ciu\&quot;,\&quot;val\&quot;:\&quot;7167158\&quot;,\&quot;id\&quot;:82},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:58},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:59},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:60},{\&quot;name\&quot;:\&quot;accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:61}]}&quot;,&quot;eventName&quot;:&quot;reload&quot;},&quot;key&quot;:&quot;*************-0t60zkfmt&quot;,&quot;label&quot;:&quot;Upsert KPI&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-sv8sboshe&quot;,&quot;label&quot;:&quot;Get Updated KPI&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;Anag_GetDataEconomy\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;AccountId\&quot;:\&quot;{Params.c__accountId}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;Params.c__accountId\\\&quot;:\\\&quot;{Params.c__accountId}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;Params.c__accountId\&quot;,\&quot;val\&quot;:\&quot;0019X000013IoN5QAK\&quot;,\&quot;id\&quot;:3}]}&quot;},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;iconOnly&quot;:false,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;iconSize&quot;:&quot;small&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_1&quot;,&quot;elementLabel&quot;:&quot;Block-0-clone-0-Action-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;iconSize&quot;:&quot;small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element0block_element2&quot;}],&quot;elementLabel&quot;:&quot;Block-0-clone-0&quot;,&quot;userUpdatedElementLabel&quot;:true,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Dati generici&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_1_1_baseInputElement_0_1&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Stato civile&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_STATOCIVILE.apiName}&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;-&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;label&quot;:&quot;CONIUGATO/CONVIVENTE&quot;,&quot;value&quot;:&quot;C&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;label&quot;:&quot; LIBERO/SINGLE&quot;,&quot;value&quot;:&quot;S&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6}],&quot;value&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Select-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element1block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_1_1_block_1_1&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_STATOCIVILE.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;C&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Sì&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Coniuge Carico&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-1-Block-1-Radio-2&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGCONIUGECARICO.apiName}&quot;,&quot;value&quot;:&quot;&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;elementLabel&quot;:&quot;Block-1-Block-1-Radio-2&quot;,&quot;key&quot;:&quot;element_element_element_block_1_1_block_1_1_flexRadioInput_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_1_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element1block_element0&quot;},{&quot;key&quot;:&quot;element_element_element_block_1_1_block_1_1_baseInputElement_1_1&quot;,&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Data Nascita Coniuge&quot;,&quot;format&quot;:&quot;MM-DD-YYYY&quot;,&quot;outputFormat&quot;:&quot;MM-DD-YYYY&quot;,&quot;name&quot;:&quot;Block-1-Block-1-Date-1&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATANASCITACONIUGE.apiName}&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_FLAGCONIUGECARICO.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-1-Date-1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element1block_element1&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element1&quot;},{&quot;key&quot;:&quot;element_element_block_1_1_flexRadioInput_2_1&quot;,&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Figli&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-1-Radio-3&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGFIGLI.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Radio-2&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-14&quot;,&quot;field&quot;:&quot;accountKPI.ANAG2_FLAGFIGLI.apiName&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_1_block_3_1_baseInputElement_0_1&quot;,&quot;name&quot;:&quot;Range&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;range&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Numero figli a carico&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_NUMEROFIGLIOCARICO.apiName}&quot;,&quot;min&quot;:&quot;1&quot;,&quot;max&quot;:&quot;4&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-3-Range-0&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3block_element0&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Data Nascita Figlio 1&quot;,&quot;format&quot;:&quot;YYYY-MM-DD&quot;,&quot;outputFormat&quot;:&quot;YYYY-MM-DD&quot;,&quot;name&quot;:&quot;Block-1-Block-3-Date-1&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_1}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Date-1&quot;,&quot;key&quot;:&quot;element_element_element_block_1_1_block_3_1_baseInputElement_1_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3block_element1&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Data Nascita Figlio 2&quot;,&quot;format&quot;:&quot;YYYY-MM-DD&quot;,&quot;outputFormat&quot;:&quot;YYYY-MM-DD&quot;,&quot;name&quot;:&quot;Block-1-Block-3-Date-1&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_2}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Date-1-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_1_block_3_1_baseInputElement_2_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3block_element2&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Data Nascita Figlio 3&quot;,&quot;format&quot;:&quot;YYYY-MM-DD&quot;,&quot;outputFormat&quot;:&quot;YYYY-MM-DD&quot;,&quot;name&quot;:&quot;Block-1-Block-3-Date-1&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_3}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Date-2-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_1_block_3_1_baseInputElement_3_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3block_element3&quot;},{&quot;name&quot;:&quot;Date&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;date&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Data Nascita Figlio 4&quot;,&quot;format&quot;:&quot;YYYY-MM-DD&quot;,&quot;outputFormat&quot;:&quot;YYYY-MM-DD&quot;,&quot;name&quot;:&quot;Block-1-Block-3-Date-1&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATENASCITAFIGLI.apiName.child_4}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Date-3-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_1_block_3_1_baseInputElement_4_1&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_1_block_3_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3block_element4&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-3&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element3&quot;},{&quot;name&quot;:&quot;Range&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;range&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Numero componenti nucleo familiare&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_NUMERONUCLEOFAMIGLIA.apiName}&quot;,&quot;min&quot;:&quot;1&quot;,&quot;max&quot;:&quot;10&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Range-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_1_baseInputElement_4_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element4&quot;},{&quot;key&quot;:&quot;element_element_block_1_1_flexRadioInput_5_1&quot;,&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Collaboratori domestici&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-1-Radio-5&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGCOLLABORATORIDOMESTICI.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;elementLabel&quot;:&quot;Block-1-Radio-5&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element5&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Titolo di studio&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_TITOLODISTUDIO.apiName}&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;-&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;label&quot;:&quot;Scuola d&apos;obbligo&quot;,&quot;value&quot;:&quot;O&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;label&quot;:&quot;Diploma di scuola superiore&quot;,&quot;value&quot;:&quot;M&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;label&quot;:&quot;Laurea/Master&quot;,&quot;value&quot;:&quot;L&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6}],&quot;value&quot;:&quot;-&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Select-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_1_baseInputElement_6_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element6&quot;},{&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Animali domestici&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-1-Radio-8&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGANIMALIDOMESTICI.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;elementLabel&quot;:&quot;Block-1-Radio-8&quot;,&quot;key&quot;:&quot;element_element_block_1_1_flexRadioInput_7_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_1&quot;,&quot;datasourceKey&quot;:&quot;state1element1block_element7&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Casa&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_1_flexRadioInput_0_1&quot;,&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Proprietà&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-2-Radio-0&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGCASAPROPRIETA.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_1&quot;,&quot;elementLabel&quot;:&quot;Block-2-Radio-0&quot;,&quot;datasourceKey&quot;:&quot;state1element2block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_2_1_flexRadioInput_1_1&quot;,&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Seconda/e casa/e&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-2-Radio-3&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGSECONDACASA.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;parentElementKey&quot;:&quot;element_block_2_1&quot;,&quot;elementLabel&quot;:&quot;Block-2-Radio-3&quot;,&quot;datasourceKey&quot;:&quot;state1element2block_element1&quot;},{&quot;key&quot;:&quot;element_element_block_2_1_flexRadioInput_2_1&quot;,&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Affitto&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-2-Radio-4&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGCASAAFFITTO.apiName}&quot;,&quot;value&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;parentElementKey&quot;:&quot;element_block_2_1&quot;,&quot;elementLabel&quot;:&quot;Block-2-Radio-4&quot;,&quot;datasourceKey&quot;:&quot;state1element2block_element2&quot;},{&quot;name&quot;:&quot;Radio&quot;,&quot;element&quot;:&quot;flexRadioInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;radiogroup.horizontal&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;true&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;Si&quot;,&quot;selected&quot;:false,&quot;id&quot;:0},{&quot;value&quot;:&quot;false&quot;,&quot;imgId&quot;:&quot;&quot;,&quot;label&quot;:&quot;No&quot;,&quot;selected&quot;:false,&quot;id&quot;:1}],&quot;label&quot;:&quot;Mutuo in corso&quot;,&quot;radioDisplayValue&quot;:&quot;radiogroup.horizontal&quot;,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;imageCountInRow&quot;:&quot;3&quot;,&quot;enabledCaption&quot;:&quot;true&quot;,&quot;name&quot;:&quot;Block-2-Radio-5&quot;,&quot;value&quot;:&quot;&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_FLAGMUTUO.apiName}&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;elementLabel&quot;:&quot;Block-2-Radio-5&quot;,&quot;key&quot;:&quot;element_element_block_2_1_flexRadioInput_3_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_1&quot;,&quot;datasourceKey&quot;:&quot;state1element2block_element3&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Reddito e Spese&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_3_1_baseInputElement_0_1&quot;,&quot;name&quot;:&quot;Currency&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;,&quot;label&quot;:&quot;Reddito Netto Da Lavoro Annuo&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_REDDITOLAVORO.apiName}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Currency-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element3block_element0&quot;},{&quot;key&quot;:&quot;element_element_block_3_1_baseInputElement_1_1&quot;,&quot;name&quot;:&quot;Currency&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;,&quot;label&quot;:&quot;Spese Comprimibili Annue&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_SPESECOMPRIMIBILI.apiName}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Currency-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element3block_element1&quot;},{&quot;key&quot;:&quot;element_element_block_3_1_baseInputElement_2_1&quot;,&quot;name&quot;:&quot;Currency&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;currency&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;locale&quot;:&quot;it-IT&quot;,&quot;currency&quot;:&quot;EUR&quot;,&quot;label&quot;:&quot;Spese Incomprimibili Annue&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_SPESEINCOMPRIMIBILI.apiName}&quot;,&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Currency-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element3block_element2&quot;},{&quot;key&quot;:&quot;element_element_block_3_1_baseInputElement_3_1&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Evoluzione Reddito Netto Da Lavoro&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_EVOLUZIONEREDDITOLAVORO.apiName}&quot;,&quot;value&quot;:&quot;-&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;-&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;label&quot;:&quot;Stabile&quot;,&quot;value&quot;:&quot;0&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;label&quot;:&quot;Bassa&quot;,&quot;value&quot;:&quot;1&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;label&quot;:&quot;Media&quot;,&quot;value&quot;:&quot;2&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;label&quot;:&quot;Alta&quot;,&quot;value&quot;:&quot;3&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:10}],&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Select-5&quot;,&quot;datasourceKey&quot;:&quot;state1element3block_element3&quot;},{&quot;key&quot;:&quot;element_element_block_3_1_baseInputElement_4_1&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Evoluzione Attesa Del Reddito Netto Da Altre Fonti&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_EVOLUZIONEALTRIREDDITI.apiName}&quot;,&quot;value&quot;:&quot;-&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;-&quot;,&quot;value&quot;:&quot;-&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;label&quot;:&quot;Stabile&quot;,&quot;value&quot;:&quot;0&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;label&quot;:&quot;Bassa&quot;,&quot;value&quot;:&quot;1&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;label&quot;:&quot;Media&quot;,&quot;value&quot;:&quot;2&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;label&quot;:&quot;Alta&quot;,&quot;value&quot;:&quot;3&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:8}],&quot;placeholder&quot;:&quot;-&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_3_1&quot;,&quot;elementLabel&quot;:&quot;Block-3-Select-3-clone-0&quot;,&quot;datasourceKey&quot;:&quot;state1element3block_element4&quot;}],&quot;elementLabel&quot;:&quot;Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Altro&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Multi-Select&quot;,&quot;element&quot;:&quot;flexMultiSelectInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;pill&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Utilizzo Internet&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Per lavoro/informazioni&quot;,&quot;value&quot;:&quot;LAV&quot;,&quot;id&quot;:0,&quot;index&quot;:0},{&quot;label&quot;:&quot;Per accedere ai social&quot;,&quot;value&quot;:&quot;SOC&quot;,&quot;id&quot;:1,&quot;index&quot;:1},{&quot;label&quot;:&quot;Per fare acquisti&quot;,&quot;value&quot;:&quot;ACQ&quot;,&quot;id&quot;:2,&quot;index&quot;:2},{&quot;label&quot;:&quot;Per scaricare applicazioni&quot;,&quot;value&quot;:&quot;APP&quot;,&quot;id&quot;:3,&quot;index&quot;:3}],&quot;isImage&quot;:true,&quot;isImageDisplay&quot;:true,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.utilizzo_internet.apiName}&quot;,&quot;value&quot;:&quot;&quot;,&quot;displayMode&quot;:&quot;Combobox&quot;,&quot;alignment&quot;:&quot;horizontal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Multi-Select-4&quot;,&quot;key&quot;:&quot;element_element_block_4_1_flexMultiSelectInput_0_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4block_element0&quot;},{&quot;name&quot;:&quot;Multi-Select&quot;,&quot;element&quot;:&quot;flexMultiSelectInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;pill&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Interessi&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Fai da te&quot;,&quot;value&quot;:&quot;FDT&quot;,&quot;id&quot;:0,&quot;index&quot;:0},{&quot;label&quot;:&quot;Informatica, tecnologia&quot;,&quot;value&quot;:&quot;ITT&quot;,&quot;id&quot;:1,&quot;index&quot;:1},{&quot;label&quot;:&quot;Salute e benessere&quot;,&quot;value&quot;:&quot;SEB&quot;,&quot;id&quot;:2,&quot;index&quot;:2},{&quot;label&quot;:&quot;Fotografia&quot;,&quot;value&quot;:&quot;FOT&quot;,&quot;id&quot;:3,&quot;index&quot;:3},{&quot;label&quot;:&quot;Musica, cinema, teatro&quot;,&quot;value&quot;:&quot;MCT&quot;,&quot;id&quot;:4,&quot;index&quot;:4},{&quot;label&quot;:&quot;Sport&quot;,&quot;value&quot;:&quot;SPO&quot;,&quot;id&quot;:5,&quot;index&quot;:5},{&quot;label&quot;:&quot;Natura, ambiente&quot;,&quot;value&quot;:&quot;NAT&quot;,&quot;id&quot;:6,&quot;index&quot;:6},{&quot;label&quot;:&quot;Motori&quot;,&quot;value&quot;:&quot;MOT&quot;,&quot;id&quot;:7,&quot;index&quot;:7},{&quot;label&quot;:&quot;Lettura, musei&quot;,&quot;value&quot;:&quot;LET&quot;,&quot;id&quot;:8,&quot;index&quot;:8},{&quot;label&quot;:&quot;Viaggi&quot;,&quot;value&quot;:&quot;VIA&quot;,&quot;id&quot;:9,&quot;index&quot;:9},{&quot;label&quot;:&quot;Volontariato&quot;,&quot;value&quot;:&quot;VOL&quot;,&quot;id&quot;:10,&quot;index&quot;:10},{&quot;label&quot;:&quot;Enogastronomia&quot;,&quot;value&quot;:&quot;ENO&quot;,&quot;id&quot;:11,&quot;index&quot;:11}],&quot;isImage&quot;:true,&quot;isImageDisplay&quot;:true,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.interessi.apiName}&quot;,&quot;value&quot;:&quot;&quot;,&quot;displayMode&quot;:&quot;Combobox&quot;,&quot;alignment&quot;:&quot;horizontal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Multi-Select-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_4_1_flexMultiSelectInput_1_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4block_element1&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_TIPORISPARMIO.apiName}&quot;,&quot;label&quot;:&quot;Che risparmiatore sono&quot;,&quot;placeholder&quot;:&quot;-&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;fino a 5.000 annui&quot;,&quot;value&quot;:&quot;1&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;da 5.000 a 15.000 annui&quot;,&quot;value&quot;:&quot;2&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;label&quot;:&quot;oltre 15.000 annui&quot;,&quot;value&quot;:&quot;3&quot;,&quot;group&quot;:&quot;&quot;,&quot;id&quot;:2}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Select-5&quot;,&quot;key&quot;:&quot;element_element_block_4_1_baseInputElement_2_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4block_element2&quot;},{&quot;name&quot;:&quot;Multi-Select&quot;,&quot;element&quot;:&quot;flexMultiSelectInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;pill&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Vorrei saperne di più come&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Risparmiare sulla polizza auto&quot;,&quot;value&quot;:&quot;POL&quot;,&quot;id&quot;:0,&quot;index&quot;:0},{&quot;label&quot;:&quot;Garantire gli studi dei figli&quot;,&quot;value&quot;:&quot;GSF&quot;,&quot;id&quot;:1,&quot;index&quot;:1},{&quot;label&quot;:&quot;Avere un&apos;assistenza sanitaria privata&quot;,&quot;value&quot;:&quot;SAN&quot;,&quot;id&quot;:2,&quot;index&quot;:2},{&quot;label&quot;:&quot;Avere un indennizzo se mi ammalo o mi faccio male&quot;,&quot;value&quot;:&quot;IND&quot;,&quot;id&quot;:3,&quot;index&quot;:3},{&quot;label&quot;:&quot;Integrare la mia pensione&quot;,&quot;value&quot;:&quot;PEN&quot;,&quot;id&quot;:4,&quot;index&quot;:4},{&quot;label&quot;:&quot;Far rendere il mio capitale&quot;,&quot;value&quot;:&quot;REN&quot;,&quot;id&quot;:5,&quot;index&quot;:5},{&quot;label&quot;:&quot;Assicurarmi contro il terremoto&quot;,&quot;value&quot;:&quot;TER&quot;,&quot;id&quot;:6,&quot;index&quot;:6},{&quot;label&quot;:&quot;Cautelarmi se perdo l&apos;impiego&quot;,&quot;value&quot;:&quot;PER&quot;,&quot;id&quot;:7,&quot;index&quot;:7},{&quot;label&quot;:&quot;Risparmiare senza rischi&quot;,&quot;value&quot;:&quot;RIS&quot;,&quot;id&quot;:8,&quot;index&quot;:8},{&quot;label&quot;:&quot;Lasciare un&apos;eredità&quot;,&quot;value&quot;:&quot;ERE&quot;,&quot;id&quot;:9,&quot;index&quot;:9},{&quot;label&quot;:&quot;Difendermi da richieste di risarcimento danni (casa-lavoro)&quot;,&quot;value&quot;:&quot;RSC&quot;,&quot;id&quot;:10,&quot;index&quot;:10}],&quot;isImage&quot;:true,&quot;isImageDisplay&quot;:true,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.vorrei_saperne_di_piu.apiName}&quot;,&quot;value&quot;:&quot;&quot;,&quot;displayMode&quot;:&quot;Combobox&quot;,&quot;alignment&quot;:&quot;horizontal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Multi-Select-2-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_4_1_flexMultiSelectInput_3_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4block_element3&quot;},{&quot;name&quot;:&quot;Multi-Select&quot;,&quot;element&quot;:&quot;flexMultiSelectInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:1,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;pill&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;A breve Io prevedo&quot;,&quot;options&quot;:[{&quot;label&quot;:&quot;Acquistare/cambiare auto&quot;,&quot;value&quot;:&quot;AUT&quot;,&quot;id&quot;:0,&quot;index&quot;:0},{&quot;label&quot;:&quot;Finire di pagare il mutuo&quot;,&quot;value&quot;:&quot;FMU&quot;,&quot;id&quot;:1,&quot;index&quot;:1},{&quot;label&quot;:&quot;Avere una promozione sul lavoro&quot;,&quot;value&quot;:&quot;PRO&quot;,&quot;id&quot;:2,&quot;index&quot;:2},{&quot;label&quot;:&quot;Terminare studio figli&quot;,&quot;value&quot;:&quot;TSF&quot;,&quot;id&quot;:3,&quot;index&quot;:3},{&quot;label&quot;:&quot;Acquistare/cambiare/ristrutturare abitazione&quot;,&quot;value&quot;:&quot;ABI&quot;,&quot;id&quot;:4,&quot;index&quot;:4},{&quot;label&quot;:&quot;Intraprendere una nuova attività&quot;,&quot;value&quot;:&quot;ATT&quot;,&quot;id&quot;:5,&quot;index&quot;:5},{&quot;label&quot;:&quot;Fare un viaggio/vacanza importante&quot;,&quot;value&quot;:&quot;VAC&quot;,&quot;id&quot;:6,&quot;index&quot;:6},{&quot;label&quot;:&quot;Godermi la pensione&quot;,&quot;value&quot;:&quot;GOD&quot;,&quot;id&quot;:7,&quot;index&quot;:7}],&quot;isImage&quot;:true,&quot;isImageDisplay&quot;:true,&quot;controlWidth&quot;:&quot;100&quot;,&quot;controlHeight&quot;:&quot;100&quot;,&quot;fieldBinding&quot;:&quot;{accountKPI.ANAG2_DATIATTRIBUTI.a_breve_prevedo.apiName}&quot;,&quot;value&quot;:&quot;&quot;,&quot;displayMode&quot;:&quot;Combobox&quot;,&quot;alignment&quot;:&quot;horizontal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-5-Multi-Select-3-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_4_1_flexMultiSelectInput_4_1&quot;,&quot;parentElementKey&quot;:&quot;element_block_4_1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;right:small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4block_element4&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;datasourceKey&quot;:&quot;state1element4&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Anag_GetDataEconomy&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{Params.c__accountId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Params.c__accountId\&quot;:\&quot;{Params.c__accountId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Params.c__accountId&quot;,&quot;val&quot;:&quot;0019X00001CTbpdQAD&quot;,&quot;id&quot;:10}]},&quot;title&quot;:&quot;SS_DettagliSocioEconomiciChild&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSS_DettagliSocioEconomiciChild_3_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9V000003fUV3SAM&quot;,&quot;MasterLabel&quot;:&quot;cfSS_DettagliSocioEconomiciChild_3_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;apiVersion&quot;:56,&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPjxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiI+PC9wcm9wZXJ0eT48cHJvcGVydHkgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz48dGFyZ2V0Q29uZmlnIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+PHByb3BlcnR5IG5hbWU9ImRlYnVnIiB0eXBlPSJCb29sZWFuIj48L3Byb3BlcnR5Pjxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIj48L3Byb3BlcnR5PjwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;isExplicitImport&quot;:false,&quot;isExposed&quot;:true,&quot;id&quot;:&quot;0Rb9Q000002Pw9JSAS&quot;,&quot;masterLabel&quot;:&quot;SS_DettagliSocioEconomiciChild&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]}},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;sessionVars&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;isAbbinato&quot;:false,&quot;accountKPI&quot;:{&quot;ANAG2_DATIATTRIBUTI&quot;:{&quot;a_breve_prevedo&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;vorrei_saperne_di_piu&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;utilizzo_internet&quot;:{&quot;label&quot;:&quot;Per accedere ai social; Per lavoro/informazioni; Per fare acquisti&quot;,&quot;apiName&quot;:&quot;SOC;LAV;ACQ&quot;},&quot;interessi&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:&quot;&quot;},&quot;INT&quot;:{&quot;MCT&quot;:false,&quot;VIA&quot;:false,&quot;FOT&quot;:false,&quot;LET&quot;:false,&quot;ITT&quot;:false,&quot;FDT&quot;:false,&quot;ENO&quot;:false,&quot;SPO&quot;:false,&quot;MOT&quot;:false,&quot;VOL&quot;:false,&quot;SEB&quot;:false,&quot;NAT&quot;:false},&quot;NET&quot;:{&quot;ACQ&quot;:true,&quot;SOC&quot;:true,&quot;APP&quot;:false,&quot;LAV&quot;:true},&quot;PRE&quot;:{&quot;FMU&quot;:false,&quot;GOD&quot;:false,&quot;PRO&quot;:false,&quot;VAC&quot;:false,&quot;ABI&quot;:false,&quot;TSF&quot;:false,&quot;ATT&quot;:false,&quot;AUT&quot;:false},&quot;COM&quot;:{&quot;IND&quot;:false,&quot;RIS&quot;:false,&quot;POL&quot;:false,&quot;PER&quot;:false,&quot;GSF&quot;:false,&quot;REN&quot;:false,&quot;ERE&quot;:false,&quot;PEN&quot;:false,&quot;RSC&quot;:false,&quot;SAN&quot;:false,&quot;TER&quot;:false},&quot;label&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;,&quot;apiName&quot;:&quot;{\&quot;COM\&quot;:{\&quot;TER\&quot;:false,\&quot;SAN\&quot;:false,\&quot;RSC\&quot;:false,\&quot;PEN\&quot;:false,\&quot;ERE\&quot;:false,\&quot;REN\&quot;:false,\&quot;GSF\&quot;:false,\&quot;PER\&quot;:false,\&quot;POL\&quot;:false,\&quot;RIS\&quot;:false,\&quot;IND\&quot;:false},\&quot;PRE\&quot;:{\&quot;AUT\&quot;:false,\&quot;ATT\&quot;:false,\&quot;TSF\&quot;:false,\&quot;ABI\&quot;:false,\&quot;VAC\&quot;:false,\&quot;PRO\&quot;:false,\&quot;GOD\&quot;:false,\&quot;FMU\&quot;:false},\&quot;NET\&quot;:{\&quot;LAV\&quot;:true,\&quot;APP\&quot;:false,\&quot;SOC\&quot;:true,\&quot;ACQ\&quot;:true},\&quot;INT\&quot;:{\&quot;NAT\&quot;:false,\&quot;SEB\&quot;:false,\&quot;VOL\&quot;:false,\&quot;MOT\&quot;:false,\&quot;SPO\&quot;:false,\&quot;ENO\&quot;:false,\&quot;FDT\&quot;:false,\&quot;ITT\&quot;:false,\&quot;LET\&quot;:false,\&quot;FOT\&quot;:false,\&quot;VIA\&quot;:false,\&quot;MCT\&quot;:false}}&quot;},&quot;ANAG2_NUMEROFIGLIOCARICO&quot;:{&quot;label&quot;:&quot;&quot;,&quot;apiName&quot;:2},&quot;ANAG2_DATENASCITAFIGLI&quot;:{&quot;underaged_children_number&quot;:0,&quot;overaged_children_number&quot;:2,&quot;label&quot;:&quot;[\&quot;2007-06-10\&quot;,\&quot;1997-06-12\&quot;]&quot;,&quot;apiName&quot;:{&quot;child_1&quot;:&quot;2007-06-10&quot;,&quot;child_2&quot;:&quot;1997-06-12&quot;,&quot;child_3&quot;:null,&quot;child_4&quot;:null}},&quot;ANAG2_ATTRIBUTI_TSF&quot;:{&quot;label&quot;:&quot;TSF&quot;,&quot;apiName&quot;:&quot;TSF&quot;},&quot;ANAG2_ATTRIBUTI_ATT&quot;:{&quot;label&quot;:&quot;ATT&quot;,&quot;apiName&quot;:&quot;ATT&quot;},&quot;ANAG2_ATTRIBUTI_FMU&quot;:{&quot;label&quot;:&quot;FMU&quot;,&quot;apiName&quot;:&quot;FMU&quot;},&quot;ANAG2_ATTRIBUTI_TER&quot;:{&quot;label&quot;:&quot;TER&quot;,&quot;apiName&quot;:&quot;TER&quot;},&quot;ANAG2_ATTRIBUTI_GSF&quot;:{&quot;label&quot;:&quot;GSF&quot;,&quot;apiName&quot;:&quot;GSF&quot;},&quot;ANAG2_ATTRIBUTI_RIS&quot;:{&quot;label&quot;:&quot;RIS&quot;,&quot;apiName&quot;:&quot;RIS&quot;},&quot;ANAG2_ATTRIBUTI_POL&quot;:{&quot;label&quot;:&quot;POL&quot;,&quot;apiName&quot;:&quot;POL&quot;},&quot;ANAG2_ATTRIBUTI_APP&quot;:{&quot;label&quot;:&quot;APP&quot;,&quot;apiName&quot;:&quot;APP&quot;},&quot;ANAG2_ATTRIBUTI_ACQ&quot;:{&quot;label&quot;:&quot;ACQ&quot;,&quot;apiName&quot;:&quot;ACQ&quot;},&quot;ANAG2_ATTRIBUTI_SOC&quot;:{&quot;label&quot;:&quot;SOC&quot;,&quot;apiName&quot;:&quot;SOC&quot;},&quot;ANAG2_ATTRIBUTI_LAV&quot;:{&quot;label&quot;:&quot;LAV&quot;,&quot;apiName&quot;:&quot;LAV&quot;},&quot;ANAG2_ATTRIBUTI_FOT&quot;:{&quot;label&quot;:&quot;FOT&quot;,&quot;apiName&quot;:&quot;FOT&quot;},&quot;ANAG2_ATTRIBUTI_VOL&quot;:{&quot;label&quot;:&quot;VOL&quot;,&quot;apiName&quot;:&quot;VOL&quot;},&quot;ANAG2_ATTRIBUTI_SEB&quot;:{&quot;label&quot;:&quot;SEB&quot;,&quot;apiName&quot;:&quot;SEB&quot;},&quot;ANAG2_ATTRIBUTI_VIA&quot;:{&quot;label&quot;:&quot;VIA&quot;,&quot;apiName&quot;:&quot;VIA&quot;},&quot;ANAG2_ATTRIBUTI_FDT&quot;:{&quot;label&quot;:&quot;FDT&quot;,&quot;apiName&quot;:&quot;FDT&quot;},&quot;ANAG2_FLAGMUTUO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGSECONDACASA&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAAFFITTO&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_FLAGCASAPROPRIETA&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGCOLLABORATORIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_FLAGANIMALIDOMESTICI&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_NUMERONUCLEOFAMIGLIA&quot;:{&quot;label&quot;:6,&quot;apiName&quot;:6},&quot;ANAG2_SPESECOMPRIMIBILI&quot;:{&quot;label&quot;:60050,&quot;apiName&quot;:60050},&quot;ANAG2_SPESEINCOMPRIMIBILI&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEALTRIREDDITI&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_EVOLUZIONEREDDITOLAVORO&quot;:{&quot;label&quot;:&quot;Stabile&quot;,&quot;apiName&quot;:0},&quot;ANAG2_REDDITOLAVORO&quot;:{&quot;label&quot;:0,&quot;apiName&quot;:0},&quot;ANAG2_FLAGCONIUGECARICO&quot;:{&quot;label&quot;:&quot;No&quot;,&quot;apiName&quot;:false},&quot;ANAG2_TIPORISPARMIO&quot;:{&quot;label&quot;:&quot;fino a 5.000 annui&quot;,&quot;apiName&quot;:1},&quot;ANAG2_TITOLODISTUDIO&quot;:{&quot;label&quot;:&quot;Scuola d&apos;obbligo&quot;,&quot;apiName&quot;:&quot;O&quot;},&quot;ANAG2_FLAGFIGLI&quot;:{&quot;label&quot;:&quot;Sì&quot;,&quot;apiName&quot;:true},&quot;ANAG2_STATOCIVILE&quot;:{&quot;label&quot;:&quot;CONIUGATO/CONVIVENTE&quot;,&quot;apiName&quot;:&quot;C&quot;},&quot;ciu&quot;:7167158,&quot;society_account_account_relation_id&quot;:&quot;a009X00000YyGkiQAF&quot;}}</sampleDataSourceResponse>
    <versionNumber>3</versionNumber>
</OmniUiCard>
