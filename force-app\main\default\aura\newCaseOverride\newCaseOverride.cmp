<aura:component
    controller="NewPermissionChecker"
    implements="lightning:actionOverride, lightning:hasPageReference, force:lightningQuickActionWithoutHeader, flexipage:availableForRecordHome, force:hasRecordId, force:hasSObjectName"
    access="global">

    <!-- ATTRIBUTI -->
    <aura:attribute name="isAccountCreationAllowed" type="Boolean" default="false"/>
    <aura:attribute name="showRTs" type="Boolean" default="false"/>
    <aura:attribute name="selectedRTId" type="String"/>
    <aura:attribute name="recordTypesMap" type="List"/>
    <aura:attribute name="RToptions" type="List"/>
    <aura:attribute name="showNewCaseOrAura" type="Boolean" default="false"/>

    <!-- INIT -->
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <!-- OVERLAY LIBRARY -->
    <lightning:overlayLibrary aura:id="overlayLib"/>

    <!-- MODALE RADIO BUTTON -->
    <aura:if isTrue="{!v.showRTs}">
        <div class="customCardContainer" style="padding: 15px; max-width: 600px; margin: auto;">
            <lightning:card>
                <div class="slds-p-around_medium">
                    <!-- Titolo personalizzato -->
                    <h2 class="slds-text-heading_large slds-m-bottom_medium">Nuovo Case</h2>

                    <!-- Radio Group con padding -->
                    <div class="slds-box slds-theme_default slds-p-around_small">
                        <lightning:radioGroup name="radioGroup"
                            aura:id="radioGroup"
                            label="Seleziona Tipologia"
                            options="{! v.RToptions }"
                            required="true"
                            value="{! v.selectedRTId }"
                            type="radio"/>
                    </div>
                </div>

                <!-- Pulsanti -->
                <div class="slds-p-horizontal_small slds-p-bottom_small slds-text-align_right">
                    <lightning:button variant="neutral" label="Annulla" onclick="{! c.closeAction }"/>
                    <lightning:button variant="brand" label="Nuovo" onclick="{! c.handleNew }" class="slds-m-left_small"/>
                </div>
            </lightning:card>
        </div>
    </aura:if>

    <aura:if isTrue="{!v.showNewCaseOrAura}">
        <c:newCaseOrAura />
    </aura:if>

</aura:component>
