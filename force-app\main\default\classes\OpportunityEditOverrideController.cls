public with sharing class OpportunityEditOverrideController {
    
    public class EditResponse {
        @AuraEnabled public Boolean isEditAllowed { get; set; }
        @AuraEnabled public String message { get; set; }
        
        public EditResponse() {
            this.isEditAllowed = false;
            this.message = '';
        }
    }
    
    @AuraEnabled
    public static EditResponse checkEditAccess(Id opportunityId) {
        EditResponse response = new EditResponse();
        
        try {
            // Verifica i permessi di base dell'utente
            if (!Schema.sObjectType.Opportunity.isUpdateable() || 
                !opportunityId.getSObjectType().getDescribe().isAccessible()) {
                response.message = 'Non hai i permessi necessari per modificare le opportunità';
                return response;
            }
            
            // Query dell'opportunità con il suo Record Type
            Opportunity opp = [SELECT Id, RecordTypeId, RecordType.DeveloperName,
                             Name, IsClosed, IsWon 
                             FROM Opportunity 
                             WHERE Id = :opportunityId];
            
            // Verifica se l'opportunità è chiusa
            if(opp.IsClosed) {
                response.message = 'Non è possibile modificare un\'opportunità chiusa';
                return response;
            }
            
            // Verifica il Record Type
            if(opp.RecordType.DeveloperName == 'Prodotto' || opp.RecordType.DeveloperName == 'Omnicanale') {
                response.isEditAllowed = true;
            } else {
                response.isEditAllowed = false;
                response.message = 'La modifica è consentita solo per le opportunità di tipo Prodotto o Omnicanale. ' +
                                 'Visualizza i dettagli dell\'opportunità ' + opp.Name;
            }
            
        } catch(Exception e) {
            response.message = 'Si è verificato un errore durante la verifica dei permessi: ' + e.getMessage();
            // Logging dell'errore
            System.debug(LoggingLevel.ERROR, 'Errore in checkEditAccess: ' + e.getMessage());
            System.debug(LoggingLevel.ERROR, 'Stack trace: ' + e.getStackTraceString());
        }
        
        return response;
    }
}
