public with sharing class FeiHelper {
    /** 
     * Recupera il Permission Set name da FEI_Settings__mdt
     */
@AuraEnabled(cacheable=true)
public static String getPermissionSetName() {
    FEI_Environment__c env = FEI_Environment__c.getInstance();
    // Query in lista per gestire il caso no‐rows
    List<FEI_Settings__mdt> settList = [
        SELECT UCA_Permission_Name__c
        FROM FEI_Settings__mdt
        WHERE Label = 'DOWNLOAD.DOCUMENTO'
          AND Environment__c = :env.Environment__c
        LIMIT 1
    ];
    // Se non trova nulla ritorna null
    return settList.isEmpty() ? null : settList[0].UCA_Permission_Name__c;
}


    /**
     * Ritorna il FederationIdentifier dell’utente corrente
     */
    @AuraEnabled(cacheable=true)
    public static String getFederationIdentifier() {
        List<User> users = [
            SELECT FederationIdentifier
            FROM User
            WHERE Id = :UserInfo.getUserId()
            LIMIT 1
        ];
        return users.isEmpty() 
            ? null 
            : users[0].FederationIdentifier;
    }

    /**
     * Dato un federationIdentifier e un codice agenzia,
     * restituisce i due Id di Account:
     *  - customerId = Account legato al Contact dell’User
     *  - agencyId   = Account con quel AgencyCode__c
     */
    @AuraEnabled(cacheable=true)
    public static Map<String,Id> getCustomerAndAgencyIds(String federationIdentifier, String agencyCode) {
        Map<String,Id> result = new Map<String,Id>{
            'customerId' => null,
            'agencyId'   => null
        };

        // 1) Trova l’User e ne prendi il Contact.AccountId
        List<User> users = [
            SELECT Contact.AccountId
            FROM User
            WHERE FederationIdentifier = :federationIdentifier
            LIMIT 1
        ];
        if (!users.isEmpty() && users[0].Contact != null) {
            result.put('customerId', users[0].Contact.AccountId);
        }

        // 2) Trova l’Account Agency in base al codice
        List<Account> agencies = [
            SELECT Id
            FROM Account
            WHERE AgencyCode__c = :agencyCode
              AND RecordType.DeveloperName = 'Agency'
            LIMIT 1
        ];
        if (!agencies.isEmpty()) {
            result.put('agencyId', agencies[0].Id);
        }

        return result;
    }

    /**
     * Dati un customerId e un agencyId, prova a recuperare
     * la relazione FinServ__AccountAccountRelation__c di tipo 'AccountAgency'.
     * Se la trova la restituisce, altrimenti null.
     */
    @AuraEnabled(cacheable=true)
    public static FinServ__AccountAccountRelation__c getAccountRelation(Id customerId, Id agencyId) {
        List<FinServ__AccountAccountRelation__c> rels = [
            SELECT FinServ__Account__c, FinServ__RelatedAccount__c
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c        = :customerId
              AND FinServ__RelatedAccount__c = :agencyId
              AND RecordType.DeveloperName   = 'AccountAgency'
            LIMIT 1
        ];
        return rels.isEmpty() ? null : rels[0];
    }

     @AuraEnabled(cacheable=true)
    public static String getAgencyCode(String quoteId) {
      Quote quo = [SELECT Id, OpportunityId from Quote where Id =: quoteId LIMIT 1];
      Opportunity opp = [SELECT Id, Agency__r.AgencyCode__c from Opportunity where Id =: quo.OpportunityId LIMIT 1];
      String result = opp.Agency__r.AgencyCode__c;
      return result;
    }
}