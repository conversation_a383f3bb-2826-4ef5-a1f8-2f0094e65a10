<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;actionType&quot;:&quot;getInfoTerritoriali&quot;,&quot;codProv&quot;:&quot;{Parent.codProv}&quot;,&quot;codStato&quot;:&quot;{Parent.codStato}&quot;,&quot;codComune&quot;:&quot;{Parent.codComune}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.codProv\&quot;:\&quot;{Parent.codProv}\&quot;,\&quot;Parent.codStato\&quot;:\&quot;{Parent.codStato}\&quot;,\&quot;Parent.codComune\&quot;:\&quot;{Parent.codComune}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.codProv&quot;,&quot;val&quot;:&quot;RC&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.codStato&quot;,&quot;val&quot;:&quot;Z000&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;Parent.codComune&quot;,&quot;val&quot;:&quot;H224&quot;,&quot;id&quot;:6}]},&quot;state0element1_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;actionType&quot;:&quot;getComuni&quot;,&quot;provinciaRO&quot;:&quot;{attributes.provincia.isRO}&quot;,&quot;provinciaREQ&quot;:&quot;{attributes.provincia.isRequired}&quot;,&quot;statoRO&quot;:&quot;{attributes.stato.isRO}&quot;,&quot;statoREQ&quot;:&quot;{attributes.stato.isRequired}&quot;,&quot;comuneRO&quot;:&quot;{attributes.comune.isRO}&quot;,&quot;comuneREQ&quot;:&quot;{attributes.comune.isRequired}&quot;,&quot;provincia&quot;:&quot;{values.provincia}&quot;,&quot;comune&quot;:&quot;{values.comune}&quot;,&quot;stato&quot;:&quot;{values.stato}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;values.provincia\&quot;:\&quot;{values.provincia}\&quot;,\&quot;attributes.provincia.isRO\&quot;:\&quot;{attributes.provincia.isRO}\&quot;,\&quot;attributes.provincia.isRequired\&quot;:\&quot;{attributes.provincia.isRequired}\&quot;,\&quot;attributes.comune.isRO\&quot;:\&quot;{attributes.comune.isRO}\&quot;,\&quot;attributes.comune.isRequired\&quot;:\&quot;{attributes.comune.isRequired}\&quot;,\&quot;attributes.stato.isRequired\&quot;:\&quot;{attributes.stato.isRequired}\&quot;,\&quot;attributes.stato.isRO\&quot;:\&quot;{attributes.stato.isRO}\&quot;,\&quot;values.comune\&quot;:\&quot;{values.comune}\&quot;,\&quot;values.stato\&quot;:\&quot;{values.stato}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;values.provincia&quot;,&quot;val&quot;:&quot;RC&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;attributes.provincia.isRO&quot;,&quot;val&quot;:&quot;false&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;attributes.provincia.isRequired&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;attributes.comune.isRO&quot;,&quot;val&quot;:&quot;false&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;attributes.comune.isRequired&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;attributes.stato.isRequired&quot;,&quot;val&quot;:&quot;false&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;attributes.stato.isRO&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:13},{&quot;name&quot;:&quot;values.comune&quot;,&quot;val&quot;:&quot;H224&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;values.stato&quot;,&quot;val&quot;:&quot;Z000&quot;,&quot;id&quot;:18}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDoc_UnitaTerrBlock</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Stato&quot;,&quot;fieldBinding&quot;:&quot;{values.stato}&quot;,&quot;value&quot;:&quot;{Parent.codStato}&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{options.optionsStati}&quot;},{&quot;id&quot;:1,&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{attributes.stato.isRequired}&quot;},{&quot;id&quot;:2,&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{attributes.stato.isRO}&quot;},{&quot;id&quot;:3,&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756975488676-cq5nc95b2&quot;,&quot;label&quot;:&quot;SetROREQtrue&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756976142218&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;attributes.provincia.isRO&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.provincia.isRequired&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRO&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRequired&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1756975679890-e8o8pn0ai&quot;,&quot;label&quot;:&quot;SetROREQFalse&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756976421760&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;attributes.provincia.isRO&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.provincia.isRequired&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRO&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRequired&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;values.provincia&quot;,&quot;fieldValue&quot;:&quot;$Vlocity.NULL&quot;},{&quot;fieldName&quot;:&quot;values.comune&quot;,&quot;fieldValue&quot;:&quot;$Vlocity.NULL&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-11&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1756976702950-s8oarc8cx&quot;,&quot;label&quot;:&quot;UpdateOmniscriptEstero&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756987866582&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceProv&quot;:&quot;$Vlocity.NULL&quot;,&quot;codiceComune&quot;:&quot;$Vlocity.NULL&quot;,&quot;codiceStato&quot;:&quot;{values.stato}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:2},{&quot;key&quot;:&quot;1756976855191-75wymgraz&quot;,&quot;label&quot;:&quot;UpdateOmniscriptItalia&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988015839&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:3}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756975488676-cq5nc95b2&quot;,&quot;label&quot;:&quot;SetROREQtrue&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756976142218&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;attributes.provincia.isRO&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.provincia.isRequired&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRO&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRequired&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1756975679890-e8o8pn0ai&quot;,&quot;label&quot;:&quot;SetROREQFalse&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756976421760&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;attributes.provincia.isRO&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.provincia.isRequired&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRO&quot;,&quot;fieldValue&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;attributes.comune.isRequired&quot;,&quot;fieldValue&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;values.provincia&quot;,&quot;fieldValue&quot;:&quot;$Vlocity.NULL&quot;},{&quot;fieldName&quot;:&quot;values.comune&quot;,&quot;fieldValue&quot;:&quot;$Vlocity.NULL&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-11&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1756976702950-s8oarc8cx&quot;,&quot;label&quot;:&quot;UpdateOmniscriptEstero&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756987866582&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceProv&quot;:&quot;$Vlocity.NULL&quot;,&quot;codiceComune&quot;:&quot;$Vlocity.NULL&quot;,&quot;codiceStato&quot;:&quot;{values.stato}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:2},{&quot;key&quot;:&quot;1756976855191-75wymgraz&quot;,&quot;label&quot;:&quot;UpdateOmniscriptItalia&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988015839&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-9&quot;,&quot;field&quot;:&quot;values.stato&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Z000&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:3}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;right:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;datasourceKey&quot;:&quot;state0element0&quot;,&quot;uKey&quot;:&quot;1756966326711-33&quot;,&quot;elementLabel&quot;:&quot;Select-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;right:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Provincia&quot;,&quot;fieldBinding&quot;:&quot;{values.provincia}&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{options.optionsProvince}&quot;},{&quot;id&quot;:1,&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{attributes.provincia.isRequired}&quot;},{&quot;id&quot;:2,&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{attributes.provincia.isRO}&quot;},{&quot;id&quot;:3,&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;}],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756976995807-dwq2mlw0k&quot;,&quot;label&quot;:&quot;GetComuniList&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756986400699&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;getComuni\&quot;,\&quot;provinciaRO\&quot;:\&quot;{attributes.provincia.isRO}\&quot;,\&quot;provinciaREQ\&quot;:\&quot;{attributes.provincia.isRequired}\&quot;,\&quot;statoRO\&quot;:\&quot;{attributes.stato.isRO}\&quot;,\&quot;statoREQ\&quot;:\&quot;{attributes.stato.isRequired}\&quot;,\&quot;comuneRO\&quot;:\&quot;{attributes.comune.isRO}\&quot;,\&quot;comuneREQ\&quot;:\&quot;{attributes.comune.isRequired}\&quot;,\&quot;provincia\&quot;:\&quot;{values.provincia}\&quot;,\&quot;comune\&quot;:\&quot;{values.comune}\&quot;,\&quot;stato\&quot;:\&quot;{values.stato}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;values.provincia\\\&quot;:\\\&quot;{values.provincia}\\\&quot;,\\\&quot;attributes.provincia.isRO\\\&quot;:\\\&quot;{attributes.provincia.isRO}\\\&quot;,\\\&quot;attributes.provincia.isRequired\\\&quot;:\\\&quot;{attributes.provincia.isRequired}\\\&quot;,\\\&quot;attributes.comune.isRO\\\&quot;:\\\&quot;{attributes.comune.isRO}\\\&quot;,\\\&quot;attributes.comune.isRequired\\\&quot;:\\\&quot;{attributes.comune.isRequired}\\\&quot;,\\\&quot;attributes.stato.isRequired\\\&quot;:\\\&quot;{attributes.stato.isRequired}\\\&quot;,\\\&quot;attributes.stato.isRO\\\&quot;:\\\&quot;{attributes.stato.isRO}\\\&quot;,\\\&quot;values.comune\\\&quot;:\\\&quot;{values.comune}\\\&quot;,\\\&quot;values.stato\\\&quot;:\\\&quot;{values.stato}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;values.provincia\&quot;,\&quot;val\&quot;:\&quot;RC\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;attributes.provincia.isRO\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;attributes.provincia.isRequired\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;attributes.comune.isRO\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;attributes.comune.isRequired\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;attributes.stato.isRequired\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;attributes.stato.isRO\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:13},{\&quot;name\&quot;:\&quot;values.comune\&quot;,\&quot;val\&quot;:\&quot;H224\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;values.stato\&quot;,\&quot;val\&quot;:\&quot;Z000\&quot;,\&quot;id\&quot;:18}]}&quot;},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1756986486552-4j1mrkcf3&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988038124&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756976995807-dwq2mlw0k&quot;,&quot;label&quot;:&quot;GetComuniList&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756986400699&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;UniDoc_OmniscriptHelper\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;actionType\&quot;:\&quot;getComuni\&quot;,\&quot;provinciaRO\&quot;:\&quot;{attributes.provincia.isRO}\&quot;,\&quot;provinciaREQ\&quot;:\&quot;{attributes.provincia.isRequired}\&quot;,\&quot;statoRO\&quot;:\&quot;{attributes.stato.isRO}\&quot;,\&quot;statoREQ\&quot;:\&quot;{attributes.stato.isRequired}\&quot;,\&quot;comuneRO\&quot;:\&quot;{attributes.comune.isRO}\&quot;,\&quot;comuneREQ\&quot;:\&quot;{attributes.comune.isRequired}\&quot;,\&quot;provincia\&quot;:\&quot;{values.provincia}\&quot;,\&quot;comune\&quot;:\&quot;{values.comune}\&quot;,\&quot;stato\&quot;:\&quot;{values.stato}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;values.provincia\\\&quot;:\\\&quot;{values.provincia}\\\&quot;,\\\&quot;attributes.provincia.isRO\\\&quot;:\\\&quot;{attributes.provincia.isRO}\\\&quot;,\\\&quot;attributes.provincia.isRequired\\\&quot;:\\\&quot;{attributes.provincia.isRequired}\\\&quot;,\\\&quot;attributes.comune.isRO\\\&quot;:\\\&quot;{attributes.comune.isRO}\\\&quot;,\\\&quot;attributes.comune.isRequired\\\&quot;:\\\&quot;{attributes.comune.isRequired}\\\&quot;,\\\&quot;attributes.stato.isRequired\\\&quot;:\\\&quot;{attributes.stato.isRequired}\\\&quot;,\\\&quot;attributes.stato.isRO\\\&quot;:\\\&quot;{attributes.stato.isRO}\\\&quot;,\\\&quot;values.comune\\\&quot;:\\\&quot;{values.comune}\\\&quot;,\\\&quot;values.stato\\\&quot;:\\\&quot;{values.stato}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;values.provincia\&quot;,\&quot;val\&quot;:\&quot;RC\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;attributes.provincia.isRO\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;attributes.provincia.isRequired\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;attributes.comune.isRO\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;attributes.comune.isRequired\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;attributes.stato.isRequired\&quot;,\&quot;val\&quot;:\&quot;false\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;attributes.stato.isRO\&quot;,\&quot;val\&quot;:\&quot;true\&quot;,\&quot;id\&quot;:13},{\&quot;name\&quot;:\&quot;values.comune\&quot;,\&quot;val\&quot;:\&quot;H224\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;values.stato\&quot;,\&quot;val\&quot;:\&quot;Z000\&quot;,\&quot;id\&quot;:18}]}&quot;},&quot;actionIndex&quot;:0,&quot;datasourceKey&quot;:&quot;state0element1_0&quot;},{&quot;key&quot;:&quot;1756986486552-4j1mrkcf3&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988038124&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;left:xx-small&quot;},{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;right:xx-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_xx-small slds-p-right_xx-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;datasourceKey&quot;:&quot;state0element1&quot;,&quot;uKey&quot;:&quot;1756966333426-232&quot;,&quot;elementLabel&quot;:&quot;Select-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;left:xx-small&quot;},{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;right:xx-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_xx-small slds-p-right_xx-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Comune di emissione&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{options.optionsComuni}&quot;},{&quot;id&quot;:1,&quot;label&quot;:&quot;required&quot;,&quot;value&quot;:&quot;{attributes.comune.isRequired}&quot;},{&quot;id&quot;:2,&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{attributes.comune.isRO}&quot;},{&quot;id&quot;:3,&quot;label&quot;:&quot;searchable&quot;,&quot;value&quot;:&quot;true&quot;}],&quot;fieldBinding&quot;:&quot;{values.comune}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756986568905-a13zpli65&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988061460&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1756986568905-a13zpli65&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1756988061460&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;codiceStato&quot;:&quot;{values.stato}&quot;,&quot;codiceProv&quot;:&quot;{values.provincia}&quot;,&quot;codiceComune&quot;:&quot;{values.comune}&quot;},&quot;elementId&quot;:&quot;root&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;datasourceKey&quot;:&quot;state0element2&quot;,&quot;uKey&quot;:&quot;1756966335675-5&quot;,&quot;elementLabel&quot;:&quot;Select-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-p-left_x-small slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-small-size_4-of-12 slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;4&quot;,&quot;large&quot;:&quot;4&quot;,&quot;medium&quot;:&quot;4&quot;,&quot;small&quot;:&quot;4&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;actionType&quot;:&quot;getInfoTerritoriali&quot;,&quot;codProv&quot;:&quot;{Parent.codProv}&quot;,&quot;codStato&quot;:&quot;{Parent.codStato}&quot;,&quot;codComune&quot;:&quot;{Parent.codComune}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.codProv\&quot;:\&quot;{Parent.codProv}\&quot;,\&quot;Parent.codStato\&quot;:\&quot;{Parent.codStato}\&quot;,\&quot;Parent.codComune\&quot;:\&quot;{Parent.codComune}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.codProv&quot;,&quot;val&quot;:&quot;RC&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;Parent.codStato&quot;,&quot;val&quot;:&quot;Z000&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;Parent.codComune&quot;,&quot;val&quot;:&quot;H224&quot;,&quot;id&quot;:6}]},&quot;title&quot;:&quot;UniDoc_UnitaTerrBlock&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDoc_UnitaTerrBlock&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:61}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;attributes&quot;:{&quot;provincia&quot;:{&quot;isRO&quot;:false,&quot;isRequired&quot;:true},&quot;comune&quot;:{&quot;isRO&quot;:false,&quot;isRequired&quot;:true},&quot;stato&quot;:{&quot;isRO&quot;:false,&quot;isRequired&quot;:true}},&quot;options&quot;:{&quot;optionsComuni&quot;:[{&quot;value&quot;:&quot;A065&quot;,&quot;label&quot;:&quot;AFRICO&quot;},{&quot;value&quot;:&quot;A077&quot;,&quot;label&quot;:&quot;AGNANA CALABRA&quot;},{&quot;value&quot;:&quot;A303&quot;,&quot;label&quot;:&quot;ANOIA&quot;},{&quot;value&quot;:&quot;A314&quot;,&quot;label&quot;:&quot;ANTONIMINA&quot;},{&quot;value&quot;:&quot;A385&quot;,&quot;label&quot;:&quot;ARDORE&quot;},{&quot;value&quot;:&quot;A544&quot;,&quot;label&quot;:&quot;BAGALADI&quot;},{&quot;value&quot;:&quot;A552&quot;,&quot;label&quot;:&quot;BAGNARA CALABRA&quot;},{&quot;value&quot;:&quot;A780&quot;,&quot;label&quot;:&quot;BENESTARE&quot;},{&quot;value&quot;:&quot;A843&quot;,&quot;label&quot;:&quot;BIANCO&quot;},{&quot;value&quot;:&quot;A897&quot;,&quot;label&quot;:&quot;BIVONGI&quot;},{&quot;value&quot;:&quot;B097&quot;,&quot;label&quot;:&quot;BOVA&quot;},{&quot;value&quot;:&quot;B099&quot;,&quot;label&quot;:&quot;BOVA MARINA&quot;},{&quot;value&quot;:&quot;B098&quot;,&quot;label&quot;:&quot;BOVALINO&quot;},{&quot;value&quot;:&quot;B118&quot;,&quot;label&quot;:&quot;BRANCALEONE&quot;},{&quot;value&quot;:&quot;B234&quot;,&quot;label&quot;:&quot;BRUZZANO ZEFFIRIO&quot;},{&quot;value&quot;:&quot;B379&quot;,&quot;label&quot;:&quot;CALANNA&quot;},{&quot;value&quot;:&quot;B481&quot;,&quot;label&quot;:&quot;CAMINI&quot;},{&quot;value&quot;:&quot;B516&quot;,&quot;label&quot;:&quot;CAMPO CALABRO&quot;},{&quot;value&quot;:&quot;B591&quot;,&quot;label&quot;:&quot;CANDIDONI&quot;},{&quot;value&quot;:&quot;B617&quot;,&quot;label&quot;:&quot;CANOLO&quot;},{&quot;value&quot;:&quot;B718&quot;,&quot;label&quot;:&quot;CARAFFA DEL BIANCO&quot;},{&quot;value&quot;:&quot;B756&quot;,&quot;label&quot;:&quot;CARDETO&quot;},{&quot;value&quot;:&quot;B766&quot;,&quot;label&quot;:&quot;CARERI&quot;},{&quot;value&quot;:&quot;B966&quot;,&quot;label&quot;:&quot;CASIGNANA&quot;},{&quot;value&quot;:&quot;C285&quot;,&quot;label&quot;:&quot;CAULONIA&quot;},{&quot;value&quot;:&quot;C695&quot;,&quot;label&quot;:&quot;CIMINÀ&quot;},{&quot;value&quot;:&quot;C710&quot;,&quot;label&quot;:&quot;CINQUEFRONDI&quot;},{&quot;value&quot;:&quot;C747&quot;,&quot;label&quot;:&quot;CITTANOVA&quot;},{&quot;value&quot;:&quot;C954&quot;,&quot;label&quot;:&quot;CONDOFURI&quot;},{&quot;value&quot;:&quot;D089&quot;,&quot;label&quot;:&quot;COSOLETO&quot;},{&quot;value&quot;:&quot;D268&quot;,&quot;label&quot;:&quot;DELIANUOVA&quot;},{&quot;value&quot;:&quot;D545&quot;,&quot;label&quot;:&quot;FEROLETO DELLA CHIESA&quot;},{&quot;value&quot;:&quot;D557&quot;,&quot;label&quot;:&quot;FERRUZZANO&quot;},{&quot;value&quot;:&quot;D619&quot;,&quot;label&quot;:&quot;FIUMARA&quot;},{&quot;value&quot;:&quot;D864&quot;,&quot;label&quot;:&quot;GALATRO&quot;},{&quot;value&quot;:&quot;D975&quot;,&quot;label&quot;:&quot;GERACE&quot;},{&quot;value&quot;:&quot;E025&quot;,&quot;label&quot;:&quot;GIFFONE&quot;},{&quot;value&quot;:&quot;E041&quot;,&quot;label&quot;:&quot;GIOIA TAURO&quot;},{&quot;value&quot;:&quot;E044&quot;,&quot;label&quot;:&quot;GIOIOSA IONICA&quot;},{&quot;value&quot;:&quot;E212&quot;,&quot;label&quot;:&quot;GROTTERIA&quot;},{&quot;value&quot;:&quot;E402&quot;,&quot;label&quot;:&quot;LAGANADI&quot;},{&quot;value&quot;:&quot;E479&quot;,&quot;label&quot;:&quot;LAUREANA DI BORRELLO&quot;},{&quot;value&quot;:&quot;D976&quot;,&quot;label&quot;:&quot;LOCRI&quot;},{&quot;value&quot;:&quot;E873&quot;,&quot;label&quot;:&quot;MAMMOLA&quot;},{&quot;value&quot;:&quot;E956&quot;,&quot;label&quot;:&quot;MARINA DI GIOIOSA IONICA&quot;},{&quot;value&quot;:&quot;E968&quot;,&quot;label&quot;:&quot;MAROPATI&quot;},{&quot;value&quot;:&quot;E993&quot;,&quot;label&quot;:&quot;MARTONE&quot;},{&quot;value&quot;:&quot;F105&quot;,&quot;label&quot;:&quot;MELICUCCÀ&quot;},{&quot;value&quot;:&quot;F106&quot;,&quot;label&quot;:&quot;MELICUCCO&quot;},{&quot;value&quot;:&quot;F112&quot;,&quot;label&quot;:&quot;MELITO DI PORTO SALVO&quot;},{&quot;value&quot;:&quot;F301&quot;,&quot;label&quot;:&quot;MOLOCHIO&quot;},{&quot;value&quot;:&quot;F324&quot;,&quot;label&quot;:&quot;MONASTERACE&quot;},{&quot;value&quot;:&quot;D746&quot;,&quot;label&quot;:&quot;MONTEBELLO JONICO&quot;},{&quot;value&quot;:&quot;F779&quot;,&quot;label&quot;:&quot;MOTTA SAN GIOVANNI&quot;},{&quot;value&quot;:&quot;G082&quot;,&quot;label&quot;:&quot;OPPIDO MAMERTINA&quot;},{&quot;value&quot;:&quot;G277&quot;,&quot;label&quot;:&quot;PALIZZI&quot;},{&quot;value&quot;:&quot;G288&quot;,&quot;label&quot;:&quot;PALMI&quot;},{&quot;value&quot;:&quot;G394&quot;,&quot;label&quot;:&quot;PAZZANO&quot;},{&quot;value&quot;:&quot;G729&quot;,&quot;label&quot;:&quot;PLACANICA&quot;},{&quot;value&quot;:&quot;G735&quot;,&quot;label&quot;:&quot;PLATÌ&quot;},{&quot;value&quot;:&quot;G791&quot;,&quot;label&quot;:&quot;POLISTENA&quot;},{&quot;value&quot;:&quot;G905&quot;,&quot;label&quot;:&quot;PORTIGLIOLA&quot;},{&quot;value&quot;:&quot;H224&quot;,&quot;label&quot;:&quot;REGGIO DI CALABRIA&quot;},{&quot;value&quot;:&quot;H265&quot;,&quot;label&quot;:&quot;RIACE&quot;},{&quot;value&quot;:&quot;H359&quot;,&quot;label&quot;:&quot;RIZZICONI&quot;},{&quot;value&quot;:&quot;H408&quot;,&quot;label&quot;:&quot;ROCCAFORTE DEL GRECO&quot;},{&quot;value&quot;:&quot;H456&quot;,&quot;label&quot;:&quot;ROCCELLA IONICA&quot;},{&quot;value&quot;:&quot;H489&quot;,&quot;label&quot;:&quot;ROGHUDI&quot;},{&quot;value&quot;:&quot;H558&quot;,&quot;label&quot;:&quot;ROSARNO&quot;},{&quot;value&quot;:&quot;H013&quot;,&quot;label&quot;:&quot;SAMO&quot;},{&quot;value&quot;:&quot;M277&quot;,&quot;label&quot;:&quot;SAN FERDINANDO&quot;},{&quot;value&quot;:&quot;H889&quot;,&quot;label&quot;:&quot;SAN GIORGIO MORGETO&quot;},{&quot;value&quot;:&quot;H903&quot;,&quot;label&quot;:&quot;SAN GIOVANNI DI GERACE&quot;},{&quot;value&quot;:&quot;H959&quot;,&quot;label&quot;:&quot;SAN LORENZO&quot;},{&quot;value&quot;:&quot;H970&quot;,&quot;label&quot;:&quot;SAN LUCA&quot;},{&quot;value&quot;:&quot;I102&quot;,&quot;label&quot;:&quot;SAN PIETRO DI CARIDÀ&quot;},{&quot;value&quot;:&quot;I132&quot;,&quot;label&quot;:&quot;SAN PROCOPIO&quot;},{&quot;value&quot;:&quot;I139&quot;,&quot;label&quot;:&quot;SAN ROBERTO&quot;},{&quot;value&quot;:&quot;I198&quot;,&quot;label&quot;:&quot;SANT&apos;AGATA DEL BIANCO&quot;},{&quot;value&quot;:&quot;I214&quot;,&quot;label&quot;:&quot;SANT&apos;ALESSIO IN ASPROMONTE&quot;},{&quot;value&quot;:&quot;I333&quot;,&quot;label&quot;:&quot;SANT&apos;EUFEMIA D&apos;ASPROMONTE&quot;},{&quot;value&quot;:&quot;I341&quot;,&quot;label&quot;:&quot;SANT&apos;ILARIO DELLO IONIO&quot;},{&quot;value&quot;:&quot;I176&quot;,&quot;label&quot;:&quot;SANTA CRISTINA D&apos;ASPROMONTE&quot;},{&quot;value&quot;:&quot;I371&quot;,&quot;label&quot;:&quot;SANTO STEFANO IN ASPROMONTE&quot;},{&quot;value&quot;:&quot;I536&quot;,&quot;label&quot;:&quot;SCIDO&quot;},{&quot;value&quot;:&quot;I537&quot;,&quot;label&quot;:&quot;SCILLA&quot;},{&quot;value&quot;:&quot;I600&quot;,&quot;label&quot;:&quot;SEMINARA&quot;},{&quot;value&quot;:&quot;I656&quot;,&quot;label&quot;:&quot;SERRATA&quot;},{&quot;value&quot;:&quot;I725&quot;,&quot;label&quot;:&quot;SIDERNO&quot;},{&quot;value&quot;:&quot;I753&quot;,&quot;label&quot;:&quot;SINOPOLI&quot;},{&quot;value&quot;:&quot;I936&quot;,&quot;label&quot;:&quot;STAITI&quot;},{&quot;value&quot;:&quot;I955&quot;,&quot;label&quot;:&quot;STIGNANO&quot;},{&quot;value&quot;:&quot;I956&quot;,&quot;label&quot;:&quot;STILO&quot;},{&quot;value&quot;:&quot;L063&quot;,&quot;label&quot;:&quot;TAURIANOVA&quot;},{&quot;value&quot;:&quot;L127&quot;,&quot;label&quot;:&quot;TERRANOVA SAPPO MINULIO&quot;},{&quot;value&quot;:&quot;L673&quot;,&quot;label&quot;:&quot;VARAPODIO&quot;},{&quot;value&quot;:&quot;M018&quot;,&quot;label&quot;:&quot;VILLA SAN GIOVANNI&quot;}],&quot;optionsProvince&quot;:[{&quot;value&quot;:&quot;AG&quot;,&quot;label&quot;:&quot;AGRIGENTO&quot;},{&quot;value&quot;:&quot;AL&quot;,&quot;label&quot;:&quot;ALESSANDRIA&quot;},{&quot;value&quot;:&quot;AN&quot;,&quot;label&quot;:&quot;ANCONA&quot;},{&quot;value&quot;:&quot;AR&quot;,&quot;label&quot;:&quot;AREZZO&quot;},{&quot;value&quot;:&quot;AP&quot;,&quot;label&quot;:&quot;ASCOLI PICENO&quot;},{&quot;value&quot;:&quot;AT&quot;,&quot;label&quot;:&quot;ASTI&quot;},{&quot;value&quot;:&quot;AV&quot;,&quot;label&quot;:&quot;AVELLINO&quot;},{&quot;value&quot;:&quot;BA&quot;,&quot;label&quot;:&quot;BARI&quot;},{&quot;value&quot;:&quot;BT&quot;,&quot;label&quot;:&quot;BARLETTA-ANDRIA-TRANI&quot;},{&quot;value&quot;:&quot;BL&quot;,&quot;label&quot;:&quot;BELLUNO&quot;},{&quot;value&quot;:&quot;BN&quot;,&quot;label&quot;:&quot;BENEVENTO&quot;},{&quot;value&quot;:&quot;BG&quot;,&quot;label&quot;:&quot;BERGAMO&quot;},{&quot;value&quot;:&quot;BI&quot;,&quot;label&quot;:&quot;BIELLA&quot;},{&quot;value&quot;:&quot;BO&quot;,&quot;label&quot;:&quot;BOLOGNA&quot;},{&quot;value&quot;:&quot;BZ&quot;,&quot;label&quot;:&quot;BOLZANO/BOZEN&quot;},{&quot;value&quot;:&quot;BS&quot;,&quot;label&quot;:&quot;BRESCIA&quot;},{&quot;value&quot;:&quot;BR&quot;,&quot;label&quot;:&quot;BRINDISI&quot;},{&quot;value&quot;:&quot;CA&quot;,&quot;label&quot;:&quot;CAGLIARI&quot;},{&quot;value&quot;:&quot;CL&quot;,&quot;label&quot;:&quot;CALTANISSETTA&quot;},{&quot;value&quot;:&quot;CB&quot;,&quot;label&quot;:&quot;CAMPOBASSO&quot;},{&quot;value&quot;:&quot;CE&quot;,&quot;label&quot;:&quot;CASERTA&quot;},{&quot;value&quot;:&quot;CT&quot;,&quot;label&quot;:&quot;CATANIA&quot;},{&quot;value&quot;:&quot;CZ&quot;,&quot;label&quot;:&quot;CATANZARO&quot;},{&quot;value&quot;:&quot;CH&quot;,&quot;label&quot;:&quot;CHIETI&quot;},{&quot;value&quot;:&quot;CO&quot;,&quot;label&quot;:&quot;COMO&quot;},{&quot;value&quot;:&quot;CS&quot;,&quot;label&quot;:&quot;COSENZA&quot;},{&quot;value&quot;:&quot;CR&quot;,&quot;label&quot;:&quot;CREMONA&quot;},{&quot;value&quot;:&quot;KR&quot;,&quot;label&quot;:&quot;CROTONE&quot;},{&quot;value&quot;:&quot;CN&quot;,&quot;label&quot;:&quot;CUNEO&quot;},{&quot;value&quot;:&quot;EN&quot;,&quot;label&quot;:&quot;ENNA&quot;},{&quot;value&quot;:&quot;FM&quot;,&quot;label&quot;:&quot;FERMO&quot;},{&quot;value&quot;:&quot;FE&quot;,&quot;label&quot;:&quot;FERRARA&quot;},{&quot;value&quot;:&quot;FI&quot;,&quot;label&quot;:&quot;FIRENZE&quot;},{&quot;value&quot;:&quot;FG&quot;,&quot;label&quot;:&quot;FOGGIA&quot;},{&quot;value&quot;:&quot;FC&quot;,&quot;label&quot;:&quot;FORLÌ-CESENA&quot;},{&quot;value&quot;:&quot;FR&quot;,&quot;label&quot;:&quot;FROSINONE&quot;},{&quot;value&quot;:&quot;GE&quot;,&quot;label&quot;:&quot;GENOVA&quot;},{&quot;value&quot;:&quot;GO&quot;,&quot;label&quot;:&quot;GORIZIA&quot;},{&quot;value&quot;:&quot;GR&quot;,&quot;label&quot;:&quot;GROSSETO&quot;},{&quot;value&quot;:&quot;IM&quot;,&quot;label&quot;:&quot;IMPERIA&quot;},{&quot;value&quot;:&quot;IS&quot;,&quot;label&quot;:&quot;ISERNIA&quot;},{&quot;value&quot;:&quot;AQ&quot;,&quot;label&quot;:&quot;L&apos;AQUILA&quot;},{&quot;value&quot;:&quot;SP&quot;,&quot;label&quot;:&quot;LA SPEZIA&quot;},{&quot;value&quot;:&quot;LT&quot;,&quot;label&quot;:&quot;LATINA&quot;},{&quot;value&quot;:&quot;LE&quot;,&quot;label&quot;:&quot;LECCE&quot;},{&quot;value&quot;:&quot;LC&quot;,&quot;label&quot;:&quot;LECCO&quot;},{&quot;value&quot;:&quot;LI&quot;,&quot;label&quot;:&quot;LIVORNO&quot;},{&quot;value&quot;:&quot;LO&quot;,&quot;label&quot;:&quot;LODI&quot;},{&quot;value&quot;:&quot;LU&quot;,&quot;label&quot;:&quot;LUCCA&quot;},{&quot;value&quot;:&quot;MC&quot;,&quot;label&quot;:&quot;MACERATA&quot;},{&quot;value&quot;:&quot;MN&quot;,&quot;label&quot;:&quot;MANTOVA&quot;},{&quot;value&quot;:&quot;MS&quot;,&quot;label&quot;:&quot;MASSA-CARRARA&quot;},{&quot;value&quot;:&quot;MT&quot;,&quot;label&quot;:&quot;MATERA&quot;},{&quot;value&quot;:&quot;ME&quot;,&quot;label&quot;:&quot;MESSINA&quot;},{&quot;value&quot;:&quot;MI&quot;,&quot;label&quot;:&quot;MILANO&quot;},{&quot;value&quot;:&quot;MO&quot;,&quot;label&quot;:&quot;MODENA&quot;},{&quot;value&quot;:&quot;MB&quot;,&quot;label&quot;:&quot;MONZA E DELLA BRIANZA&quot;},{&quot;value&quot;:&quot;NA&quot;,&quot;label&quot;:&quot;NAPOLI&quot;},{&quot;value&quot;:&quot;NO&quot;,&quot;label&quot;:&quot;NOVARA&quot;},{&quot;value&quot;:&quot;NU&quot;,&quot;label&quot;:&quot;NUORO&quot;},{&quot;value&quot;:&quot;OR&quot;,&quot;label&quot;:&quot;ORISTANO&quot;},{&quot;value&quot;:&quot;PD&quot;,&quot;label&quot;:&quot;PADOVA&quot;},{&quot;value&quot;:&quot;PA&quot;,&quot;label&quot;:&quot;PALERMO&quot;},{&quot;value&quot;:&quot;PR&quot;,&quot;label&quot;:&quot;PARMA&quot;},{&quot;value&quot;:&quot;PV&quot;,&quot;label&quot;:&quot;PAVIA&quot;},{&quot;value&quot;:&quot;PG&quot;,&quot;label&quot;:&quot;PERUGIA&quot;},{&quot;value&quot;:&quot;PU&quot;,&quot;label&quot;:&quot;PESARO E URBINO&quot;},{&quot;value&quot;:&quot;PE&quot;,&quot;label&quot;:&quot;PESCARA&quot;},{&quot;value&quot;:&quot;PC&quot;,&quot;label&quot;:&quot;PIACENZA&quot;},{&quot;value&quot;:&quot;PI&quot;,&quot;label&quot;:&quot;PISA&quot;},{&quot;value&quot;:&quot;PT&quot;,&quot;label&quot;:&quot;PISTOIA&quot;},{&quot;value&quot;:&quot;PN&quot;,&quot;label&quot;:&quot;PORDENONE&quot;},{&quot;value&quot;:&quot;PZ&quot;,&quot;label&quot;:&quot;POTENZA&quot;},{&quot;value&quot;:&quot;PO&quot;,&quot;label&quot;:&quot;PRATO&quot;},{&quot;value&quot;:&quot;RG&quot;,&quot;label&quot;:&quot;RAGUSA&quot;},{&quot;value&quot;:&quot;RA&quot;,&quot;label&quot;:&quot;RAVENNA&quot;},{&quot;value&quot;:&quot;RC&quot;,&quot;label&quot;:&quot;REGGIO CALABRIA&quot;},{&quot;value&quot;:&quot;RE&quot;,&quot;label&quot;:&quot;REGGIO NELL&apos;EMILIA&quot;},{&quot;value&quot;:&quot;RI&quot;,&quot;label&quot;:&quot;RIETI&quot;},{&quot;value&quot;:&quot;RN&quot;,&quot;label&quot;:&quot;RIMINI&quot;},{&quot;value&quot;:&quot;RM&quot;,&quot;label&quot;:&quot;ROMA&quot;},{&quot;value&quot;:&quot;RO&quot;,&quot;label&quot;:&quot;ROVIGO&quot;},{&quot;value&quot;:&quot;SA&quot;,&quot;label&quot;:&quot;SALERNO&quot;},{&quot;value&quot;:&quot;SS&quot;,&quot;label&quot;:&quot;SASSARI&quot;},{&quot;value&quot;:&quot;SV&quot;,&quot;label&quot;:&quot;SAVONA&quot;},{&quot;value&quot;:&quot;SI&quot;,&quot;label&quot;:&quot;SIENA&quot;},{&quot;value&quot;:&quot;SR&quot;,&quot;label&quot;:&quot;SIRACUSA&quot;},{&quot;value&quot;:&quot;SO&quot;,&quot;label&quot;:&quot;SONDRIO&quot;},{&quot;value&quot;:&quot;SU&quot;,&quot;label&quot;:&quot;SUD SARDEGNA&quot;},{&quot;value&quot;:&quot;TA&quot;,&quot;label&quot;:&quot;TARANTO&quot;},{&quot;value&quot;:&quot;TE&quot;,&quot;label&quot;:&quot;TERAMO&quot;},{&quot;value&quot;:&quot;TR&quot;,&quot;label&quot;:&quot;TERNI&quot;},{&quot;value&quot;:&quot;TO&quot;,&quot;label&quot;:&quot;TORINO&quot;},{&quot;value&quot;:&quot;TP&quot;,&quot;label&quot;:&quot;TRAPANI&quot;},{&quot;value&quot;:&quot;TN&quot;,&quot;label&quot;:&quot;TRENTO&quot;},{&quot;value&quot;:&quot;TV&quot;,&quot;label&quot;:&quot;TREVISO&quot;},{&quot;value&quot;:&quot;TS&quot;,&quot;label&quot;:&quot;TRIESTE&quot;},{&quot;value&quot;:&quot;UD&quot;,&quot;label&quot;:&quot;UDINE&quot;},{&quot;value&quot;:&quot;AO&quot;,&quot;label&quot;:&quot;VALLE D&apos;AOSTA/VALLÉE D&apos;AOSTE&quot;},{&quot;value&quot;:&quot;VA&quot;,&quot;label&quot;:&quot;VARESE&quot;},{&quot;value&quot;:&quot;VE&quot;,&quot;label&quot;:&quot;VENEZIA&quot;},{&quot;value&quot;:&quot;VB&quot;,&quot;label&quot;:&quot;VERBANO-CUSIO-OSSOLA&quot;},{&quot;value&quot;:&quot;VC&quot;,&quot;label&quot;:&quot;VERCELLI&quot;},{&quot;value&quot;:&quot;VR&quot;,&quot;label&quot;:&quot;VERONA&quot;},{&quot;value&quot;:&quot;VV&quot;,&quot;label&quot;:&quot;VIBO VALENTIA&quot;},{&quot;value&quot;:&quot;VI&quot;,&quot;label&quot;:&quot;VICENZA&quot;},{&quot;value&quot;:&quot;VT&quot;,&quot;label&quot;:&quot;VITERBO&quot;}],&quot;optionsStati&quot;:[{&quot;value&quot;:&quot;Z200&quot;,&quot;label&quot;:&quot;AFGHANISTAN&quot;},{&quot;value&quot;:&quot;Z100&quot;,&quot;label&quot;:&quot;ALBANIA&quot;},{&quot;value&quot;:&quot;Z301&quot;,&quot;label&quot;:&quot;ALGERIA&quot;},{&quot;value&quot;:&quot;Z725&quot;,&quot;label&quot;:&quot;AMERICAN SAMOA&quot;},{&quot;value&quot;:&quot;Z101&quot;,&quot;label&quot;:&quot;ANDORRA&quot;},{&quot;value&quot;:&quot;Z302&quot;,&quot;label&quot;:&quot;ANGOLA&quot;},{&quot;value&quot;:&quot;Z529&quot;,&quot;label&quot;:&quot;ANGUILLA&quot;},{&quot;value&quot;:&quot;Z532&quot;,&quot;label&quot;:&quot;ANTIGUA E BARBUDA&quot;},{&quot;value&quot;:&quot;Z203&quot;,&quot;label&quot;:&quot;ARABIA SAUDITA&quot;},{&quot;value&quot;:&quot;Z600&quot;,&quot;label&quot;:&quot;ARGENTINA&quot;},{&quot;value&quot;:&quot;Z252&quot;,&quot;label&quot;:&quot;ARMENIA&quot;},{&quot;value&quot;:&quot;Z501&quot;,&quot;label&quot;:&quot;ARUBA&quot;},{&quot;value&quot;:&quot;Z700&quot;,&quot;label&quot;:&quot;AUSTRALIA&quot;},{&quot;value&quot;:&quot;Z102&quot;,&quot;label&quot;:&quot;AUSTRIA&quot;},{&quot;value&quot;:&quot;Z253&quot;,&quot;label&quot;:&quot;AZERBAIJAN&quot;},{&quot;value&quot;:&quot;Z502&quot;,&quot;label&quot;:&quot;BAHAMAS&quot;},{&quot;value&quot;:&quot;Z204&quot;,&quot;label&quot;:&quot;BAHREIN&quot;},{&quot;value&quot;:&quot;Z249&quot;,&quot;label&quot;:&quot;BANGLADESH&quot;},{&quot;value&quot;:&quot;Z522&quot;,&quot;label&quot;:&quot;BARBADOS&quot;},{&quot;value&quot;:&quot;Z103&quot;,&quot;label&quot;:&quot;BELGIO&quot;},{&quot;value&quot;:&quot;Z512&quot;,&quot;label&quot;:&quot;BELIZE&quot;},{&quot;value&quot;:&quot;Z314&quot;,&quot;label&quot;:&quot;BENIN&quot;},{&quot;value&quot;:&quot;Z400&quot;,&quot;label&quot;:&quot;BERMUDA&quot;},{&quot;value&quot;:&quot;Z205&quot;,&quot;label&quot;:&quot;BHUTAN&quot;},{&quot;value&quot;:&quot;Z139&quot;,&quot;label&quot;:&quot;BIELORUSSIA&quot;},{&quot;value&quot;:&quot;Z601&quot;,&quot;label&quot;:&quot;BOLIVIA&quot;},{&quot;value&quot;:&quot;Z153&quot;,&quot;label&quot;:&quot;BOSNIA ED ERZEGOVINA&quot;},{&quot;value&quot;:&quot;Z358&quot;,&quot;label&quot;:&quot;BOTSWANA&quot;},{&quot;value&quot;:&quot;Z602&quot;,&quot;label&quot;:&quot;BRASILE&quot;},{&quot;value&quot;:&quot;Z104&quot;,&quot;label&quot;:&quot;BULGARIA&quot;},{&quot;value&quot;:&quot;Z354&quot;,&quot;label&quot;:&quot;BURKINA FASO&quot;},{&quot;value&quot;:&quot;Z305&quot;,&quot;label&quot;:&quot;BURUNDI&quot;},{&quot;value&quot;:&quot;Z716&quot;,&quot;label&quot;:&quot;CALEDONIA NUOVA&quot;},{&quot;value&quot;:&quot;Z208&quot;,&quot;label&quot;:&quot;CAMBOGIA&quot;},{&quot;value&quot;:&quot;Z306&quot;,&quot;label&quot;:&quot;CAMERUN&quot;},{&quot;value&quot;:&quot;Z401&quot;,&quot;label&quot;:&quot;CANADA&quot;},{&quot;value&quot;:&quot;Z307&quot;,&quot;label&quot;:&quot;CAPO VERDE&quot;},{&quot;value&quot;:&quot;Z309&quot;,&quot;label&quot;:&quot;CIAD&quot;},{&quot;value&quot;:&quot;Z603&quot;,&quot;label&quot;:&quot;CILE&quot;},{&quot;value&quot;:&quot;Z210&quot;,&quot;label&quot;:&quot;CINA REPUBBLICA POPOLARE&quot;},{&quot;value&quot;:&quot;Z211&quot;,&quot;label&quot;:&quot;CIPRO&quot;},{&quot;value&quot;:&quot;Z106&quot;,&quot;label&quot;:&quot;CITTA&apos; DEL VATICANO&quot;},{&quot;value&quot;:&quot;Z604&quot;,&quot;label&quot;:&quot;COLOMBIA&quot;},{&quot;value&quot;:&quot;Z310&quot;,&quot;label&quot;:&quot;COMORE&quot;},{&quot;value&quot;:&quot;Z311&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z312&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA DEMOCRATICA&quot;},{&quot;value&quot;:&quot;Z214&quot;,&quot;label&quot;:&quot;COREA DEL NORD&quot;},{&quot;value&quot;:&quot;Z213&quot;,&quot;label&quot;:&quot;COREA DEL SUD&quot;},{&quot;value&quot;:&quot;Z313&quot;,&quot;label&quot;:&quot;COSTA D&apos;AVORIO&quot;},{&quot;value&quot;:&quot;Z503&quot;,&quot;label&quot;:&quot;COSTA RICA&quot;},{&quot;value&quot;:&quot;Z149&quot;,&quot;label&quot;:&quot;CROAZIA&quot;},{&quot;value&quot;:&quot;Z504&quot;,&quot;label&quot;:&quot;CUBA&quot;},{&quot;value&quot;:&quot;Z107&quot;,&quot;label&quot;:&quot;DANIMARCA&quot;},{&quot;value&quot;:&quot;Z900&quot;,&quot;label&quot;:&quot;DIPENDENZE AUSTRALIANE&quot;},{&quot;value&quot;:&quot;Z901&quot;,&quot;label&quot;:&quot;DIPENDENZE BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z800&quot;,&quot;label&quot;:&quot;DIPENDENZE CANADESI&quot;},{&quot;value&quot;:&quot;Z902&quot;,&quot;label&quot;:&quot;DIPENDENZE FRANCESI&quot;},{&quot;value&quot;:&quot;Z903&quot;,&quot;label&quot;:&quot;DIPENDENZE NEOZELANDESI&quot;},{&quot;value&quot;:&quot;Z904&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;},{&quot;value&quot;:&quot;Z801&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ARTICHE&quot;},{&quot;value&quot;:&quot;Z802&quot;,&quot;label&quot;:&quot;DIPENDENZE RUSSE&quot;},{&quot;value&quot;:&quot;Z905&quot;,&quot;label&quot;:&quot;DIPENDENZE STATUNITENSI&quot;},{&quot;value&quot;:&quot;Z906&quot;,&quot;label&quot;:&quot;DIPENDENZE SUDAFRICANE&quot;},{&quot;value&quot;:&quot;Z361&quot;,&quot;label&quot;:&quot;DJIBOUTI&quot;},{&quot;value&quot;:&quot;Z526&quot;,&quot;label&quot;:&quot;DOMINICA&quot;},{&quot;value&quot;:&quot;Z605&quot;,&quot;label&quot;:&quot;ECUADOR&quot;},{&quot;value&quot;:&quot;Z336&quot;,&quot;label&quot;:&quot;EGITTO&quot;},{&quot;value&quot;:&quot;Z506&quot;,&quot;label&quot;:&quot;EL SALVADOR&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z368&quot;,&quot;label&quot;:&quot;ERITREA&quot;},{&quot;value&quot;:&quot;Z144&quot;,&quot;label&quot;:&quot;ESTONIA&quot;},{&quot;value&quot;:&quot;Z349&quot;,&quot;label&quot;:&quot;ESWATINI&quot;},{&quot;value&quot;:&quot;Z315&quot;,&quot;label&quot;:&quot;ETIOPIA&quot;},{&quot;value&quot;:&quot;Z108&quot;,&quot;label&quot;:&quot;FAROE ISLANDS&quot;},{&quot;value&quot;:&quot;Z704&quot;,&quot;label&quot;:&quot;FIJI&quot;},{&quot;value&quot;:&quot;Z216&quot;,&quot;label&quot;:&quot;FILIPPINE&quot;},{&quot;value&quot;:&quot;Z109&quot;,&quot;label&quot;:&quot;FINLANDIA&quot;},{&quot;value&quot;:&quot;Z110&quot;,&quot;label&quot;:&quot;FRANCIA&quot;},{&quot;value&quot;:&quot;Z316&quot;,&quot;label&quot;:&quot;GABON&quot;},{&quot;value&quot;:&quot;Z317&quot;,&quot;label&quot;:&quot;GAMBIA&quot;},{&quot;value&quot;:&quot;Z254&quot;,&quot;label&quot;:&quot;GEORGIA&quot;},{&quot;value&quot;:&quot;Z112&quot;,&quot;label&quot;:&quot;GERMANIA&quot;},{&quot;value&quot;:&quot;Z260&quot;,&quot;label&quot;:&quot;GERUSALEMME&quot;},{&quot;value&quot;:&quot;Z318&quot;,&quot;label&quot;:&quot;GHANA&quot;},{&quot;value&quot;:&quot;Z507&quot;,&quot;label&quot;:&quot;GIAMAICA&quot;},{&quot;value&quot;:&quot;Z219&quot;,&quot;label&quot;:&quot;GIAPPONE&quot;},{&quot;value&quot;:&quot;Z113&quot;,&quot;label&quot;:&quot;GIBILTERRA&quot;},{&quot;value&quot;:&quot;Z220&quot;,&quot;label&quot;:&quot;GIORDANIA&quot;},{&quot;value&quot;:&quot;Z115&quot;,&quot;label&quot;:&quot;GRECIA&quot;},{&quot;value&quot;:&quot;Z524&quot;,&quot;label&quot;:&quot;GRENADA&quot;},{&quot;value&quot;:&quot;Z402&quot;,&quot;label&quot;:&quot;GROENLANDIA&quot;},{&quot;value&quot;:&quot;Z508&quot;,&quot;label&quot;:&quot;GUADALUPE&quot;},{&quot;value&quot;:&quot;Z706&quot;,&quot;label&quot;:&quot;GUAM&quot;},{&quot;value&quot;:&quot;Z509&quot;,&quot;label&quot;:&quot;GUATEMALA&quot;},{&quot;value&quot;:&quot;Z319&quot;,&quot;label&quot;:&quot;GUINEA&quot;},{&quot;value&quot;:&quot;Z321&quot;,&quot;label&quot;:&quot;GUINEA EQUATORIALE&quot;},{&quot;value&quot;:&quot;Z320&quot;,&quot;label&quot;:&quot;GUINEA-BISSAU&quot;},{&quot;value&quot;:&quot;Z606&quot;,&quot;label&quot;:&quot;GUYANA&quot;},{&quot;value&quot;:&quot;Z607&quot;,&quot;label&quot;:&quot;GUYANA FRANCESE&quot;},{&quot;value&quot;:&quot;Z510&quot;,&quot;label&quot;:&quot;HAITI&quot;},{&quot;value&quot;:&quot;Z511&quot;,&quot;label&quot;:&quot;HONDURAS&quot;},{&quot;value&quot;:&quot;Z222&quot;,&quot;label&quot;:&quot;INDIA&quot;},{&quot;value&quot;:&quot;Z223&quot;,&quot;label&quot;:&quot;INDONESIA&quot;},{&quot;value&quot;:&quot;Z224&quot;,&quot;label&quot;:&quot;IRAN&quot;},{&quot;value&quot;:&quot;Z225&quot;,&quot;label&quot;:&quot;IRAQ&quot;},{&quot;value&quot;:&quot;Z707&quot;,&quot;label&quot;:&quot;IRIAN OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z116&quot;,&quot;label&quot;:&quot;IRLANDA&quot;},{&quot;value&quot;:&quot;Z117&quot;,&quot;label&quot;:&quot;ISLANDA&quot;},{&quot;value&quot;:&quot;Z702&quot;,&quot;label&quot;:&quot;ISOLA CHRISTMAS&quot;},{&quot;value&quot;:&quot;Z715&quot;,&quot;label&quot;:&quot;ISOLA NORFOLK&quot;},{&quot;value&quot;:&quot;Z530&quot;,&quot;label&quot;:&quot;ISOLE CAYMAN&quot;},{&quot;value&quot;:&quot;Z212&quot;,&quot;label&quot;:&quot;ISOLE COCOS&quot;},{&quot;value&quot;:&quot;Z703&quot;,&quot;label&quot;:&quot;ISOLE COOK&quot;},{&quot;value&quot;:&quot;Z609&quot;,&quot;label&quot;:&quot;ISOLE FALKLAND&quot;},{&quot;value&quot;:&quot;Z711&quot;,&quot;label&quot;:&quot;ISOLE MARSHALL&quot;},{&quot;value&quot;:&quot;Z724&quot;,&quot;label&quot;:&quot;ISOLE SOLOMON&quot;},{&quot;value&quot;:&quot;Z519&quot;,&quot;label&quot;:&quot;ISOLE TURKS E CAICOS&quot;},{&quot;value&quot;:&quot;Z520&quot;,&quot;label&quot;:&quot;ISOLE VERGINI AMERICANE&quot;},{&quot;value&quot;:&quot;Z525&quot;,&quot;label&quot;:&quot;ISOLE VERGINI BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z729&quot;,&quot;label&quot;:&quot;ISOLE WALLIS E FUTUNA&quot;},{&quot;value&quot;:&quot;Z226&quot;,&quot;label&quot;:&quot;ISRAELE&quot;},{&quot;value&quot;:&quot;Z000&quot;,&quot;label&quot;:&quot;ITALIA&quot;},{&quot;value&quot;:&quot;Z124&quot;,&quot;label&quot;:&quot;JERSEY (BALIATO DI)&quot;},{&quot;value&quot;:&quot;Z255&quot;,&quot;label&quot;:&quot;KAZAKHSTAN&quot;},{&quot;value&quot;:&quot;Z322&quot;,&quot;label&quot;:&quot;KENYA&quot;},{&quot;value&quot;:&quot;Z731&quot;,&quot;label&quot;:&quot;KIRIBATI&quot;},{&quot;value&quot;:&quot;Z160&quot;,&quot;label&quot;:&quot;KOSOVO&quot;},{&quot;value&quot;:&quot;Z227&quot;,&quot;label&quot;:&quot;KUWAIT&quot;},{&quot;value&quot;:&quot;Z256&quot;,&quot;label&quot;:&quot;KYRGYZSTAN&quot;},{&quot;value&quot;:&quot;Z228&quot;,&quot;label&quot;:&quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;},{&quot;value&quot;:&quot;Z359&quot;,&quot;label&quot;:&quot;LESOTHO&quot;},{&quot;value&quot;:&quot;Z145&quot;,&quot;label&quot;:&quot;LETTONIA&quot;},{&quot;value&quot;:&quot;Z229&quot;,&quot;label&quot;:&quot;LIBANO&quot;},{&quot;value&quot;:&quot;Z325&quot;,&quot;label&quot;:&quot;LIBERIA&quot;},{&quot;value&quot;:&quot;Z326&quot;,&quot;label&quot;:&quot;LIBIA&quot;},{&quot;value&quot;:&quot;Z119&quot;,&quot;label&quot;:&quot;LIECHTENSTEIN&quot;},{&quot;value&quot;:&quot;Z146&quot;,&quot;label&quot;:&quot;LITUANIA&quot;},{&quot;value&quot;:&quot;Z120&quot;,&quot;label&quot;:&quot;LUSSEMBURGO&quot;},{&quot;value&quot;:&quot;Z231&quot;,&quot;label&quot;:&quot;MACAU&quot;},{&quot;value&quot;:&quot;Z148&quot;,&quot;label&quot;:&quot;MACEDONIA&quot;},{&quot;value&quot;:&quot;Z327&quot;,&quot;label&quot;:&quot;MADAGASCAR&quot;},{&quot;value&quot;:&quot;Z328&quot;,&quot;label&quot;:&quot;MALAWI&quot;},{&quot;value&quot;:&quot;Z247&quot;,&quot;label&quot;:&quot;MALAYSIA&quot;},{&quot;value&quot;:&quot;Z232&quot;,&quot;label&quot;:&quot;MALDIVE&quot;},{&quot;value&quot;:&quot;Z329&quot;,&quot;label&quot;:&quot;MALI&quot;},{&quot;value&quot;:&quot;Z121&quot;,&quot;label&quot;:&quot;MALTA&quot;},{&quot;value&quot;:&quot;Z122&quot;,&quot;label&quot;:&quot;MAN&quot;},{&quot;value&quot;:&quot;Z710&quot;,&quot;label&quot;:&quot;MARIANNE DEL NORD&quot;},{&quot;value&quot;:&quot;Z330&quot;,&quot;label&quot;:&quot;MAROCCO&quot;},{&quot;value&quot;:&quot;Z513&quot;,&quot;label&quot;:&quot;MARTINICA&quot;},{&quot;value&quot;:&quot;Z331&quot;,&quot;label&quot;:&quot;MAURITANIA&quot;},{&quot;value&quot;:&quot;Z332&quot;,&quot;label&quot;:&quot;MAURITIUS&quot;},{&quot;value&quot;:&quot;Z360&quot;,&quot;label&quot;:&quot;MAYOTTE&quot;},{&quot;value&quot;:&quot;Z514&quot;,&quot;label&quot;:&quot;MESSICO&quot;},{&quot;value&quot;:&quot;Z735&quot;,&quot;label&quot;:&quot;MICRONESIA&quot;},{&quot;value&quot;:&quot;Z140&quot;,&quot;label&quot;:&quot;MOLDAVIA&quot;},{&quot;value&quot;:&quot;Z123&quot;,&quot;label&quot;:&quot;MONACO&quot;},{&quot;value&quot;:&quot;Z233&quot;,&quot;label&quot;:&quot;MONGOLIA&quot;},{&quot;value&quot;:&quot;Z159&quot;,&quot;label&quot;:&quot;MONTENEGRO&quot;},{&quot;value&quot;:&quot;Z531&quot;,&quot;label&quot;:&quot;MONTSERRAT&quot;},{&quot;value&quot;:&quot;Z333&quot;,&quot;label&quot;:&quot;MOZAMBICO&quot;},{&quot;value&quot;:&quot;Z206&quot;,&quot;label&quot;:&quot;MYANMAR&quot;},{&quot;value&quot;:&quot;Z300&quot;,&quot;label&quot;:&quot;NAMIBIA&quot;},{&quot;value&quot;:&quot;Z713&quot;,&quot;label&quot;:&quot;NAURU&quot;},{&quot;value&quot;:&quot;Z234&quot;,&quot;label&quot;:&quot;NEPAL&quot;},{&quot;value&quot;:&quot;Z515&quot;,&quot;label&quot;:&quot;NICARAGUA&quot;},{&quot;value&quot;:&quot;Z334&quot;,&quot;label&quot;:&quot;NIGER&quot;},{&quot;value&quot;:&quot;Z335&quot;,&quot;label&quot;:&quot;NIGERIA&quot;},{&quot;value&quot;:&quot;Z714&quot;,&quot;label&quot;:&quot;NIUE&quot;},{&quot;value&quot;:&quot;Z125&quot;,&quot;label&quot;:&quot;NORVEGIA&quot;},{&quot;value&quot;:&quot;Z719&quot;,&quot;label&quot;:&quot;NUOVA ZELANDA&quot;},{&quot;value&quot;:&quot;Z235&quot;,&quot;label&quot;:&quot;OMAN&quot;},{&quot;value&quot;:&quot;Z126&quot;,&quot;label&quot;:&quot;PAESI BASSI&quot;},{&quot;value&quot;:&quot;Z236&quot;,&quot;label&quot;:&quot;PAKISTAN&quot;},{&quot;value&quot;:&quot;Z734&quot;,&quot;label&quot;:&quot;PALAU REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z516&quot;,&quot;label&quot;:&quot;PANAMA&quot;},{&quot;value&quot;:&quot;Z730&quot;,&quot;label&quot;:&quot;PAPUA NUOVA GUINEA&quot;},{&quot;value&quot;:&quot;Z610&quot;,&quot;label&quot;:&quot;PARAGUAY&quot;},{&quot;value&quot;:&quot;Z611&quot;,&quot;label&quot;:&quot;PERU&apos;&quot;},{&quot;value&quot;:&quot;Z722&quot;,&quot;label&quot;:&quot;PITCAIRN&quot;},{&quot;value&quot;:&quot;Z723&quot;,&quot;label&quot;:&quot;POLINESIA FRANCESE&quot;},{&quot;value&quot;:&quot;Z127&quot;,&quot;label&quot;:&quot;POLONIA&quot;},{&quot;value&quot;:&quot;Z128&quot;,&quot;label&quot;:&quot;PORTOGALLO&quot;},{&quot;value&quot;:&quot;Z518&quot;,&quot;label&quot;:&quot;PUERTO RICO&quot;},{&quot;value&quot;:&quot;Z237&quot;,&quot;label&quot;:&quot;QATAR&quot;},{&quot;value&quot;:&quot;Z114&quot;,&quot;label&quot;:&quot;REGNO UNITO&quot;},{&quot;value&quot;:&quot;Z156&quot;,&quot;label&quot;:&quot;REPUBBLICA CECA&quot;},{&quot;value&quot;:&quot;Z308&quot;,&quot;label&quot;:&quot;REPUBBLICA CENTRAFRICANA&quot;},{&quot;value&quot;:&quot;Z907&quot;,&quot;label&quot;:&quot;REPUBBLICA DEL SUD SUDAN&quot;},{&quot;value&quot;:&quot;Z505&quot;,&quot;label&quot;:&quot;REPUBBLICA DOMINICANA&quot;},{&quot;value&quot;:&quot;Z324&quot;,&quot;label&quot;:&quot;REUNION&quot;},{&quot;value&quot;:&quot;Z129&quot;,&quot;label&quot;:&quot;ROMANIA&quot;},{&quot;value&quot;:&quot;Z338&quot;,&quot;label&quot;:&quot;RUANDA&quot;},{&quot;value&quot;:&quot;Z154&quot;,&quot;label&quot;:&quot;RUSSIA&quot;},{&quot;value&quot;:&quot;Z339&quot;,&quot;label&quot;:&quot;SAHARA OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z533&quot;,&quot;label&quot;:&quot;SAINT CHRISTOPHER E NEVIS&quot;},{&quot;value&quot;:&quot;Z527&quot;,&quot;label&quot;:&quot;SAINT LUCIA&quot;},{&quot;value&quot;:&quot;Z528&quot;,&quot;label&quot;:&quot;SAINT VINCENT E GRANADINE&quot;},{&quot;value&quot;:&quot;Z726&quot;,&quot;label&quot;:&quot;SAMOA&quot;},{&quot;value&quot;:&quot;Z130&quot;,&quot;label&quot;:&quot;SAN MARINO&quot;},{&quot;value&quot;:&quot;Z340&quot;,&quot;label&quot;:&quot;SANT&apos;ELENA&quot;},{&quot;value&quot;:&quot;Z341&quot;,&quot;label&quot;:&quot;SAO TOME&apos; E PRINCIPE&quot;},{&quot;value&quot;:&quot;Z343&quot;,&quot;label&quot;:&quot;SENEGAL&quot;},{&quot;value&quot;:&quot;Z158&quot;,&quot;label&quot;:&quot;SERBIA&quot;},{&quot;value&quot;:&quot;Z342&quot;,&quot;label&quot;:&quot;SEYCHELLES&quot;},{&quot;value&quot;:&quot;Z344&quot;,&quot;label&quot;:&quot;SIERRA LEONE&quot;},{&quot;value&quot;:&quot;Z248&quot;,&quot;label&quot;:&quot;SINGAPORE&quot;},{&quot;value&quot;:&quot;Z240&quot;,&quot;label&quot;:&quot;SIRIA&quot;},{&quot;value&quot;:&quot;Z155&quot;,&quot;label&quot;:&quot;SLOVACCHIA&quot;},{&quot;value&quot;:&quot;Z150&quot;,&quot;label&quot;:&quot;SLOVENIA&quot;},{&quot;value&quot;:&quot;Z345&quot;,&quot;label&quot;:&quot;SOMALIA&quot;},{&quot;value&quot;:&quot;Z131&quot;,&quot;label&quot;:&quot;SPAGNA&quot;},{&quot;value&quot;:&quot;Z209&quot;,&quot;label&quot;:&quot;SRI LANKA&quot;},{&quot;value&quot;:&quot;Z403&quot;,&quot;label&quot;:&quot;ST. PIERRE AND MIQUELON&quot;},{&quot;value&quot;:&quot;Z404&quot;,&quot;label&quot;:&quot;STATI UNITI D&apos;AMERICA&quot;},{&quot;value&quot;:&quot;Z347&quot;,&quot;label&quot;:&quot;SUD AFRICA&quot;},{&quot;value&quot;:&quot;Z348&quot;,&quot;label&quot;:&quot;SUDAN&quot;},{&quot;value&quot;:&quot;Z207&quot;,&quot;label&quot;:&quot;SULTANATO DEL BRUNEI&quot;},{&quot;value&quot;:&quot;Z608&quot;,&quot;label&quot;:&quot;SURINAME&quot;},{&quot;value&quot;:&quot;Z132&quot;,&quot;label&quot;:&quot;SVEZIA&quot;},{&quot;value&quot;:&quot;Z133&quot;,&quot;label&quot;:&quot;SVIZZERA&quot;},{&quot;value&quot;:&quot;Z257&quot;,&quot;label&quot;:&quot;TAGIKISTAN&quot;},{&quot;value&quot;:&quot;Z217&quot;,&quot;label&quot;:&quot;TAIWAN&quot;},{&quot;value&quot;:&quot;Z357&quot;,&quot;label&quot;:&quot;TANZANIA&quot;},{&quot;value&quot;:&quot;Z161&quot;,&quot;label&quot;:&quot;TERRITORI PALESTINESI&quot;},{&quot;value&quot;:&quot;Z241&quot;,&quot;label&quot;:&quot;THAILANDIA&quot;},{&quot;value&quot;:&quot;Z242&quot;,&quot;label&quot;:&quot;TIMOR-LESTE&quot;},{&quot;value&quot;:&quot;Z351&quot;,&quot;label&quot;:&quot;TOGO&quot;},{&quot;value&quot;:&quot;Z727&quot;,&quot;label&quot;:&quot;TOKELAU&quot;},{&quot;value&quot;:&quot;Z728&quot;,&quot;label&quot;:&quot;TONGA&quot;},{&quot;value&quot;:&quot;Z612&quot;,&quot;label&quot;:&quot;TRINIDAD E TOBAGO&quot;},{&quot;value&quot;:&quot;Z352&quot;,&quot;label&quot;:&quot;TUNISIA&quot;},{&quot;value&quot;:&quot;Z243&quot;,&quot;label&quot;:&quot;TURCHIA&quot;},{&quot;value&quot;:&quot;Z258&quot;,&quot;label&quot;:&quot;TURKMENISTAN&quot;},{&quot;value&quot;:&quot;Z732&quot;,&quot;label&quot;:&quot;TUVALU&quot;},{&quot;value&quot;:&quot;Z138&quot;,&quot;label&quot;:&quot;UCRAINA&quot;},{&quot;value&quot;:&quot;Z353&quot;,&quot;label&quot;:&quot;UGANDA&quot;},{&quot;value&quot;:&quot;Z134&quot;,&quot;label&quot;:&quot;UNGHERIA&quot;},{&quot;value&quot;:&quot;Z613&quot;,&quot;label&quot;:&quot;URUGUAY&quot;},{&quot;value&quot;:&quot;Z259&quot;,&quot;label&quot;:&quot;UZBEKISTAN&quot;},{&quot;value&quot;:&quot;Z733&quot;,&quot;label&quot;:&quot;VANUATU&quot;},{&quot;value&quot;:&quot;Z614&quot;,&quot;label&quot;:&quot;VENEZUELA&quot;},{&quot;value&quot;:&quot;Z251&quot;,&quot;label&quot;:&quot;VIETNAM&quot;},{&quot;value&quot;:&quot;Z246&quot;,&quot;label&quot;:&quot;YEMEN&quot;},{&quot;value&quot;:&quot;Z355&quot;,&quot;label&quot;:&quot;ZAMBIA&quot;},{&quot;value&quot;:&quot;Z337&quot;,&quot;label&quot;:&quot;ZIMBABWE&quot;}]},&quot;values&quot;:{&quot;provincia&quot;:&quot;RC&quot;,&quot;comune&quot;:&quot;H224&quot;,&quot;stato&quot;:&quot;Z000&quot;}}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
