<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;codTipoDoc&quot;:&quot;{Parent.tipoDoc}&quot;,&quot;actionType&quot;:&quot;enteEmissione&quot;,&quot;enteEmissione&quot;:&quot;{Parent.enteEmissione}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.tipoDoc\&quot;:\&quot;{Parent.tipoDoc}\&quot;,\&quot;Parent.enteEmissione\&quot;:\&quot;{Parent.enteEmissione}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.tipoDoc&quot;,&quot;val&quot;:&quot;CDN&quot;,&quot;id&quot;:13},{&quot;name&quot;:&quot;Parent.enteEmissione&quot;,&quot;val&quot;:&quot;AMBASCIATA&quot;,&quot;id&quot;:9}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDoc_EnteEmissione</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Ente emissione&quot;,&quot;customProperties&quot;:[{&quot;id&quot;:0,&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{list}&quot;},{&quot;id&quot;:1,&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{readOnly}&quot;}],&quot;value&quot;:&quot;{default}&quot;,&quot;fieldBinding&quot;:&quot;{default}&quot;,&quot;required&quot;:true,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1757005925795-qx8k77s7v&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1757012233791&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;root&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;enteEmissione&quot;:&quot;{default}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1757005925795-qx8k77s7v&quot;,&quot;label&quot;:&quot;UpdateOS&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1757012233791&quot;,&quot;type&quot;:&quot;updateOmniScript&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;elementId&quot;:&quot;root&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;enteEmissione&quot;:&quot;{default}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;datasourceKey&quot;:&quot;state0element0&quot;,&quot;uKey&quot;:&quot;1757001825933-76&quot;,&quot;elementLabel&quot;:&quot;Select-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDoc_OmniscriptHelper&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;codTipoDoc&quot;:&quot;{Parent.tipoDoc}&quot;,&quot;actionType&quot;:&quot;enteEmissione&quot;,&quot;enteEmissione&quot;:&quot;{Parent.enteEmissione}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Parent.tipoDoc\&quot;:\&quot;{Parent.tipoDoc}\&quot;,\&quot;Parent.enteEmissione\&quot;:\&quot;{Parent.enteEmissione}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.tipoDoc&quot;,&quot;val&quot;:&quot;CDN&quot;,&quot;id&quot;:13},{&quot;name&quot;:&quot;Parent.enteEmissione&quot;,&quot;val&quot;:&quot;AMBASCIATA&quot;,&quot;id&quot;:9}]},&quot;title&quot;:&quot;UniDoc_EnteEmissione&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDoc_EnteEmissione&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:61}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;list&quot;:[{&quot;value&quot;:&quot;COMUNE&quot;,&quot;label&quot;:&quot;Comune&quot;},{&quot;value&quot;:&quot;CONSOLATO&quot;,&quot;label&quot;:&quot;Consolato&quot;},{&quot;value&quot;:&quot;AMBASCIATA&quot;,&quot;label&quot;:&quot;Ambasciata&quot;}],&quot;readOnly&quot;:false,&quot;default&quot;:&quot;AMBASCIATA&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
