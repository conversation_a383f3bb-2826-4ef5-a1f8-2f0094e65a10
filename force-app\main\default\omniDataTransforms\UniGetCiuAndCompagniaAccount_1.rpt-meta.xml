<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;GetDocumentsDetails&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniGetCiuAndCompagniaAccount</name>
    <nullInputsIncludedInOutput>true</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem5</globalKey>
        <inputFieldName>accountDetails:Relation__r.FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetails:compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;SOC_1&quot; : &quot;unipolsai&quot;,
  &quot;SOC_4&quot; : &quot;unisalute&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetCiuAndCompagniaAccountCustom6847</globalKey>
        <inputFieldName>accountDetails:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetails:accountDetailsId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem1</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Individual&apos;</filterValue>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem0</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem3</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Business&apos;</filterValue>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem2</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetCiuAndCompagniaAccountCustom0jI9O000000ybazUAAItem4</globalKey>
        <inputFieldName>accountDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetCiuAndCompagniaAccount</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetails:ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X000015e2ZLQAY&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetCiuAndCompagniaAccount_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>