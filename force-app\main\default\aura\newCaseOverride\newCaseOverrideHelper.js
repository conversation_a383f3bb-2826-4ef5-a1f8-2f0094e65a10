({
    processRecordTypes: function(component, recordTypes) {
        var urRecordTypes = recordTypes.filter(function(rt) {
            return rt.Name.toLowerCase().indexOf("ur_") === 0;
        });

        if (urRecordTypes.length === 0) {
            // this.showNewCaseAuraModal(component);
            component.set("v.showNewCaseOrAura", true);
            component.set("v.showRTs", false);
            return;
        }

        if (urRecordTypes.length === 1) {
            this.createCaseWithRecordType(urRecordTypes[0].Id);
            return;
        }

        var options = urRecordTypes.map(function(rt) {
            return {
                label: rt.Name,
                value: rt.Id
            };
        });

        component.set("v.recordTypesMap", urRecordTypes);
        component.set("v.RToptions", options);
        component.set("v.selectedRTId", urRecordTypes[0].Id);
        component.set("v.showRTs", true);
    },

    createCaseWithRecordType: function(recordTypeId) {
        var createRecordEvent = $A.get("e.force:createRecord");
        createRecordEvent.setParams({
            "entityApiName": "Case",
            "recordTypeId": recordTypeId
        });
        createRecordEvent.fire();
    },

    showNewCaseAuraModal: function(component) {
        var overlayLib = component.find("overlayLib");
        $A.createComponent("c:newCaseOrAura", {}, function(content, status) {
            if (status === "SUCCESS") {
                overlayLib.showCustomModal({
                    header: "Nuovo Case",
                    body: content,
                    showCloseButton: true,
                    cssClass: "customModal"
                });
            } else {
                console.error("Errore nella creazione del componente newCaseOrAura");
            }
        });
    },

    showToast: function(title, type, message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({ title, type, message });
        toastEvent.fire();
    },

   closeAction: function() {

        var navEvt = $A.get("e.force:navigateToURL");
        if (navEvt) {
            navEvt.setParams({
                "url": "/lightning/o/Case/list"
            });
            navEvt.fire();
        } else {
            window.location.href = "/lightning/o/Case/list";
        }
    },

    handleError: function(errors) {
        if (errors && errors[0] && errors[0].message) {
            console.error("Error message: " + errors[0].message);
        } else {
            console.error("Unknown error");
        }
    }
})
