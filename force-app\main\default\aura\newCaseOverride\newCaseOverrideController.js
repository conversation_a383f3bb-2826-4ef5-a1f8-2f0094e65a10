({
    doInit: function(component, event, helper) {
        var getRecordTypeAction = component.get("c.isNewCaseAllowed");
        getRecordTypeAction.setCallback(this, function(response) {
            if (response.getState() === "SUCCESS") {
                var trs = JSON.parse(response.getReturnValue());
                if (trs.isAllowed) {
                    helper.processRecordTypes(component, trs.recordTypes);
                } else {
                    helper.showToast("Attenzione!", "warning", "Non si dispone dei permessi per procedere alla creazione manuale degli Account. Utilizzare le procedure guidate.");
                    helper.closeAction();
                }
            } else {
                helper.handleError(response.getError());
            }
        });
        $A.enqueueAction(getRecordTypeAction);
    },

    selectChange: function(component, event, helper) {
        var selectCmp = component.find("RTList");
        component.set("v.selectedRTId", selectCmp.get("v.value"));
    },

    handleNew: function(component, event, helper) {
        var selectedRTId = component.get("v.selectedRTId");
        var createRecordEvent = $A.get("e.force:createRecord");
        createRecordEvent.setParams({
            "entityApiName": "Case",
            "recordTypeId": selectedRTId
        });
        createRecordEvent.fire();
    },

    closeAction: function(component, event, helper) {
        helper.closeAction();
    }
})
