<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CCgetAccountIdInfoFromCase</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:ClienteVip__c ISBLANK &quot;--&quot; var:Account:ClienteVip__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:ClienteVip__c),&quot;--&quot;,Account:ClienteVip__c)</formulaExpression>
        <formulaResultPath>clienteVipF</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>d9bbf272-9b5a-4151-9db9-d27229f29dd2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>3eb1c8f3-64ed-44ca-b597-86bcd336f350</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Case</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Case</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:VatNumber__c ISBLANK &quot;--&quot; var:Account:VatNumber__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:VatNumber__c),&quot;--&quot;,Account:VatNumber__c)</formulaExpression>
        <formulaResultPath>partitaIvaF</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>8fa6284f-2fac-491e-8f16-e6356e0a69cc</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Email__c ISBLANK &quot;--&quot; var:Account:Email__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Email__c),&quot;--&quot;,Account:Email__c)</formulaExpression>
        <formulaResultPath>emailF</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>89d13144-20bb-4451-aea6-c89384688aed</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>4de32065-4498-44cc-94eb-745def6af39a</globalKey>
        <inputFieldName>Account:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>d1e8c01e-4443-4a87-9b6e-d73af16d7904</globalKey>
        <inputFieldName>Account:PersonBirthdate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>accountBirthDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Street__c ISBLANK | var:AccountDetail:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetail:City__c ISBLANK &amp;&amp; | var:AccountDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetail:Street__c &apos;,/\/\/&apos; + var:AccountDetail:State__c + &apos;,/\/\/&apos; + var:AccountDetail:City__c + &apos;,/\/\/&apos; + var:AccountDetail:PostalCode__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:Street__c) &amp;&amp; ISBLANK(AccountDetail:PostalCode__c) &amp;&amp;ISBLANK(AccountDetail:City__c) &amp;&amp; ISBLANK(AccountDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountDetail:Street__c +&apos;, &apos; +AccountDetail:State__c +&apos;, &apos;
+AccountDetail:City__c +&apos;, &apos;+AccountDetail:PostalCode__c)))</formulaExpression>
        <formulaResultPath>residenzaF</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>1aab363a-e828-4b93-8ab4-138417e7f2a7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Phone ISBLANK &quot;--&quot; var:Account:Phone IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Phone),&quot;--&quot;,Account:Phone)</formulaExpression>
        <formulaResultPath>cellulareF</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>b14462ed-9e12-4bfa-a711-cabddfb948e7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>37bfaeba-72ec-4ade-a7d1-cc67b119066a</globalKey>
        <inputFieldName>Account:VatNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountVat</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Case:AccountId</filterValue>
        <globalKey>e4f11ddd-cb71-434d-946a-73016c14ff38</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:PersonBirthdate ISBLANK &quot;--&quot; var:Account:PersonBirthdate IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:PersonBirthdate),&quot;--&quot;,Account:PersonBirthdate)</formulaExpression>
        <formulaResultPath>dataNascitaF</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>10cdb77e-d684-40cb-869f-a8cc88df92a0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:FinServ__TaxId__pc ISBLANK &quot;--&quot; var:Account:FinServ__TaxId__pc IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:FinServ__TaxId__pc),&quot;--&quot;,Account:FinServ__TaxId__pc)</formulaExpression>
        <formulaResultPath>codiceFiscaleF</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>2d8a37db-b354-421a-935f-55ca22cdc78f</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Street__c ISBLANK | var:AccountDetail:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetail:City__c ISBLANK &amp;&amp; | var:AccountDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetail:Street__c &apos;,/\/\/&apos; + var:AccountDetail:Address__StateCode__s + &apos;,/\/\/&apos; + var:AccountDetail:City__c + &apos;,/\/\/&apos; + var:AccountDetail:PostalCode__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:Street__c) &amp;&amp; ISBLANK(AccountDetail:PostalCode__c) &amp;&amp;ISBLANK(AccountDetail:City__c) &amp;&amp; ISBLANK(AccountDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountDetail:Street__c +&apos;, &apos; +AccountDetail:Address__StateCode__s +&apos;, &apos; +AccountDetail:City__c +&apos;, &apos; +AccountDetail:PostalCode__c)))</formulaExpression>
        <formulaResultPath>indirizzoF</formulaResultPath>
        <formulaSequence>9.0</formulaSequence>
        <globalKey>3ac18303-b120-4899-b0d8-7c85a162e079</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Capacitadispesa__c ISBLANK &quot;--&quot; var:Account:Capacitadispesa__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Capacitadispesa__c),&quot;--&quot;,Account:Capacitadispesa__c)</formulaExpression>
        <formulaResultPath>spesaF</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>d5811b13-f26d-48ea-ac27-0413e543dd62</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCgetAccountIdInfoFromCase</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;recordId&quot; : &quot;5009O00000dSIDHQA4&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>CCgetAccountIdInfoFromCase_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
