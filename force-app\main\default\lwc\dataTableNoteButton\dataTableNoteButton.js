import { LightningElement, api, track, wire } from 'lwc';
import { getDataHand<PERSON> } from 'omnistudio/utility';
import deleteNote from '@salesforce/apex/NoteController.deleteNote';
import updateNote from '@salesforce/apex/NoteController.updateNote';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';
import { NavigationMixin } from 'lightning/navigation';
import getNotesJson from '@salesforce/apex/NoteController.getNotesJson';
import updateNoteCase from '@salesforce/apex/NoteController.updateCaseNote';
import deleteNoteCase from '@salesforce/apex/NoteController.deleteNoteCase';
//import hasPermissionSet from '@salesforce/apex/NoteController.hasPermissionSet';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';


export default class DataTableNoteButton extends NavigationMixin(LightningElement) {
    @api recordId;
    @track noteData = [];
    @track showDeleteModal = false;
    @track showEditModal = false;
    @track showViewModal = false;
    @track showAllNotes = false;

    @track noteToDelete = null;
    @track editedNote = { Title: '', Body: '', recordId: null };
    @track selectedNote = { Title: '', Body: '', recordId: null };
    //@wire(CurrentPageReference) currentPageReference;

    @track showFlow = false;
    @track flowParams = [];

    canCreateNote = false;
    
    @track idForNotes = null;
    @track isCase = false;
    @wire(CurrentPageReference)
    getIdForNotes(currentPageReference) {
        this.isCase = currentPageReference?.attributes?.objectApiName === 'Case';
        if (currentPageReference?.attributes?.recordId) {
            this.idForNotes = currentPageReference.attributes.recordId;
        } else {
            const ws = currentPageReference?.state?.ws;
            if (this.isCase) {
                this.idForNotes = ws?.split('Case/')[1]?.split('/view')[0];
            } else {
                this.idForNotes = ws?.split('Account/')[1]?.split('/view')[0];
            }
        }
    }

    /*@wire(hasPermissionSet)
    wiredPermission({ error, data }) {
        if (data) {
            this.canCreateNote = data;
        } else {
            this.canCreateNote = false;
        }
    }*/

    columns = [
        {
            label: 'Nome',
            fieldName: 'Title',
            type: 'button',
            typeAttributes: {
                label: { fieldName: 'Title' },
                name: 'view',
                variant: 'base'
            }
        },
        { label: 'Contenuto', fieldName: 'Body' },
        { label: 'Data Creazione', fieldName: 'CreatedDateFormatted' },
        {
            label: 'Creato da',
            fieldName: 'createLink',
            type: 'url',
            typeAttributes: { label: { fieldName: 'CreatedBy' }, target: '_blank' }
        },
        { label: 'Data Ultima modifica', fieldName: 'LastModifiedDateFormatted' },
        {
            label: 'Ultima modifica da',
            fieldName: 'lastEditLink',
            type: 'url',
            typeAttributes: { label: { fieldName:'modifiedBy' }, target: '_blank' }
        },
        {
            type: 'action',
            typeAttributes: {
                rowActions: [
                    { label: 'Modifica', name: 'edit' },
                    { label: 'Elimina', name: 'delete' }
                ]
            }
        }
    ];

    connectedCallback() {
        this.loadNotes();
        this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    /*get isCase() {
        return this.currentPageReference?.attributes?.objectApiName === 'Case';
    }

    get testId() {
        let recid;
        if(this.currentPageReference){
            if (this.currentPageReference?.attributes?.recordId) {
                recid = this.currentPageReference.attributes.recordId;
            } else {
                const ws = this.currentPageReference?.state?.ws;
                if (this.isCase) {
                    recid = ws?.split('Case/')[1]?.split('/view')[0];
                } else {
                    recid = ws?.split('Account/')[1]?.split('/view')[0];
                }
            }
        }
        return recid;
    }*/

    get recentNotes() {
        if (!this.noteData) return [];
        return [...this.noteData]
            .sort((a, b) => new Date(b.LastModifiedDateFormatted) - new Date(a.LastModifiedDateFormatted))
            .slice(0, 6);
    }    

    async loadNotes() {
        try {
            const testId = this.idForNotes;
            if (this.isCase) {
                const data = await getNotesJson({ recordId: testId });
                this.processNotesData(data);
            } else {
                const bundleName = 'DMExtractNote';
                const tempObject = { recordId: testId };
                const datasource = JSON.stringify({
                    type: 'dataraptor',
                    value: { bundleName: bundleName, inputMap: tempObject }
                });
                const data = await getDataHandler(datasource);
                this.processNotesData(data);
            }
        } catch (error) {
            console.error('Errore nel caricamento note:', error);
            const errorMessage = error?.body?.message || error?.message || 'Errore generico nel caricamento note.';
            this.showToast('Errore', errorMessage, 'error');
        }
    }

    processNotesData(data) {
        let result;
        try {
            result = JSON.parse(data);
        } catch (parseError) {
            this.showToast('Errore', 'Errore nella lettura della risposta dati.', 'error');
            return;
        }
        const noteRaw = result?.[0]?.Note || result;
        const notes = Array.isArray(noteRaw) ? noteRaw : (noteRaw ? [noteRaw] : []);
        this.noteData = notes.map((note, index) => ({
            id: index,
            Title: note.Title,
            Body: note.Body,
            CreatedBy: note.CreatedBy,
            //modifiedBy: note.LastModifiedBy,
            modifiedById: note.IdModified,
            CreatedDateFormatted: this.formatDate(note.CreatedDate),
            /*LastModifiedDateFormatted: this.formatDate(note.LastModifiedDate),*/
            //LastModifiedDateFormatted: this.isCase ? this.formatDateCase(note.LastModifiedDate) : this.formatDate(note.LastModifiedDate), 
            titleLink: '/' + note.Id,
            createLink: '/' + note.IdCreated,
            lastEditLink: '/' + note.IdModified,
            recordId: note.Id,
            //logica gestita sia per i case che mostra direttamente l'utente che ha modificato la nota, sia per gli account che mostra l'utente che ha creato la nota
            // in caso di modifica, mostra l'utente che ha modificato la nota, altrimenti mostra l'utente che ha creato la nota
            modifiedBy: this.isCase 
                ? note.LastModifiedBy 
                : (note.CreatedDate == note.LastModifiedDate ? '' : note.LastModifiedBy),

            lastEditLink: note.CreatedDate == note.LastModifiedDate ? '' : note.IdModified,
            //LastModifiedDateFormatted: note.LastModifiedDate == note.CreatedDate ? '' : (this.isCase ? this.formatDateCase(note.LastModifiedDate) : this.formatDate(note.LastModifiedDate))
            LastModifiedDateFormatted: this.isCase 
                ? this.formatDateCase(note.LastModifiedDate) 
                : (note.CreatedDate == note.LastModifiedDate ? '' : this.formatDate(note.LastModifiedDate))
        }));
    }
    
    // Formato standard, usato per la datatable
    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('it-IT') + ', ' + date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' });
    }

    // Formato arricchito, usato nelle card di Case
    formatDateCase(dateStr) {
        const date = new Date(dateStr);
        const formattedDate = date.toLocaleDateString('it-IT') + ' ' +
            date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' }) + ', ';
            return `${formattedDate} creata da `;
        }

    handleNewNote() {
        const testId = this.idForNotes;
        this.flowParams = [{ name: 'recordId', type: 'String', value: testId }];
        this.flowName = this.isCase ? 'Create_Note_From_Case' : 'Create_Note_From_Account';
        this.showFlow = true;
    }

    handleFlowStatusChange(event) {
        console.log('Flow event:', event);
        if (event.detail.status === 'FINISHED' || event.detail.status === 'FINISHED_SCREEN') {
            this.showToast('Nota creata', 'La nota è stata aggiunta con successo.', 'success');
            this.showFlow = false; 
            this.loadNotes();
        }
    }

    closeFlowModal() {
        this.showFlow = false;
    }

    handleRowAction(event) {
        const action = event.detail.action.name;
        const row = event.detail.row;
        if (action === 'edit') {
            this.editedNote = { Title: row.Title, Body: row.Body, recordId: row.recordId };
            this.showEditModal = true;
        } else if (action === 'delete') {
            this.noteToDelete = row;
            this.showDeleteModal = true;
        } else if (action === 'view') {
            this.selectedNote = { Title: row.Title, Body: row.Body, recordId: row.recordId };
            this.showViewModal = true;
        }
    }

    handleTitleChange(event) {
        this.editedNote.Title = event.detail.value;
    }

    handleBodyChange(event) {
        this.editedNote.Body = event.detail.value;
    }

    closeEditModal() {
        this.editedNote = { Title: '', Body: '', recordId: null };
        this.showEditModal = false;
    }

    async saveEditedNote() {
        const { recordId, Title, Body } = this.editedNote;
        try {
            if (this.isCase) {
                await updateNoteCase({ noteId: recordId, title: Title, body: Body });
            } else {
                await updateNote({ noteId: recordId, title: Title, body: Body });
            }
            this.showToast('Nota aggiornata', 'La nota è stata modificata con successo.', 'success');
            this.showEditModal = false;
            this.loadNotes();
        } catch (error) {
            console.error('Errore modifica:', error);
            this.showToast('Errore', 'Errore durante la modifica della nota.', 'error');
        }
    }

    cancelDelete() {
        this.noteToDelete = null;
        this.showDeleteModal = false;
    }

    async confirmDelete() {
        try {
            if (!this.noteToDelete) {
                this.showToast('Errore', 'Nessuna nota selezionata per la cancellazione', 'error');
                return;
            }
            const payload = { Id: this.noteToDelete.recordId };
            if (this.isCase) {
                await deleteNoteCase({ recordId: payload.Id });
            } else {
                await deleteNote({ recordId: payload.Id });
            }
            this.showToast('Nota eliminata', `${this.noteToDelete.Title} è stata eliminata.`, 'success');
            this.noteToDelete = null;
            this.showDeleteModal = false;
            this.loadNotes();
        } catch (error) {
            console.error('Errore eliminazione:', error);
            const errorMessage = error.body ? error.body.message : 'Errore durante l\'eliminazione';
            this.showToast('Errore', errorMessage, 'error');
        }
    }    

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    closeViewModal() {
        this.showViewModal = false;
        this.selectedNote = { Title: '', Body: '', recordId: null };
    }
    
    openEditFromView() {
        this.editedNote = { ...this.selectedNote };
        this.showEditModal = true;
        this.showViewModal = false;
    }

    navigateToNoteTablePage() {

        this[NavigationMixin.Navigate]({
            type: 'standard__navItemPage',
            attributes: {
                apiName: 'Nota',
                recordId: this.idForNotes
            }
            /*state: {
                recordId: this.idForNotes
            }*/
        });        
    }

    handleNavigateToUserPage(event) {
        const userId = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: userId,
                actionName: 'view'
            }
        });        
    }

    get caseCardTitle() {
        return `Note (${this.noteData.length})`;
    }
    
    handleViewNote(event) {
        const noteId = event.currentTarget.dataset.id;
        const selected = this.noteData.find(note => note.recordId === noteId);
        if (selected) {
            this.selectedNote = { ...selected };
            this.showViewModal = true;
        }else{
            this.showToast('Errore', 'Nessuna nota selezionata per la visualizzazione', 'error');
        } 
    }

    refreshContainer(refreshPromise) {
        console.log("refreshing of dataTableNoteButton");
        //location.reload(); 
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);
        this.loadNotes();
        

        
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("refresh of dataTableNoteButton Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("refresh of dataTableNoteButton Done, with issues refreshing some components");
            } else if (status === REFRESH_ERROR) {
                console.error("refresh of dataTableNoteButton Major error with refresh.");
            }
        });
    }
}