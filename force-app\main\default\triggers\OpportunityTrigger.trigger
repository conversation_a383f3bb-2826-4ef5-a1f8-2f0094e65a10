/**
 * @File Name         : OpportunityTrigger.trigger
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 05-02-2025
 * @Last Modified By  : <EMAIL>
 **/
trigger OpportunityTrigger on Opportunity(before insert, after insert, after update, before update) {
    OpportunityTriggerHandler oth = new OpportunityTriggerHandler();

    if (SkipTrigger__c.getInstance(UserInfo.getUserId()).SkipOpportunity__c) {
        return;
    }

    if (Trigger.isBefore && Trigger.isInsert) {
        User u = [SELECT Id FROM User WHERE Name = :System.Label.OwnerId];
        for (Opportunity o : Trigger.new) {
            o.OwnerId = u.Id;
            System.debug('>>>> OpportunityTrigger.before.insert: ' + o.OwnerId);
        }
    }

    //  AFTER INSERT
    if (Trigger.isAfter && Trigger.isInsert) {
        /*
        ##########################################################################################################
        ##########################################################################################################
        ####    IMPORTANT ===> to call only if the insert DML is related to the assignement of the records    ####
        ##########################################################################################################
        */
        System.debug('### DEVCAP => INSIDE AFTER INSERT TRG');
        oth.onAfterInsOrUpd(Trigger.new, Trigger.newMap, Trigger.oldMap);
    }

    //  AFTER UPDATE
    if (Trigger.isAfter && Trigger.isUpdate) {
        /*  
        ##########################################################################################################
        ##########################################################################################################
        ####    IMPORTANT ===> to call only if the update DML is related to the assignement of the records    ####
        ##########################################################################################################
        */
        System.debug('### DEVCAP => INSIDE AFTER UPDATE TRG');
        oth.onAfterInsOrUpd(Trigger.new, Trigger.newMap, Trigger.oldMap);
        
        oth.onAfterUpdNotification(Trigger.new, Trigger.oldMap);

    }

    //  BEFORE UPDATE
    if (Trigger.isBefore && Trigger.isUpdate) {
    }
}