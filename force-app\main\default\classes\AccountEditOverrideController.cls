public with sharing class AccountEditOverrideController {
    @AuraEnabled
    public static AccountEditOverrideResult isNewAccountAllowed(Id accountId) {
        AccountEditOverrideResult result = new AccountEditOverrideResult();
        result.isAllowed = false;
        result.recordTypes = new List<RecordTypeInfo>();
        try {
            // Recupera i recordType dell'oggetto Account che iniziano per "ur_"
            Map<String, Schema.RecordTypeInfo> rtMap = Schema.SObjectType.Account.getRecordTypeInfosByName();
            for (Schema.RecordTypeInfo rt : rtMap.values()) {
                if (rt.getName().toLowerCase().startsWith('ur_')) {
                    result.recordTypes.add(new RecordTypeInfo(rt.getRecordTypeId(), rt.getName()));
                }
            }
            // Se almeno uno trovato, consenti
            result.isAllowed = !result.recordTypes.isEmpty();
        } catch (Exception ex) {
            // In caso di errore, lascia isAllowed a false
        }
        return result;
    }

    public class AccountEditOverrideResult {
        @AuraEnabled public Boolean isAllowed;
        @AuraEnabled public List<RecordTypeInfo> recordTypes;
    }
    public class RecordTypeInfo {
        @AuraEnabled public Id recordTypeId;
        @AuraEnabled public String name;
        public RecordTypeInfo(Id id, String name) {
            this.recordTypeId = id;
            this.name = name;
        }
    }
}