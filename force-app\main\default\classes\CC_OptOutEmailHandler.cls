/**
 * @description       : <PERSON><PERSON> per invio email Opt-Out da Flow
 * <AUTHOR> CC
 * @last modified on  : 09-05-2025
**/

public class CC_OptOutEmailHandler {

    public class EmailRequestWrapper {
        @InvocableVariable(label='Case Id')
        public Id caseId;

        @InvocableVariable(label='Flow Origin')
        public String flowOrigin;
    }

    @InvocableMethod(label='Send Opt-Out Email From Flow')
    public static void sendOptOutEmail(List<EmailRequestWrapper> requests) {
        if (requests == null || requests.isEmpty()) return;

        List<Id> caseIds = new List<Id>();
        Map<Id, String> flowOrigins = new Map<Id, String>();

        for (EmailRequestWrapper req : requests) {
            if (req.caseId != null) {
                caseIds.add(req.caseId);
                flowOrigins.put(req.caseId, req.flowOrigin);
            }
        }

        List<Contact> toContacts = [SELECT Id FROM Contact WHERE Email = 'l.le<PERSON><PERSON>@reply.it' LIMIT 1];
        if (toContacts.isEmpty()) {
            System.debug('Contatto dummy non trovato. Invio annullato.');
            return;
        }


        if (caseIds.isEmpty()) return;
        String idEmailConfig;
        List<String> toAddresses = new List<String>();
        try {
            CC_Email_Config__mdt config = [
                SELECT Id, EmailAddress__c
                FROM CC_Email_Config__mdt
                //WHERE OrgId__c = :UserInfo.getOrganizationId()
                LIMIT 1
            ];
            idEmailConfig = config.Id;
            system.debug('Email_Config__mdt prese : '+ config);
            if (String.isNotBlank(config.EmailAddress__c)) {
                for (String addr : config.EmailAddress__c.split('[;,]')) {
                    if (String.isNotBlank(addr)) {
                        toAddresses.add(addr.trim());
                    }
                }
            }
        } catch (Exception e) {
            System.debug('Config email non trovato: ' + e.getMessage());
            return;
        }

        if (toAddresses.isEmpty()) {
            System.debug('Nessun indirizzo email configurato. Invio annullato.');
            return;
        }

        List<Case> cases = [
            SELECT Id, Account.FirstName, Account.LastName, DrCfPivaCliente__c, AccountId,
                   Agency__c, NotesRecontact__c, EmailStatus__c
            FROM Case
            WHERE Id IN :caseIds
        ];
        // idEmailConfig = cases[0].AccountId;
        if (cases.isEmpty()) return;

        Id templateId;
        try {
            templateId = [SELECT Id FROM EmailTemplate WHERE DeveloperName = 'OPT_OUT_Privacy_Template' LIMIT 1].Id;
        } catch (Exception e) {
            System.debug('Template email non trovato: ' + e.getMessage());
            return;
        }

        for (Case c : cases) {
            try {
                system.debug('Invio Email : '+ toAddresses);
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setTargetObjectId(toContacts[0].Id); 
                email.setToAddresses(toAddresses);
                email.setTemplateId(templateId);
                email.setWhatId(c.Id);
                email.setSaveAsActivity(false);
                System.debug('Destinatari finali: ' + JSON.serialize(toAddresses));
                System.debug('Template usato: ' + templateId);
                System.debug('WhatId: ' + c.Id);

                Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ email });

                if (flowOrigins.get(c.Id) == 'OPT OUT') {
                    c.EmailStatus__c  = 'InviatoOptOut';
                } else if (flowOrigins.get(c.Id) == 'Revoca CC') {
                    c.EmailStatus__c  = 'InviatoRevoca';
                }
            } catch (Exception ex) {
                c.EmailStatus__c  = 'Errore';
            }
        }

        update cases;
    }
}
