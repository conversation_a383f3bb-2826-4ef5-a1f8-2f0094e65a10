<template>
    <!-- Case layout -->
    <template if:true={isCase}>
        <lightning-card icon-name="standard:note" title={caseCardTitle}>
            <!-- <PERSON><PERSON><PERSON> per creare nuova nota 
            <template if:true={canCreateNote}>-->
                <lightning-button label="Nuova nota" slot="actions" onclick={handleNewNote}></lightning-button>
            <!--</template>

             Vista compatta -->
            <template if:false={showAllNotes}>
                <div class="slds-card__body slds-p-horizontal_medium">
                    <div class="slds-grid slds-wrap">
                        <template for:each={recentNotes} for:item="note">
                            <div key={note.id} class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3 slds-p-around_x-small">
                                <div class="slds-p-around_medium slds-theme_default">
                                    <div class="slds-text-title_bold slds-truncate">
                                        <a href="javascript:void(0);" class="slds-text-link" onclick={handleViewNote} data-id={note.recordId}>
                                            {note.Title}
                                        </a>
                                    </div>
                                    <div class="slds-text-body_small slds-m-top_xx-small">
                                        {note.LastModifiedDateFormatted}
                                        <a href="javascript:void(0);" class="slds-text-link" onclick={handleNavigateToUserPage} data-id={note.modifiedById}>
                                            {note.modifiedBy}
                                        </a>
                                        <br/>
                                        <lightning-formatted-text value={note.Body}></lightning-formatted-text>
                                    </div>
                                    <!--<div class="slds-m-top_small slds-text-body_regular">
                                        
                                    </div>-->
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Footer con bottone Visualizza Tutto -->
                <div class="slds-card__footer slds-grid slds-grid_align-center" style="padding: 6px 1rem;">
                    <div style="max-width: 400px; width: 100%;">
                        <lightning-button
                            label="Visualizza Tutto"
                            variant="base"
                            onclick={navigateToNoteTablePage}
                            class="slds-button_stretch">
                        </lightning-button>
                    </div>
                </div>
            </template>

            <!-- Vista completa (datatable) -->
            <template if:true={showAllNotes}>
                <div class="slds-card__body slds-p-horizontal_medium">
                    <lightning-datatable
                        key-field="id"
                        data={noteData}
                        columns={columns}
                        onrowaction={handleRowAction}
                        hide-checkbox-column>
                    </lightning-datatable>
                </div>
            </template>
        </lightning-card>
    </template>

    <!-- Layout per Account o altri oggetti -->
    <template if:false={isCase}>
        <lightning-card title="Tutte le note" icon-name="standard:note">
            <lightning-button label="Nuova nota" slot="actions" onclick={handleNewNote}></lightning-button>

            <lightning-datatable
                key-field="id"
                data={noteData}
                columns={columns}
                onrowaction={handleRowAction}
                hide-checkbox-column>
            </lightning-datatable>
        </lightning-card>
    </template>

    <!-- Modale Eliminazione -->
    <template if:true={showDeleteModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Conferma Eliminazione</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium slds-text-align_center">
                    <p>Sei sicuro di voler eliminare la Nota?</p>
                </div>
                <footer class="slds-modal__footer slds-grid slds-grid_align-center slds-p-top_medium">
                    <lightning-button variant="neutral" label="Annulla" onclick={cancelDelete} class="slds-m-right_medium"></lightning-button>
                    <lightning-button variant="brand" label="Conferma" onclick={confirmDelete}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- Modale Modifica -->
    <template if:true={showEditModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Modifica Nota</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-input label="Titolo" value={editedNote.Title} onchange={handleTitleChange}></lightning-input>
                    <lightning-textarea label="Descrizione" value={editedNote.Body} onchange={handleBodyChange}></lightning-textarea>
                </div>
                <footer class="slds-modal__footer slds-grid slds-grid_align-center slds-p-top_medium">
                    <lightning-button variant="neutral" label="Annulla" onclick={closeEditModal} class="slds-m-right_medium"></lightning-button>
                    <lightning-button variant="brand" label="Conferma" onclick={saveEditedNote}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- Modale Flow -->
    <template if:true={showFlow}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Nuova Nota</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={closeFlowModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div style="background-color: #f2f2f2; padding: 6px 10px; border-radius: 3px; font-weight: 500; font-size: 14px; margin-bottom: 8px;">
                        Informazioni
                    </div>
                    <lightning-flow flow-api-name={flowName} flow-input-variables={flowParams} onstatuschange={handleFlowStatusChange}></lightning-flow>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- Modale Visualizza -->
    <template if:true={showViewModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Visualizza Nota</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-input label="Titolo" value={selectedNote.Title} disabled class="slds-m-bottom_medium"></lightning-input>
                    <lightning-textarea label="Descrizione" value={selectedNote.Body} disabled></lightning-textarea>
                </div>
                <footer class="slds-modal__footer slds-grid slds-grid_align-center slds-p-top_medium">
                    <lightning-button variant="neutral" label="Annulla" onclick={closeViewModal} class="slds-m-right_medium"></lightning-button>
                    <lightning-button variant="brand" label="Modifica" onclick={openEditFromView}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>