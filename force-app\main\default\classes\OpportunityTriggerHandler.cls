/**
 * @File Name         : OpportunityTriggerHandler.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 10-02-2025
 * @Last Modified By  : <EMAIL>
 * @cicd_tests TestOpportunityTriggerHandler
 **/
public without sharing class OpportunityTriggerHandler {
    /*  
    ##########################################################################################################
    ##########################################################################################################
    ####    IMPORTANT ===> NEW CODE FOR CONI VISIBILITA                                                   ####
    ##########################################################################################################
    */

    public void onAfterInsOrUpd(List<Opportunity> newList, Map<Id, Opportunity> newMap, Map<Id, Opportunity> oldMap) {
        System.debug('### DEVCAP => INSIDE OpportunityTriggerHandler BEFORE performShares');
        System.debug('*** OpportunityCount => ' + newList.size());
        if (Trigger.isInsert) {
            performShares(newList, newMap, oldMap);
        } else {
            List<Opportunity> listCorrect = new List<Opportunity>();
            for (Opportunity opp : newList) {
                if (
                    opp.StageName != oldMap.get(opp.Id).StageName ||
                    opp.Agency__c != oldMap.get(opp.Id).Agency__c ||
                    opp.AssignedGroup__c != oldMap.get(opp.Id).AssignedGroup__c ||
                    opp.AssignedTo__c != oldMap.get(opp.Id).AssignedTo__c
                ) {
                    listCorrect.add(opp);
                }
            }
            if (!listCorrect.isEmpty()) {
                performShares(listCorrect, new Map<Id, Opportunity>(listCorrect), oldMap);
                //NO IN UAT - START
                //ConiAssignmentHelper.checkOtherShare(setIdAssigned, oldSetIdAssigned);
                //NO IN UAT - END
            }
        }
    }

    public void onAfterUpdNotification(List<Opportunity> newList, Map<Id, Opportunity> oldMap) {
        System.debug('### DEVCAP => Start Method for send Notification');
        
        List<Opportunity> listUser = new List<Opportunity>();
        Map<Opportunity,Id> MapGroupId = new  Map<Opportunity,Id>();

        Map<Id,List<Id>> MapUserId = new  Map<Id,List<Id>>();

        for (Opportunity opp : newList) {
            System.debug('### DEVCAP => Start Method opp.AssignedTo__c' + opp.AssignedTo__c);
            System.debug('### DEVCAP => Start Method oldMap.get(opp.Id).AssignedGroup__c' + oldMap.get(opp.Id).AssignedTo__c);
            if (opp.DomainType__c != null) {continue;}

            if (
                (opp.AssignedTo__c != oldMap.get(opp.Id).AssignedTo__c) && oldMap.get(opp.Id).AssignedTo__c != null) {
                listUser.add(opp);
            }else if (
                (opp.AssignedGroup__c != oldMap.get(opp.Id).AssignedGroup__c) && oldMap.get(opp.Id).AssignedGroup__c != null) {
                MapGroupId.put(opp,opp.AssignedGroup__c);
            }
        }
        
        if(MapGroupId.size() > 0){
            List<GroupMember> takeUserGroup = [SELECT GroupId, UserOrGroup.Id FROM GroupMember 
                                   WHERE GroupId IN :MapGroupId.values() 
                                   AND UserOrGroup.IsActive = true];


            if(takeUserGroup.size() > 0){
                for(GroupMember singleGroupMember: takeUserGroup){

                    if(MapUserId.containsKey(singleGroupMember.GroupId)){
                        MapUserId.get(singleGroupMember.GroupId).add(singleGroupMember.UserOrGroup.Id);
                    }else{
                        MapUserId.put(singleGroupMember.GroupId,new List<Id>{singleGroupMember.UserOrGroup.Id});
                    }
                }
            }
        }

        if(listUser.size () > 0 || MapGroupId.size() > 0){
            NotificationHandler.opportunityNotification(listUser,MapGroupId,MapUserId, 'Reassignment');
        }
        System.debug('### DEVCAP => End Method for send Notification');
    }

    public static void performShares(List<Opportunity> newListLst, Map<Id, Opportunity> newListMap, Map<Id, Opportunity> oldOppsMap) {
        /* if (ConiQueRefUpdate.isQueueableUpdate) {
            System.debug('>>>> performShares in queuable');
            return;
        }

        if (Trigger.isBefore && Trigger.isInsert) {
            System.debug('>>>> trigger before insert');
            return;
        } */

        Set<Id> setOppsAgencies = new Set<Id>();
        List<FinServ__AccountAccountRelation__Share> lstAccAccRelShare = new List<FinServ__AccountAccountRelation__Share>();

        Map<Id, Set<Id>> mapAux = new Map<Id, Set<Id>>();
        System.debug('### DEVCAP => newListLst = ' + newListLst);
        if (!newListLst.isEmpty()) {
            for (Opportunity opp : newListLst) {
                setOppsAgencies.add(opp.Agency__c);
                if (!mapAux.containsKey(opp.AccountId)) {
                    mapAux.put(opp.AccountId, new Set<Id>{ opp.Agency__c });
                } else {
                    Set<Id> aux = mapAux.get(opp.AccountId);
                    aux.add(opp.Agency__c);
                    mapAux.put(opp.AccountId, aux);
                }
            }
        }
        System.debug('### DEVCAP => mapAux = ' + mapAux);
        System.debug('### DEVCAP => setOppsAgencies = ' + setOppsAgencies);

        List<FinServ__AccountAccountRelation__c> lstAccAccRel = new List<FinServ__AccountAccountRelation__c>();
        lstAccAccRel = [
            SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c IN :mapAux.keySet() AND FinServ__RelatedAccount__c IN :setOppsAgencies
        ];

        System.debug('### DEVCAP => lstAccAccRel = ' + lstAccAccRel);

        Map<Id, Map<Id, FinServ__AccountAccountRelation__c>> mapAccAccRel = new Map<Id, Map<Id, FinServ__AccountAccountRelation__c>>();
        for (FinServ__AccountAccountRelation__c accAccRel : lstAccAccRel) {
            if (!mapAccAccRel.containsKey(accAccRel.FinServ__Account__c)) {
                mapAccAccRel.put(accAccRel.FinServ__Account__c, new Map<Id, FinServ__AccountAccountRelation__c>{ accAccRel.FinServ__RelatedAccount__c => accAccRel });
            } else {
                Map<Id, FinServ__AccountAccountRelation__c> aux = mapAccAccRel.get(accAccRel.FinServ__Account__c);
                aux.put(accAccRel.FinServ__RelatedAccount__c, accAccRel);
                mapAccAccRel.put(accAccRel.FinServ__Account__c, aux);
            }
        }

        System.debug('### DEVCAP => mapAccAccRel = ' + mapAccAccRel);

        if (!lstAccAccRel.isEmpty()) {
            for (Opportunity opp : newListLst) {
                System.debug('### DEVCAP => opp.AccountId = ' + opp.AccountId);
                System.debug('### DEVCAP => opp.Agency__c = ' + opp.Agency__c);
                if (mapAux.containsKey(opp.AccountId) && mapAux.get(opp.AccountId).contains(opp.Agency__c)) {
                    if (mapAccAccRel.containsKey(opp.AccountId)) {
                        Map<Id, FinServ__AccountAccountRelation__c> aux = mapAccAccRel.get(opp.AccountId);
                        System.debug('### DEVCAP => aux = ' + aux);
                        if (aux.containsKey(opp.Agency__c)) {
                            FinServ__AccountAccountRelation__c accAccRel = aux.get(opp.Agency__c);
                            FinServ__AccountAccountRelation__Share newAccAccRelShare = new FinServ__AccountAccountRelation__Share();
                            newAccAccRelShare.ParentId = accAccRel.Id;
                            newAccAccRelShare.AccessLevel = 'Read';

                            if (opp.AssignedTo__c != null) {
                                newAccAccRelShare.UserOrGroupId = opp.AssignedTo__c;
                            } else if (opp.AssignedGroup__c != null) {
                                newAccAccRelShare.UserOrGroupId = opp.AssigneeGroupId__c;
                            }
                            lstAccAccRelShare.add(newAccAccRelShare);
                        }
                    }
                }
            }
        }
        try {
            System.debug('### DEVCAP => lstAccAccRelShare = ' + lstAccAccRelShare);
            insert lstAccAccRelShare;
        } catch (Exception e) {
            System.debug('### DEVCAP => OpportunityTriggerHandler: error in insert FinServ__AccountAccountRelation__Share records => ' + e.getMessage());
        }

        System.debug('### DEVCAP => INSIDE OpportunityTriggerHandler performShares');
        ConiAssignmentService.performShares(newListLst, newListMap, oldOppsMap);
    }
}