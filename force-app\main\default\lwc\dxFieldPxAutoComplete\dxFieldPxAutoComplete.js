import { errors } from 'c/dxErrorMessage';
import { setFieldValue, markFieldTouched } from 'c/dxFormState';
import { fireEvent, registerListener, unregisterListener } from 'c/pubsub';
import { utils } from 'c/dxUtils';
import { LightningElement, api, track } from 'lwc';

export default class FieldPxAutoComplete extends LightningElement {
  _field;
  @track opened = false;
  @track isValid = true;
  @track noResults = false;
  visualizedValue = '';
  visualizedImage = '';
  lastSelectedValue = '';
  @api decodedValue;
  @api parentLayout;
  @api debug;
  @api disabled;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    const selectedOption = value.control.modes[0].options.find((opt) => opt.key === value.value);
    this.visualizedValue = selectedOption ? selectedOption?.value : '';
    this.lastSelectedValue = this.visualizedValue;

    setFieldValue(
      value.reference,
      value.value,
      value?.customAttributes?.validation || 'text',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get placeholder() {
    return this.field?.control?.modes?.[0]?.placeholder || '';
  }

  get componentClass() {
    return `pxAutoComplete ${this.debug ? 'debug' : ''}`.trim();
  }

  get isReadonly() {
    return this.field.readOnly === true;
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get selectedImage() {
    return `background-image: url("https://unipolsai.it/NextAssets/interprete-pu/immagini-pet/${this.visualizedImage}.png"`;
  }

  get options() {
    const allOptions = this.field?.control?.modes[0]?.options || [];
    const filteredOptions = this.visualizedValue
      ? allOptions.filter((opt) =>
        opt.value.toLowerCase().includes(this.visualizedValue.toLowerCase())
      )
      : allOptions;

    const highlights = this.field?.customAttributes?.highlights;
    if (!highlights) {
      return filteredOptions.map((opt) => ({
        label: opt.value,
        value: opt.key,
        tooltip: opt.tooltip
          ? `background-image: url("https://unipolsai.it/NextAssets/interprete-pu/immagini-pet/${opt.tooltip}.png"`
          : undefined,
        optionClass: '',
        isHighlighted: false
      })) || [];
    }
    const highlightKeys = highlights.split('|').map(key => key.trim());
    const highlightedOptions = [];
    const normalOptions = [];
    
    filteredOptions.forEach(opt => {
      if (highlightKeys.includes(opt.key)) {
        highlightedOptions.push({
          label: opt.value,
          value: opt.key,
          tooltip: opt.tooltip
            ? `background-image: url("https://unipolsai.it/NextAssets/interprete-pu/immagini-pet/${opt.tooltip}.png"`
            : undefined,
          optionClass: 'select-highlight-options',
          isHighlighted: true
        });
      } else {
        normalOptions.push({
          label: opt.value,
          value: opt.key,
          tooltip: opt.tooltip
            ? `background-image: url("https://unipolsai.it/NextAssets/interprete-pu/immagini-pet/${opt.tooltip}.png"`
            : undefined,
          optionClass: '',
          isHighlighted: false
        });
      }
    });
    return [...highlightedOptions, ...normalOptions];
  }

  get autoCompleteClasses() {
    return `tpd_selectAngular ${this.disabled ? 'isDisabled disabled-input-box' : ''} `;
  }

  get listOptionsClasses() {
    return `select-options withLabel ${this.opened ? 'opened' : ''} `;
  }

  get listClasses() {
    return `select-styled ${this.disabled ? 'isDisabled disabled-input-box' : ''}`;
  }

  get errorMessage() {
    if (this.noResults) {
      return errors['noResults'];
    }
    if (!this.isValid) {
      return errors['required'];
    }
    return '';
  }

  dropdownClick() {
    const container = this.template.querySelector('.select-styled');
    this.opened = true;
    container.classList.add('active');
  }

  fieldTouched() {
    this.opened = !this.opened;
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('.select-styled');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  handleInputChange(evt) {
    if (this.field.readOnly || this.field.disabled) return;
    const newValue = evt.target.value;
    this.visualizedValue = newValue;

    const allOptions = this.field?.control?.modes[0]?.options || [];
    const exactMatch = allOptions.find(opt => 
      opt.value.toLowerCase() === newValue.toLowerCase()
    );
    this.noResults = newValue && !exactMatch;
    const selectedOption = this.options.find(
      (opt) => opt.label.toLowerCase() === newValue.toLowerCase()
    );

    if (selectedOption) {
      const syntheticEvent = {
        target: {
          dataset: {
            reference: this.field.reference,
          },
          value: selectedOption.value,
        },
      };

      fireEvent('handleFieldChanged', {
        evt: syntheticEvent,
        field: this.field,
        parentLayout: this.parentLayout,
      });
    }
    
    this.validateField();
  }

  handleOptionSelect(evt) {
    const selectedKey = evt.currentTarget.dataset.key;
    const selectedOption = this.options.find((opt) => opt.value === selectedKey);

    if (selectedOption) {
      this.visualizedValue = selectedOption.label;
      this.visualizedImage = selectedOption?.tooltip;
      this.opened = false;
      this.noResults = false; 

      const changeEvent = {
        target: {
          value: selectedOption.label,
        },
      };
      this.handleInputChange(changeEvent);
    }
  }

  handleInputBlur() {
    if (this.visualizedValue === '' || this.noResults) {
      this.visualizedValue = this.lastSelectedValue;

      const allOptions = this.field?.control?.modes[0]?.options || [];
      const selectedOption = allOptions.find(opt =>
        opt.value.toLowerCase() === this.lastSelectedValue.toLowerCase()
      );

      if (selectedOption) {
        setFieldValue(
          this.field.reference,
          selectedOption.key,
          this.field?.customAttributes?.validation || 'text',
          this.field.required || this.field?.customAttributes?.required
        );
      }
    }
    this.validateField();
    const container = this.template.querySelector('.select-styled');
    this.opened = false;
    container.classList.remove('active');
  }

  handleInputFocus() {
    this.opened = true;
    const container = this.template.querySelector('.select-styled');
    if (container) {
      container.classList.add('active');
    }
  }

  validateField() {
    const container = this.template.querySelector('.select-styled');
    if (!container) {
      return;
    }

    if (container.classList.contains('disabled-input-box')) {
      return;
    }

    if (this.field.required && !this.visualizedValue) {
      container.classList.add('invalid-input');
      this.isValid = false;
      this.noResults = false; 
    } else if (this.noResults) {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  handleExternalValidation() {
    try {
      this.validateField();
    } catch (error) {
    }
  }

  connectedCallback() {
    registerListener('triggerValidation', this.handleExternalValidation, this);
  }

  disconnectedCallback() {
    unregisterListener('triggerValidation', this.handleExternalValidation, this);
    this._isRegistered = false;
  }

  renderedCallback() {
    if (this._isRegistered) return;

    this._isRegistered = true;
    registerListener('triggerValidation', this.handleExternalValidation, this);
  }
}